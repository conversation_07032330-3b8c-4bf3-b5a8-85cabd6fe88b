package test;

import dao.BaseDao;
import pojo.Message;
import utils.EmailUtils;
import utils.SmsUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * Utility class per testare l'invio di email e SMS tramite Twilio e SendGrid
 * Contiene funzioni statiche per l'uso in una webapp
 *
 * CONFIGURAZIONE RICHIESTA:
 *
 * Variabili d'ambiente per SendGrid (Email):
 * - SENDGRID_API_KEY: La tua API key di SendGrid
 * - DEFAULT_FROM_EMAIL: Email mittente di default (deve essere verificata in SendGrid)
 * - DEFAULT_FROM_NAME: Nome mittente di default
 *
 * Variabili d'ambiente per Twilio (SMS):
 * - TWILIO_ACCOUNT_SID: Il tuo Account SID di Twilio
 * - TWILIO_AUTH_TOKEN: Il tuo Auth Token di Twilio
 * - TWILIO_PHONE_NUMBER: Il tuo numero di telefono Twilio (formato: +**********)
 */
public class MessageTester {

    private static final Logger LOGGER = LoggerFactory.getLogger(MessageTester.class.getName());

    /**
     * Risultato di un test
     */
    public static class TestResult {
        private boolean success;
        private String message;
        private String messageId;
        private Exception error;

        public TestResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public TestResult(boolean success, String message, String messageId) {
            this.success = success;
            this.message = message;
            this.messageId = messageId;
        }

        public TestResult(boolean success, String message, Exception error) {
            this.success = success;
            this.message = message;
            this.error = error;
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public String getMessageId() { return messageId; }
        public Exception getError() { return error; }
    }
    
    /**
     * Verifica la configurazione delle variabili d'ambiente
     */
    public static Map<String, Object> checkConfiguration() {
        Map<String, Object> config = new HashMap<>();

        // SendGrid
        boolean sendGridConfigured = EmailUtils.isConfigured();
        config.put("sendGridConfigured", sendGridConfigured);

        // Twilio
        boolean twilioConfigured = SmsUtils.isConfigured();
        config.put("twilioConfigured", twilioConfigured);

        config.put("overallConfigured", sendGridConfigured || twilioConfigured);

        LOGGER.info("Configurazione verificata - SendGrid: {}, Twilio: {}",
            sendGridConfigured, twilioConfigured);

        return config;
    }
    
    /**
     * Test invio email
     */
    public static TestResult testEmail(String to, String toName, String subject, String body, String htmlBody) {
        if (!EmailUtils.isConfigured()) {
            return new TestResult(false, "SendGrid non configurato. Impossibile inviare email.");
        }

        if (to == null || to.trim().isEmpty()) {
            return new TestResult(false, "Email destinatario obbligatoria!");
        }

        try {
            // Valori di default
            if (subject == null || subject.trim().isEmpty()) {
                subject = "Test Email da MessageTester";
            }
            if (body == null || body.trim().isEmpty()) {
                body = "Questo è un messaggio di test inviato tramite SendGrid.";
            }

            // Crea oggetto Message
            Message message = new Message(Message.MessageType.EMAIL.name(), to.trim(), null, body);
            message.setToName(toName != null && !toName.trim().isEmpty() ? toName.trim() : null);
            message.setSubject(subject);
            message.setHtmlBody(htmlBody != null && !htmlBody.trim().isEmpty() ? htmlBody : null);

            // Invia email
            String messageId = EmailUtils.sendMessage(message);

            if (messageId != null) {
                message.markAsSent(messageId);

                // Salva nel database
                try {
                    BaseDao.insertDocument(message);
                    LOGGER.info("Email inviata e salvata nel database. ID: {}", messageId);
                    return new TestResult(true, "Email inviata con successo e salvata nel database", messageId);
                } catch (Exception e) {
                    LOGGER.warn("Email inviata ma errore nel salvataggio database: {}", e.getMessage());
                    return new TestResult(true, "Email inviata con successo ma errore nel salvataggio database: " + e.getMessage(), messageId);
                }

            } else {
                message.markAsFailed("Errore nell'invio");
                return new TestResult(false, "Errore nell'invio email");
            }

        } catch (Exception e) {
            LOGGER.error("Errore nel test email: {}", e.getMessage(), e);
            return new TestResult(false, "Errore: " + e.getMessage(), e);
        }
    }

    /**
     * Test invio email semplice con parametri minimi
     */
    public static TestResult testSimpleEmail(String to, String subject, String body) {
        return testEmail(to, null, subject, body, null);
    }
    
    /**
     * Test invio SMS
     */
    public static TestResult testSms(String to, String body) {
        if (!SmsUtils.isConfigured()) {
            return new TestResult(false, "Twilio non configurato. Impossibile inviare SMS.");
        }

        if (to == null || to.trim().isEmpty()) {
            return new TestResult(false, "Numero destinatario obbligatorio!");
        }

        try {
            // Valida e formatta il numero
            String formattedTo = SmsUtils.formatPhoneNumber(to.trim());
            if (!SmsUtils.isValidPhoneNumber(formattedTo)) {
                LOGGER.warn("Il numero {} potrebbe non essere valido. Continuo comunque...", formattedTo);
            }

            // Valore di default per il body
            if (body == null || body.trim().isEmpty()) {
                body = "Questo è un messaggio di test inviato tramite Twilio.";
            }

            // Crea oggetto Message
            Message message = new Message(Message.MessageType.SMS.name(), formattedTo, null, body);

            // Invia SMS
            String messageSid = SmsUtils.sendMessage(message, "https://apdbh-158-47-225-145.a.free.pinggy.link/tigani/api/message/twilio/callback");

            if (messageSid != null) {
                message.markAsSent(messageSid);

                // Salva nel database
                try {
                    BaseDao.insertDocument(message);
                    LOGGER.info("SMS inviato e salvato nel database. SID: {}", messageSid);

                    // Verifica stato dopo qualche secondo (opzionale)
                    try {
                        Thread.sleep(2000);
                        String status = SmsUtils.getMessageStatus(messageSid);
                        LOGGER.info("Stato SMS {}: {}", messageSid, status);
                        return new TestResult(true, "SMS inviato con successo e salvato nel database. Stato: " + status, messageSid);
                    } catch (InterruptedException e) {
                        return new TestResult(true, "SMS inviato con successo e salvato nel database", messageSid);
                    }

                } catch (Exception e) {
                    LOGGER.warn("SMS inviato ma errore nel salvataggio database: {}", e.getMessage());
                    return new TestResult(true, "SMS inviato con successo ma errore nel salvataggio database: " + e.getMessage(), messageSid);
                }

            } else {
                message.markAsFailed("Errore nell'invio");
                return new TestResult(false, "Errore nell'invio SMS");
            }

        } catch (Exception e) {
            LOGGER.error("Errore nel test SMS: {}", e.getMessage(), e);
            return new TestResult(false, "Errore: " + e.getMessage(), e);
        }
    }
    
    /**
     * Test connessione SendGrid
     */
    public static TestResult testSendGridConnection() {
        if (!EmailUtils.isConfigured()) {
            return new TestResult(false, "SendGrid non configurato");
        }

        try {
            boolean connected = EmailUtils.testConnection();

            if (connected) {
                LOGGER.info("Test connessione SendGrid riuscito");
                return new TestResult(true, "Connessione SendGrid riuscita!");
            } else {
                LOGGER.warn("Test connessione SendGrid fallito");
                return new TestResult(false, "Errore nella connessione SendGrid");
            }
        } catch (Exception e) {
            LOGGER.error("Errore nel test connessione SendGrid: {}", e.getMessage(), e);
            return new TestResult(false, "Errore nel test connessione: " + e.getMessage(), e);
        }
    }

    /**
     * Test connessione Twilio
     */
    public static TestResult testTwilioConnection() {
        if (!SmsUtils.isConfigured()) {
            return new TestResult(false, "Twilio non configurato");
        }

        try {
            boolean connected = SmsUtils.testConnection();

            if (connected) {
                LOGGER.info("Test connessione Twilio riuscito");
                return new TestResult(true, "Connessione Twilio riuscita!");
            } else {
                LOGGER.warn("Test connessione Twilio fallito");
                return new TestResult(false, "Errore nella connessione Twilio");
            }
        } catch (Exception e) {
            LOGGER.error("Errore nel test connessione Twilio: {}", e.getMessage(), e);
            return new TestResult(false, "Errore nel test connessione: " + e.getMessage(), e);
        }
    }

    /**
     * Ottiene la configurazione attuale
     */
    public static Map<String, Object> getConfiguration() {
        Map<String, Object> config = new HashMap<>();

        // SendGrid
        Map<String, Object> sendGrid = new HashMap<>();
        sendGrid.put("apiKeyConfigured", System.getenv("SENDGRID_API_KEY") != null);
        sendGrid.put("fromEmail", System.getenv("DEFAULT_FROM_EMAIL"));
        sendGrid.put("fromName", System.getenv("DEFAULT_FROM_NAME"));
        sendGrid.put("webhookUrl", System.getenv("SENDGRID_WEBHOOK_URL"));
        sendGrid.put("configured", EmailUtils.isConfigured());
        sendGrid.put("trackingConfigured", EmailUtils.isTrackingConfigured());

        // Aggiungi informazioni dettagliate sul tracking
        sendGrid.put("tracking", EmailUtils.getTrackingInfo());

        config.put("sendGrid", sendGrid);

        // Twilio
        Map<String, Object> twilio = new HashMap<>();
        twilio.put("accountSidConfigured", System.getenv("TWILIO_ACCOUNT_SID") != null);
        twilio.put("authTokenConfigured", System.getenv("TWILIO_AUTH_TOKEN") != null);
        twilio.put("phoneNumber", System.getenv("TWILIO_PHONE_NUMBER"));
        twilio.put("configured", SmsUtils.isConfigured());
        config.put("twilio", twilio);

        return config;
    }

    /**
     * Esegue tutti i test di connessione
     */
    public static Map<String, TestResult> runAllConnectionTests() {
        Map<String, TestResult> results = new HashMap<>();

        results.put("sendGrid", testSendGridConnection());
        results.put("twilio", testTwilioConnection());

        return results;
    }
}
