package extensions;

import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import dao.BaseDao;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class CategoryListFunction implements Function {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("table");
        names.add("language");
        names.add("identifier"); // aggiunto argomento opzionale
        return names;
    }

    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        String table = (String) args.get("table");
        String language = (String) args.get("language");

        if (StringUtils.isBlank(language)) {
            language = (String) context.getVariable("language");
        }

        Object identifierObj = args.get("identifier");
        boolean useIdentifier = false;

        if (identifierObj != null) {
            if (identifierObj instanceof Boolean) {
                useIdentifier = (Boolean) identifierObj;
            } else if (identifierObj instanceof String) {
                useIdentifier = Boolean.parseBoolean((String) identifierObj); // "true"/"false"
            }
        }

        if (useIdentifier) {
            return loadCategoryMap(table, language);
        } else {
            return loadCategoryList(table, language);
        }
    }
    private List<String> loadCategoryList(String table, String language) {
        List<String> items = null;
        try {
            items = BaseDao.loadCategoryList(Class.forName("pojo." + table), language);
            Collections.sort(items);
        } catch (Exception ex) {
            logger.error("suppressed", ex);
        }
        return items;
    }

    private Map<String, String> loadCategoryMap(String table, String language) {
        Map<String, String> map = new HashMap<>();
        try {
            // Qui dovrai implementare il metodo in BaseDao per ottenere la mappa.
            // Presumo un metodo simile a loadCategoryList ma che ritorni Map<identifier, label>
            map = BaseDao.loadCategoryMap(Class.forName("pojo." + table), language);
        } catch (Exception ex) {
            logger.error("suppressed", ex);
        }
        return map;
    }
}