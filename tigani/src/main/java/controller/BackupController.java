package controller;

import core.Core;
import dao.BaseDaoBackup;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.BackupDocument;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.Route;
import utils.BackupUtils;
import utils.RequestUtils;

/**
 * Controller for backup-related operations and API endpoints
 * 
 * <AUTHOR>
 */
public class BackupController {

    private static final Logger LOGGER = LoggerFactory.getLogger(BackupController.class.getName());

    /**
     * Get backup history for a specific document
     * URL: /api/backup/history/:collection/:id
     */
    public static Route getDocumentHistory = (Request request, Response response) -> {
        response.type("application/json");
        
        try {
            String collectionName = request.params(":collection");
            String documentId = request.params(":id");
            
            if (StringUtils.isBlank(collectionName) || StringUtils.isBlank(documentId)) {
                response.status(400);
                return Core.serializeToJson(Map.of("error", "Collection name and document ID are required"));
            }
            
            ObjectId realId = RequestUtils.toObjectId(documentId);
            if (realId == null) {
                response.status(400);
                return Core.serializeToJson(Map.of("error", "Invalid document ID format"));
            }
            
            List<BackupDocument> history = BackupUtils.getDocumentHistory(realId, collectionName);
            
            Map<String, Object> result = new HashMap<>();
            result.put("collectionName", collectionName);
            result.put("documentId", documentId);
            result.put("backupCount", history.size());
            result.put("history", history);
            
            return Core.serializeToJson(result);
            
        } catch (Exception ex) {
            LOGGER.error("Error retrieving document history", ex);
            response.status(500);
            return Core.serializeToJson(Map.of("error", "Internal server error"));
        }
    };

    /**
     * Get latest backup for a specific document
     * URL: /api/backup/latest/:collection/:id
     */
    public static Route getLatestBackup = (Request request, Response response) -> {
        response.type("application/json");
        
        try {
            String collectionName = request.params(":collection");
            String documentId = request.params(":id");
            
            if (StringUtils.isBlank(collectionName) || StringUtils.isBlank(documentId)) {
                response.status(400);
                return Core.serializeToJson(Map.of("error", "Collection name and document ID are required"));
            }
            
            ObjectId realId = RequestUtils.toObjectId(documentId);
            if (realId == null) {
                response.status(400);
                return Core.serializeToJson(Map.of("error", "Invalid document ID format"));
            }
            
            BackupDocument latest = BackupUtils.getLatestDocumentBackup(realId, collectionName);
            
            if (latest == null) {
                response.status(404);
                return Core.serializeToJson(Map.of("error", "No backup found for this document"));
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("collectionName", collectionName);
            result.put("documentId", documentId);
            result.put("backup", latest);
            result.put("changesSummary", BackupUtils.getChangesSummary(latest));
            
            return Core.serializeToJson(result);
            
        } catch (Exception ex) {
            LOGGER.error("Error retrieving latest backup", ex);
            response.status(500);
            return Core.serializeToJson(Map.of("error", "Internal server error"));
        }
    };

    /**
     * Get recent changes for a collection
     * URL: /api/backup/recent/:collection?limit=10
     */
    public static Route getRecentChanges = (Request request, Response response) -> {
        response.type("application/json");
        
        try {
            String collectionName = request.params(":collection");
            String limitParam = request.queryParams("limit");
            
            if (StringUtils.isBlank(collectionName)) {
                response.status(400);
                return Core.serializeToJson(Map.of("error", "Collection name is required"));
            }
            
            int limit = 10; // default
            if (StringUtils.isNotBlank(limitParam)) {
                try {
                    limit = Integer.parseInt(limitParam);
                    if (limit <= 0 || limit > 100) {
                        limit = 10;
                    }
                } catch (NumberFormatException ex) {
                    limit = 10;
                }
            }
            
            List<BackupDocument> recentChanges = BackupUtils.getRecentChanges(collectionName, limit);
            
            Map<String, Object> result = new HashMap<>();
            result.put("collectionName", collectionName);
            result.put("limit", limit);
            result.put("changesCount", recentChanges.size());
            result.put("changes", recentChanges);
            
            return Core.serializeToJson(result);
            
        } catch (Exception ex) {
            LOGGER.error("Error retrieving recent changes", ex);
            response.status(500);
            return Core.serializeToJson(Map.of("error", "Internal server error"));
        }
    };

    /**
     * Get backup monitoring status
     * URL: /api/backup/status
     */
    public static Route getBackupStatus = (Request request, Response response) -> {
        response.type("application/json");
        
        try {
            Map<String, Object> status = new HashMap<>();
            status.put("enabled", utils.Defaults.ENABLE_CHANGE_STREAMS_BACKUP);
            status.put("running", service.ChangeStreamBackupService.isRunning());
            status.put("monitoredCollections", BackupUtils.getMonitoredCollections());
            status.put("backupCollectionName", BackupUtils.getBackupCollectionName());
            status.put("statusText", BackupUtils.getBackupStatus());
            
            return Core.serializeToJson(status);
            
        } catch (Exception ex) {
            LOGGER.error("Error retrieving backup status", ex);
            response.status(500);
            return Core.serializeToJson(Map.of("error", "Internal server error"));
        }
    };

    /**
     * Restart backup monitoring (admin only)
     * URL: POST /api/backup/restart
     */
    public static Route restartBackupMonitoring = (Request request, Response response) -> {
        response.type("application/json");
        
        try {
            // Check if user is authenticated and has admin privileges
            User user = Core.getUserFromRequest(request);
            if (user == null) {
                response.status(401);
                return Core.serializeToJson(Map.of("error", "Authentication required"));
            }
            
            // Add admin check here if needed
            // if (!user.isAdmin()) { ... }
            
            BackupUtils.restartBackupMonitoring();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "Backup monitoring restarted successfully");
            result.put("status", BackupUtils.getBackupStatus());
            
            return Core.serializeToJson(result);
            
        } catch (Exception ex) {
            LOGGER.error("Error restarting backup monitoring", ex);
            response.status(500);
            return Core.serializeToJson(Map.of("error", "Internal server error"));
        }
    };

    /**
     * Get backup count for a specific document
     * URL: /api/backup/count/:collection/:id
     */
    public static Route getBackupCount = (Request request, Response response) -> {
        response.type("application/json");
        
        try {
            String collectionName = request.params(":collection");
            String documentId = request.params(":id");
            
            if (StringUtils.isBlank(collectionName) || StringUtils.isBlank(documentId)) {
                response.status(400);
                return Core.serializeToJson(Map.of("error", "Collection name and document ID are required"));
            }
            
            ObjectId realId = RequestUtils.toObjectId(documentId);
            if (realId == null) {
                response.status(400);
                return Core.serializeToJson(Map.of("error", "Invalid document ID format"));
            }
            
            long count = BackupUtils.getDocumentBackupCount(realId, collectionName);
            
            Map<String, Object> result = new HashMap<>();
            result.put("collectionName", collectionName);
            result.put("documentId", documentId);
            result.put("backupCount", count);
            result.put("isMonitored", BackupUtils.isCollectionMonitored(collectionName));
            
            return Core.serializeToJson(result);
            
        } catch (Exception ex) {
            LOGGER.error("Error retrieving backup count", ex);
            response.status(500);
            return Core.serializeToJson(Map.of("error", "Internal server error"));
        }
    };
}
