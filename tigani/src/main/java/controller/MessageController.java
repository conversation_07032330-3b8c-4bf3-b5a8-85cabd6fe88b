package controller;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import core.Core;
import core.Pages;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.PermissionType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.*;
import spark.*;
import utils.*;

import java.util.*;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.conversions.Bson;

/**
 *
 * <AUTHOR>
 */
public class MessageController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MessageController.class.getName());

    public static TemplateViewRoute be_message_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.MESSAGE_MANAGEMENT.getCode(), PermissionType.VIEW);

        // non serve nulla perchè i dati vengano caricati tramite ajax
        return Core.render(Pages.BE_MESSAGE_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_message = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.MESSAGE_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("messageId"));
        if (oid != null) {
            Message loadedMessage = BaseDao.getDocumentById(oid, Message.class);
            attributes.put("curMessage", loadedMessage);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Message loadedMessage = BaseDao.getDocumentByParentId(parentId, Message.class);
                if (loadedMessage != null) {
                    attributes.put("curMessage", loadedMessage);
                }
            }
        }

        return Core.render(Pages.BE_MESSAGE, attributes, request);
    };

    public static TemplateViewRoute be_message_form = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.MESSAGE_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("messageId"));
        if (oid != null) {
            Message loadedMessage = BaseDao.getDocumentById(oid, Message.class);
            attributes.put("curMessage", loadedMessage);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Message loadedMessage = BaseDao.getDocumentByParentId(parentId, Message.class);
                if (loadedMessage != null) {
                    attributes.put("curMessage", loadedMessage);
                }
            }
        }

        // Return only the form content without the base template
        return Core.render(Pages.BE_MESSAGE_FORM, attributes, request);
    };

    public static Route be_message_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.MESSAGE_MANAGEMENT.getCode(), PermissionType.VIEW);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Message> loadedMessages;
        List<Bson> filters = new ArrayList<>();

        // Add messageType filtering if parameter is provided
        if (params.containsKey("messageType") && StringUtils.isNotBlank(params.get("messageType"))) {
            String messageType = params.get("messageType");
            if (!"ALL".equals(messageType)) {
                filters.add(DaoFilters.getFilter("messageType", DaoFiltersOperation.EQ, messageType));
            }
        }

        // Add date range filtering if parameters are provided
        if (params.containsKey("startDate") && StringUtils.isNotBlank(params.get("startDate"))) {
            Date startDate = DateTimeUtils.stringToDate(params.get("startDate"), "dd/MM/yyyy");
            if (startDate != null) {
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.GTE, startDate));
            }
        }

        if (params.containsKey("endDate") && StringUtils.isNotBlank(params.get("endDate"))) {
            Date endDate = DateTimeUtils.stringToDate(params.get("endDate"), "dd/MM/yyyy");
            if (endDate != null) {
                // Add one day to include the entire end date
                Date endOfDay = DateUtils.addDays(endDate, 1);
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.LTE, endOfDay));
            }
        }

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);

        if (loadArchived) {
            loadedMessages = BaseDao.getDocumentsByFilters(Message.class, queryOptions, loadArchived);
        } else {
            loadedMessages = BaseDao.getDocumentsByFilters(Message.class, queryOptions);
        }

        // Build response using JsonObject instead of manual String concatenation
        Map<String, Object> jsonResponse = new HashMap<>();
        List<List<String>> dataRows = new ArrayList<>();

        if (!loadedMessages.isEmpty()) {
            for (Message tmpMessage : loadedMessages) {
                List<String> row = new ArrayList<>();
                row.add(tmpMessage.getId().toString()); // ID for row identification

                // Subject/Body con link
                String subjectDisplay = StringUtils.isNotBlank(tmpMessage.getSubject()) ?
                    tmpMessage.getSubject() :
                    StringUtils.abbreviate(tmpMessage.getBody(), 50);
                String subjectLink = "<a class='text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline' messageId='" +
                    tmpMessage.getId() + "'>" +
                    StringUtils.defaultIfBlank(subjectDisplay, "N.D.") + "</a>";
                row.add(subjectLink);

                row.add(tmpMessage.getMessageType() != null ? tmpMessage.getMessageType() : "N.D.");
                row.add(tmpMessage.getStatus() != null ? tmpMessage.getStatus() : "N.D.");
                row.add(StringUtils.defaultIfBlank(tmpMessage.getToAddress(), "N.D."));
                row.add(DateTimeUtils.dateToString(tmpMessage.getCreation(), "dd/MM/YYYY"));
                row.add(DateTimeUtils.dateToString(tmpMessage.getSentAt(), "dd/MM/YYYY"));
                row.add(DateTimeUtils.dateToString(tmpMessage.getDeliveredAt(), "dd/MM/YYYY"));
                row.add(DateTimeUtils.dateToString(tmpMessage.getOpenedAt(), "dd/MM/YYYY"));
                row.add("Azioni");

                dataRows.add(row);
            }
        }

        jsonResponse.put("data", dataRows);
        return Core.serializeToJson(jsonResponse);
    };

    public static Route be_message_save = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("messageId"));

        // Only allow creation, not editing
        if (oid != null) {
            response.status(403);
            return "Modifica non consentita";
        }

        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.MESSAGE_MANAGEMENT.getCode(), PermissionType.CREATE);

        Message newMessage = RequestUtils.createFromParams(params, Message.class);

        if (newMessage != null) {
            // Set initial status as PENDING
            newMessage.setStatus(Message.MessageStatus.PENDING.name());

            try {
                // Actually send the message based on type
                String result = sendMessage(newMessage);
                if (result != null) {
                    // Save the message to database
                    oid = BaseDao.insertDocument(newMessage);
                    newMessage.setId(oid);
                    BaseDao.insertLog(user, newMessage, LogType.INSERT);
                    return "Messaggio inviato con successo";
                } else {
                    response.status(500);
                    return "Errore nell'invio del messaggio";
                }
            } catch (Exception e) {
                LOGGER.error("Errore nell'invio messaggio: {}", e.getMessage(), e);
                response.status(500);
                return "Errore nell'invio";
            }
        }

        response.status(400);
        return "Dati non validi";
    };

    public static Route be_message_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");

        // Only allow archive/unarchive operations, not delete
        if (StringUtils.equalsIgnoreCase(operation, "delete")) {
            response.status(403);
            return "Eliminazione non consentita";
        }

        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.MESSAGE_MANAGEMENT.getCode(), PermissionType.EDIT);

        String messageIds = params.get("messageIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(messageIds)) {
            String[] ids = messageIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    Message tmpMessage = BaseDao.getDocumentById(oid, Message.class);
                    if (tmpMessage != null) {
                        switch (operation) {
                            case "archive":
                                tmpMessage.setArchived(true);
                                BaseDao.updateDocument(tmpMessage, user);
                                BaseDao.insertLog(user, tmpMessage, LogType.UPDATE);
                                break;
                            case "unarchive":
                                tmpMessage.setArchived(false);
                                BaseDao.updateDocument(tmpMessage, user);
                                BaseDao.insertLog(user, tmpMessage, LogType.UPDATE);
                                break;
                            default:
                                response.status(400);
                                return "Operazione non supportata";
                        }
                    }
                }
            }
        }

        return "ok";
    };

    /**
     * Callback per aggiornamenti di stato SMS da Twilio
     * Endpoint: POST /api/message/twilio/callback
     */
    public static Route twilio_sms_callback = (Request request, Response response) -> {
        try {
            Map<String, String> params = new LinkedHashMap<>();
            RequestUtils.parseRequest(request, params, null);

            LOGGER.info("Ricevuto callback Twilio SMS: {}", params);

            // Parametri standard di Twilio
            String messageSid = params.get("MessageSid");
            String messageStatus = params.get("MessageStatus");
            String to = params.get("To");
            String from = params.get("From");
            String errorCode = params.get("ErrorCode");
            String errorMessage = params.get("ErrorMessage");

            if (messageSid == null || messageSid.isEmpty()) {
                LOGGER.warn("MessageSid mancante nel callback Twilio");
                response.status(400);
                return "MessageSid required";
            }

            // Bisogna ora prendere solo la prima parte, prima di ".recvd"
            messageSid = StringUtils.substringBefore(messageSid, ".recvd");

            // Trova il messaggio nel database tramite externalId
            Message message = findMessageByExternalId(messageSid);

            if (message == null) {
                LOGGER.warn("Messaggio non trovato per SID: {}", messageSid);
                // Non è necessariamente un errore, potrebbe essere un messaggio di test
                response.status(200);
                return "OK";
            }

            // Aggiorna lo stato del messaggio
            updateMessageStatus(message, messageStatus, errorCode, errorMessage);

            // Salva le modifiche
            BaseDao.updateDocument(message, null);

            LOGGER.info("Stato messaggio {} aggiornato a: {}", messageSid, messageStatus);

            response.status(200);
            return "OK";

        } catch (Exception e) {
            LOGGER.error("Errore nel callback Twilio SMS: {}", e.getMessage(), e);
            response.status(500);
            return "Internal Server Error";
        }
    };

    /**
     * Callback per eventi email da SendGrid
     * Endpoint: POST /api/message/sendgrid/callback
     */
    public static Route sendgrid_email_callback = (Request request, Response response) -> {
        try {
            // SendGrid invia eventi come JSON array
            String body = request.body();
            LOGGER.info("Ricevuto callback SendGrid: {}", body);

            if (body == null || body.isEmpty()) {
                LOGGER.warn("Body vuoto nel callback SendGrid");
                response.status(400);
                return "Body required";
            }

            // Parse del JSON (SendGrid invia un array di eventi)
            try {
                // Usa Gson per parsare il JSON
                Gson gson = new Gson();
                JsonArray events = gson.fromJson(body, JsonArray.class);

                for (int i = 0; i < events.size(); i++) {
                    JsonObject event = events.get(i).getAsJsonObject();
                    processSendGridEvent(event);
                }

            } catch (Exception e) {
                LOGGER.error("Errore nel parsing JSON SendGrid: {}", e.getMessage(), e);
                response.status(400);
                return "Invalid JSON";
            }

            response.status(200);
            return "OK";

        } catch (Exception e) {
            LOGGER.error("Errore nel callback SendGrid: {}", e.getMessage(), e);
            response.status(500);
            return "Internal Server Error";
        }
    };

    /**
     * Trova un messaggio nel database tramite externalId
     */
    private static Message findMessageByExternalId(String externalId) {
        try {
            // Cerca nella collection message
            return BaseDao.getDocumentByField("externalId", externalId, Message.class);
        } catch (Exception e) {
            LOGGER.error("Errore nella ricerca messaggio per externalId {}: {}", externalId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Aggiorna lo stato di un messaggio basandosi sui parametri Twilio
     */
    private static void updateMessageStatus(Message message, String status, String errorCode, String errorMessage) {
        if (status == null) return;

        switch (status.toLowerCase()) {
            case "sent":
                message.setStatus(Message.MessageStatus.SENT.name());
                if (message.getSentAt() == null) {
                    message.setSentAt(new Date());
                }
                break;

            case "delivered":
                message.setStatus(Message.MessageStatus.DELIVERED.name());
                message.setDeliveredAt(new Date());
                break;

            case "failed":
            case "undelivered":
                message.setStatus(Message.MessageStatus.FAILED.name());
                if (errorMessage != null) {
                    message.setErrorMessage(errorMessage);
                } else if (errorCode != null) {
                    message.setErrorMessage("Error code: " + errorCode);
                }
                break;

            default:
                LOGGER.info("Stato Twilio non gestito: {}", status);
                break;
        }
    }

    /**
     * Processa un singolo evento SendGrid
     */
    private static void processSendGridEvent(JsonObject event) {
        try {
            String eventType = event.has("event") ? event.get("event").getAsString() : null;
            String messageId = event.has("sg_message_id") ? event.get("sg_message_id").getAsString() : null;

            if (messageId == null || eventType == null) {
                LOGGER.warn("Evento SendGrid incompleto: {}", event);
                return;
            }

            // Bisogna ora prendere solo la prima parte, prima di ".recvd"
            messageId = StringUtils.substringBefore(messageId, ".recvd");

            // Trova il messaggio
            Message message = findMessageByExternalId(messageId);
            if (message == null) {
                LOGGER.warn("Messaggio non trovato per SendGrid ID: {}", messageId);
                return;
            }

            // Aggiorna lo stato basandosi sull'evento
            updateMessageStatusFromSendGrid(message, eventType, event);

            // Salva le modifiche
            BaseDao.updateDocument(message, null);

            LOGGER.info("Evento SendGrid {} processato per messaggio {}", eventType, messageId);

        } catch (Exception e) {
            LOGGER.error("Errore nel processare evento SendGrid: {}", e.getMessage(), e);
        }
    }

    /**
     * Aggiorna lo stato del messaggio basandosi sull'evento SendGrid
     */
    private static void updateMessageStatusFromSendGrid(Message message, String eventType, JsonObject event) {
        switch (eventType.toLowerCase()) {
            case "delivered":
                message.setStatus(Message.MessageStatus.DELIVERED.name());
                message.setDeliveredAt(new Date());
                break;

            case "bounce":
            case "dropped":
                message.setStatus(Message.MessageStatus.BOUNCED.name());
                if (event.has("reason")) {
                    message.setErrorMessage(event.get("reason").getAsString());
                }
                break;

            case "open":
                if (message.getOpenedAt() == null) {
                    message.setOpenedAt(new Date());
                }
                break;

            case "click":
                if (message.getClickedAt() == null) {
                    message.setClickedAt(new Date());
                }
                break;

            default:
                LOGGER.info("Evento SendGrid non gestito: {}", eventType);
                break;
        }
    }

    /**
     * Helper method to send a message based on its type
     */
    private static String sendMessage(Message message) {
        try {
            if (StringUtils.equalsIgnoreCase(message.getMessageType(), Message.MessageType.EMAIL.name())) {
                String messageId = utils.EmailUtils.sendMessage(message);
                if (messageId != null) {
                    message.markAsSent(messageId);
                    return messageId;
                } else {
                    message.markAsFailed("Errore nell'invio email");
                    return null;
                }
            } else if (StringUtils.equalsIgnoreCase(message.getMessageType(), Message.MessageType.SMS.name())) {
                String messageSid = utils.SmsUtils.sendMessage(message);
                if (messageSid != null) {
                    message.markAsSent(messageSid);
                    return messageSid;
                } else {
                    message.markAsFailed("Errore nell'invio SMS");
                    return null;
                }
            } else {
                LOGGER.warn("Tipo messaggio non supportato: {}", message.getMessageType());
                return null;
            }
        } catch (Exception e) {
            LOGGER.error("Errore nell'invio messaggio: {}", e.getMessage(), e);
            message.markAsFailed("Errore nell'invio: " + e.getMessage());
            return null;
        }
    }
}
