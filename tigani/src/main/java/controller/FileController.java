package controller;

import dao.BaseDao;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.DocumentDescriptor;
import spark.Request;
import spark.Response;
import spark.Route;
import utils.Defaults;
import utils.EnvironmentUtils;
import utils.RequestUtils;

/**
 *
 * <AUTHOR>
 */
public class FileController {
    private static final Logger LOGGER = LoggerFactory.getLogger(FileController.class.getName());
    

    public static Route file = (Request request, Response response) -> {
        ObjectId oid = RequestUtils.toObjectId(request.queryParams("oid"));

        if (oid != null) {
            DocumentDescriptor document = BaseDao.getDocumentById(oid, DocumentDescriptor.class, "");

            if (document != null) {
                String contentType = document.getMetadata().get("contentType");
                if (StringUtils.isNotBlank(contentType)) {
                    response.type(contentType);
                }

                if (EnvironmentUtils.hasNotResourcesHotDeploy()) {
                    // file caching
                    response.header("Cache-Control", "private, max-age=" + Defaults.STATIC_RESOURCE_EXPIRATION_TIME);
                    response.header("Expires", new Date(System.currentTimeMillis() + (Defaults.STATIC_RESOURCE_EXPIRATION_TIME * 1000)).toString());
                }
                
                String filename = document.getMetadata().get("originalFilename");
                String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8.toString()).replaceAll("\\+", "%20");
                response.header("Content-Disposition", "attachment; filename=\"" + filename + "\"; filename*=UTF-8''" + encodedFilename);


                try {
                    File file = new File(document.getFilePath());
                    try (FileInputStream fis = new FileInputStream(file);  ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
                        byte[] buffer = new byte[1024];
                        int bytesRead;

                        while ((bytesRead = fis.read(buffer)) != -1) {
                            bos.write(buffer, 0, bytesRead);
                        }

                        document.setContent(bos.toByteArray());
                    }
                    if (document.getContent() != null && document.getContent().length > 0) {
                        response.raw().getOutputStream().write(document.getContent());
                        response.raw().getOutputStream().flush();
                        response.raw().getOutputStream().close();
                    }
                } catch (IOException ex) {
                    LOGGER.error("cannot return file; id " + oid + " exception class is " + ex.getClass().getSimpleName(), ex);
                }

            } else {
                LOGGER.warn("empty file oid " + oid);
            }
        } else {
            LOGGER.warn("empty oid " + oid);
        }

        return "";
    };
}
