package controller;

import core.Core;
import core.Pages;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.PermissionType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.*;
import spark.*;
import utils.*;

import java.util.*;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.conversions.Bson;

/**
 *
 * <AUTHOR>
 */
public class CountryController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CountryController.class.getName());

    public static TemplateViewRoute be_country_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.COUNTRY_MANAGEMENT.getCode(), PermissionType.VIEW);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_COUNTRY_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_country = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.COUNTRY_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("countryId"));
        if (oid != null) {
            Country loadedCountry = BaseDao.getDocumentById(oid, Country.class);
            attributes.put("curCountry", loadedCountry);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Country loadedCountry = BaseDao.getDocumentByParentId(parentId, Country.class);
                if (loadedCountry != null) {
                    attributes.put("curCountry", loadedCountry);
                }
            }
        }

        return Core.render(Pages.BE_COUNTRY, attributes, request);
    };

    public static TemplateViewRoute be_country_form = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.COUNTRY_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("countryId"));
        if (oid != null) {
            Country loadedCountry = BaseDao.getDocumentById(oid, Country.class);
            attributes.put("curCountry", loadedCountry);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Country loadedCountry = BaseDao.getDocumentByParentId(parentId, Country.class);
                if (loadedCountry != null) {
                    attributes.put("curCountry", loadedCountry);
                }
            }
        }

        // Return only the form content without the base template
        return Core.render(Pages.BE_COUNTRY_FORM, attributes, request);
    };

    public static Route be_country_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.COUNTRY_MANAGEMENT.getCode(), PermissionType.VIEW);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Country> loadedCountries;
        List<Bson> filters = new ArrayList<>();

        // Add date range filtering if parameters are provided
        if (params.containsKey("startDate") && StringUtils.isNotBlank(params.get("startDate"))) {
            Date startDate = DateTimeUtils.stringToDate(params.get("startDate"), "dd/MM/yyyy");
            if (startDate != null) {
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.GTE, startDate));
            }
        }

        if (params.containsKey("endDate") && StringUtils.isNotBlank(params.get("endDate"))) {
            Date endDate = DateTimeUtils.stringToDate(params.get("endDate"), "dd/MM/yyyy");
            if (endDate != null) {
                // Add one day to include the entire end date
                Date endOfDay = DateUtils.addDays(endDate, 1);
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.LTE, endOfDay));
            }
        }

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);

        if (loadArchived) {
            loadedCountries = BaseDao.getDocumentsByFilters(Country.class, queryOptions, loadArchived);
        } else {
            loadedCountries = BaseDao.getDocumentsByFilters(Country.class, queryOptions);
        }

        // Build response using JsonObject instead of manual String concatenation
        Map<String, Object> jsonResponse = new HashMap<>();
        List<List<String>> dataRows = new ArrayList<>();

        if (!loadedCountries.isEmpty()) {
            for (Country tmpCountry : loadedCountries) {
                List<String> row = new ArrayList<>();
                row.add(tmpCountry.getId().toString()); // ID for row identification

                // Description con link
                String descriptionLink = "<a class='text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline' countryId='" +
                    tmpCountry.getId() + "'>" +
                    StringUtils.defaultIfBlank(tmpCountry.getDescription(), "N.D.") + "</a>";
                row.add(descriptionLink);

                row.add(StringUtils.defaultIfBlank(tmpCountry.getCode(), "N.D."));
                row.add(StringUtils.defaultIfBlank(tmpCountry.getCodiceBelfiore(), "N.D."));
                row.add(DateTimeUtils.dateToString(tmpCountry.getCreation(), "dd/MM/YYYY"));
                row.add(DateTimeUtils.dateToString(tmpCountry.getLastUpdate(), "dd/MM/YYYY"));
                row.add("Azioni");

                dataRows.add(row);
            }
        }

        jsonResponse.put("data", dataRows);
        return Core.serializeToJson(jsonResponse);
    };

    public static Route be_country_save = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("countryId"));

        // Determine if this is create or edit operation
        PermissionType requiredPermission = (oid != null) ? PermissionType.EDIT : PermissionType.CREATE;

        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.COUNTRY_MANAGEMENT.getCode(), requiredPermission);

        Country newCountry;
        if (oid != null) {
            newCountry = BaseDao.getDocumentById(oid, Country.class);
            RequestUtils.mergeFromParams(params, newCountry);
        } else {
            newCountry = RequestUtils.createFromParams(params, Country.class);
        }

        if (newCountry != null) {
            if (oid == null) {
                oid = BaseDao.insertDocument(newCountry);
                newCountry.setId(oid);

                BaseDao.insertLog(user, newCountry, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newCountry, user);
                BaseDao.insertLog(user, newCountry, LogType.UPDATE);
            }
        }

        // se errore ritorno Spark.halt()
        return oid;
    };

    public static Route be_country_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        PermissionType requiredPermission = PermissionType.EDIT;
        if (StringUtils.equalsIgnoreCase(operation, "delete")) {
            requiredPermission = PermissionType.DELETE;
        }

        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.COUNTRY_MANAGEMENT.getCode(), requiredPermission);

        String countryIds = params.get("countryIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(countryIds)) {
            String[] ids = countryIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    Country tmpCountry = BaseDao.getDocumentById(oid, Country.class);
                    if (tmpCountry != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(tmpCountry, user);
                                BaseDao.insertLog(user, tmpCountry, LogType.DELETE);
                                break;
                            case "archive":
                                tmpCountry.setArchived(true);
                                BaseDao.updateDocument(tmpCountry, user);
                                BaseDao.insertLog(user, tmpCountry, LogType.UPDATE);
                                break;
                            case "unarchive":
                                tmpCountry.setArchived(false);
                                BaseDao.updateDocument(tmpCountry, user);
                                BaseDao.insertLog(user, tmpCountry, LogType.UPDATE);
                                break;
                        }
                    }
                }
            }
        }

        return "ok";
    };
}
