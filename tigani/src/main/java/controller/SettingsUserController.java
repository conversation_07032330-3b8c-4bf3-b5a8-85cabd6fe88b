package controller;

import com.mongodb.client.model.Filters;
import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import dao.UserDao;
import enums.ProfileType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.QueryOptions;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.TemplateViewRoute;
import utils.PasswordHash;
import utils.RequestUtils;
import utils.RoutesUtils;
import utils.UploadedFile;

/**
 *
 * <AUTHOR>
 */
public class SettingsUserController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SettingsUserController.class.getName());

    public static TemplateViewRoute be_settings_user_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_SETTINGS_USER_COLLECTION, attributes, request);
    };

    public static Route be_settings_user_data = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<User> userList;
        
        List<Bson> orFilters = new ArrayList<>();
        orFilters.add(DaoFilters.getFilter("profileType", DaoFiltersOperation.EQ, "system"));
        orFilters.add(DaoFilters.getFilter("profileType", DaoFiltersOperation.EQ, "admin"));
        orFilters.add(DaoFilters.getFilter("profileType", DaoFiltersOperation.EQ, "operator"));
        
        Bson orFilter = Filters.or(orFilters);
        List<Bson> filters = new ArrayList<>();
        filters.add(orFilter); 

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);

        if (loadArchived) {
            userList = BaseDao.getDocumentsByFilters(User.class, queryOptions, loadArchived);
        } else {
            userList = BaseDao.getDocumentsByFilters(User.class, queryOptions);
        }

        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (!userList.isEmpty()) {
            for (User tmpUser : userList) {
                json.append("[");
                // account "operator" non può fare nulla
                if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.SYSTEM.toString()) || StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.ADMIN.toString())) {
                    json.append("\"<a href='").append(RoutesUtils.contextPath(request)).append(Routes.BE_SETTINGS_USER + "?userId=").append(tmpUser.getId()).append("'>").append((StringUtils.defaultIfBlank(tmpUser.getName(), "N.D."))).append("</a>\",");
                } else {
                    json.append("\"").append((StringUtils.defaultIfBlank(tmpUser.getName(), "N.D."))).append("\",");
                }
                json.append("\"").append(tmpUser.getUsername()!= null ? tmpUser.getUsername() : "").append("\",");
                json.append("\"").append(tmpUser.getProfileType() != null ? tmpUser.getProfileType() : "").append("\",");
                json.append("\"").append("\""); // ultima colonna vuota
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovo ultima virgola array
        }
        json.append("]}");

        return json.toString();
    };

    public static TemplateViewRoute be_settings_user = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("userId"));
        if (oid != null) {
            User loadedUser = BaseDao.getDocumentById(oid, User.class);
            attributes.put("curUser", loadedUser);
        }

        return Core.render(Pages.BE_SETTINGS_USER, attributes, request);
    };

    public static Route be_settings_user_save = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);
        ObjectId oid = RequestUtils.toObjectId(request.queryParams("userId"));
        User newUser;

        boolean needToCryptPassword = false;
        if (oid != null) {
            newUser = BaseDao.getDocumentById(oid, User.class);
            if (newUser != null) {
                if (params.containsKey("password")) {
                    if (!StringUtils.equals(newUser.getPassword(), params.get("password"))) {
                        needToCryptPassword = true;
                    }
                }
                // se cambio username verifico se posso farlo
                if (params.containsKey("username")) {
                    String username = params.get("username");
                    if (!StringUtils.equals(username, newUser.getUsername())) {
                        // verifico che non ci sia già
                        User tmpUser = UserDao.loadUserByEmail(username);
                        if (tmpUser != null) {
                            throw new Exception("Username already exists");
                        }
                    }
                }
            }
            RequestUtils.mergeFromParams(params, newUser);
        } else {
            newUser = RequestUtils.createFromParams(params, User.class);
            needToCryptPassword = true;
        }

        if (newUser != null) {
            if (needToCryptPassword) {
                newUser.setPassword(PasswordHash.createHash(newUser.getPassword()));
            }
            if (StringUtils.isBlank(newUser.getEmail())) {
                newUser.setEmail(newUser.getUsername());
            }

            if (oid == null) {
                oid = BaseDao.insertDocument(newUser);
                newUser.setId(oid);
            } else {
                BaseDao.updateDocument(newUser, user);
                if (user.getId().equals(oid)) {
                    // update user on redis
                    String token = Core.getSessionToken(request);
                    Core.addValueToSession(token, "user", newUser);
                }

            }
        }
        if (oid != null) {
            if (!files.isEmpty()) {
                BaseDao.deleteImage(newUser, "imageId");
                BaseDao.saveImage(files.entrySet().iterator().next().getValue(), newUser, "imageId", true);
                if (user.getId().equals(oid)) {
                    // update user on redis
                    String token = Core.getSessionToken(request);
                    Core.addValueToSession(token, "user", newUser);
                }
            }
        }

        return oid;
    };
}
