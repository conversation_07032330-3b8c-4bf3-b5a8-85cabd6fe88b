package controller;

import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import enums.ProfileType;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.BooleanUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Smtp;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.TemplateViewRoute;
import commons.NotificationCommons;
import utils.RequestUtils;
import utils.RoutesUtils;
import utils.UploadedFile;

/**
 *
 * <AUTHOR>
 */
public class SmtpController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SmtpController.class.getName());

    public static TemplateViewRoute be_smtp_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_SETTINGS_SMTP_COLLECTION, attributes, request);
    };

    public static Route be_smtp_data = (Request request, Response response) -> {
        // logged user
        User user = Core.getUserFromRequest(request);
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Smtp> smtpList;
        if (loadArchived) {
            smtpList = BaseDao.getArchivedDocuments(Smtp.class);
        } else {
            smtpList = BaseDao.getDocuments(Smtp.class);
        }

        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (!smtpList.isEmpty()) {
            for (Smtp smtp : smtpList) {
                json.append("[");
                json.append("\"<a href='").append(RoutesUtils.contextPath(request)).append(Routes.BE_SETTINGS_SMTP + "?smtpId=").append(smtp.getId()).append("'>").append(smtp.getHostname()).append("</a>\",");
                json.append("\"").append(smtp.getPort() != null ? smtp.getPort() : "").append("\",");
                json.append("\"").append(smtp.getAuthentication() != null ? smtp.getAuthentication() : "").append("\",");
                json.append("\"").append(smtp.getUsername() != null ? smtp.getUsername() : "").append("\",");
                json.append("\"").append(smtp.getEncryption() != null ? smtp.getEncryption() : "").append("\",");
                json.append("\"").append(smtp.getStartTls() != null ? smtp.getStartTls() : "").append("\",");
                json.append("\"").append(smtp.getSender() != null ? smtp.getSender() : "").append("\",");
                json.append("\"").append("\""); // ultima colonna vuota
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovo ultima virgola array
        }
        json.append("]}");

        return json.toString();
    };

    public static TemplateViewRoute be_smtp = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes);
        if (user == null) {
            return Core.render(Pages.BE_LOGIN, attributes, request);
        }

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("smtpId"));
        if (oid != null) {
            Smtp loadedSmtp = BaseDao.getDocumentById(oid, Smtp.class);
            attributes.put("smtp", loadedSmtp);
        }

        return Core.render(Pages.BE_SETTINGS_SMTP, attributes, request);
    };

    public static Route be_smtp_save = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);
        ObjectId oid = RequestUtils.toObjectId(request.queryParams("smtpId"));
        Smtp newSmtp;
        if (oid != null) {
            newSmtp = BaseDao.getDocumentById(oid, Smtp.class);
            RequestUtils.mergeFromParams(params, newSmtp);
        } else {
            newSmtp = RequestUtils.createFromParams(params, Smtp.class);
        }

        if (newSmtp != null) {
            if (!params.containsKey("authentication")) {
                newSmtp.setAuthentication(false);
            }
            if (!params.containsKey("encryption")) {
                newSmtp.setEncryption(false);
            }
            if (!params.containsKey("startTls")) {
                newSmtp.setStartTls(false);
            }

            if (oid == null) {
                BaseDao.insertDocument(newSmtp);
            } else {
                BaseDao.updateDocument(newSmtp, user);
            }
        }

        response.redirect(RoutesUtils.contextPath(request) + Routes.BE_SETTINGS_SMTP_COLLECTION);
        return null;
    };

    public static Route send_mail = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        // TODO: salvare i files in file temporanei e poi cancellarli dopo aver inviato la mail
        try {
            Map<String, Object> objectParams = new HashMap<>();
            for (String key : params.keySet()) {
                objectParams.put(key, params.get(key));
            }
            // Use the new NotificationCommons function with recaptcha validation enabled
            NotificationCommons.sendTemplatedEmail(objectParams, true, true);
        } catch (Exception ex) {
            LOGGER.error("Error sending email: ", ex);
            // Re-throw the exception to maintain the same error handling behavior
            throw ex;
        }

        return null;
    };
}
