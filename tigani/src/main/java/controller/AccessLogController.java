package controller;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.PermissionType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.*;
import spark.*;
import utils.*;
import utils.RoutesUtils;

import java.util.*;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.conversions.Bson;

/**
 *
 * <AUTHOR>
 */
public class AccessLogController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccessLogController.class.getName());

    public static TemplateViewRoute be_accesslog_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.ACCESS_LOG_MANAGEMENT.getCode(), PermissionType.VIEW);

        // non serve nulla perchè i dati vengano caricati tramite ajax
        return Core.render(Pages.BE_ACCESSLOG_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_accesslog_form = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.ACCESS_LOG_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("accessLogId"));
        if (oid != null) {
            AccessLog loadedAccessLog = BaseDao.getDocumentById(oid, AccessLog.class);
            attributes.put("curAccessLog", loadedAccessLog);
        }

        // Return only the form content without the base template
        return Core.render(Pages.BE_ACCESSLOG_FORM, attributes, request);
    };

    public static Route be_accesslog_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.ACCESS_LOG_MANAGEMENT.getCode(), PermissionType.VIEW);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<AccessLog> loadedAccessLogs;
        List<Bson> filters = new ArrayList<>();

        // Add date range filtering if parameters are provided
        if (params.containsKey("startDate") && StringUtils.isNotBlank(params.get("startDate"))) {
            Date startDate = DateTimeUtils.stringToDate(params.get("startDate"), "dd/MM/yyyy");
            if (startDate != null) {
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.GTE, startDate));
            }
        }

        if (params.containsKey("endDate") && StringUtils.isNotBlank(params.get("endDate"))) {
            Date endDate = DateTimeUtils.stringToDate(params.get("endDate"), "dd/MM/yyyy");
            if (endDate != null) {
                // Add one day to include the entire end date
                Date endOfDay = DateUtils.addDays(endDate, 1);
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.LTE, endOfDay));
            }
        }

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);

        if (loadArchived) {
            loadedAccessLogs = BaseDao.getDocumentsByFilters(AccessLog.class, queryOptions, loadArchived);
        } else {
            loadedAccessLogs = BaseDao.getDocumentsByFilters(AccessLog.class, queryOptions);
        }

        // Build response using JsonObject instead of manual String concatenation
        Map<String, Object> jsonResponse = new HashMap<>();
        List<List<String>> dataRows = new ArrayList<>();

        if (!loadedAccessLogs.isEmpty()) {
            for (AccessLog tmpAccessLog : loadedAccessLogs) {
                List<String> row = new ArrayList<>();
                row.add(tmpAccessLog.getId().toString()); // ID for row identification

                // User ID with popover (similar to LogController)
                StringBuilder userHtml = new StringBuilder();
                String userName = "N.D.";
                User logUser = null;

                // Try to load user data for popover if userId is available
                if (tmpAccessLog.getUserId() != null) {
                    try {
                        logUser = BaseDao.getDocumentById(tmpAccessLog.getUserId(), User.class);
                    } catch (Exception e) {
                        // Silently ignore user loading errors to avoid breaking display
                        LOGGER.debug("Could not load user data for popover: " + e.getMessage());
                    }
                }

                // Generate username span with popover data if user is available
                if (logUser != null) {
                    userName = StringUtils.trim(StringUtils.defaultIfBlank(logUser.getName(), "") + " " + StringUtils.defaultIfBlank(logUser.getLastname(), ""));
                    if (StringUtils.isBlank(userName)) {
                        userName = StringUtils.defaultIfBlank(logUser.getUsername(), "Utente");
                    }

                    userHtml.append("<div class=\"hs-tooltip [--trigger:hover] sm:[--placement:right] inline-block\">");
                    userHtml.append("<div class=\"hs-tooltip-toggle\" tabindex=\"0\">");
                    userHtml.append("<span class=\"font-medium text-gray-800 dark:text-neutral-200 cursor-pointer hover:underline\">").append(userName).append("</span>");

                    // Popover Content
                    userHtml.append("<div class=\"hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible hidden opacity-0 transition-opacity absolute invisible z-10 max-w-xs w-full bg-white border border-gray-100 text-start rounded-xl shadow-md after:absolute after:top-0 after:-start-4 after:w-4 after:h-full dark:bg-neutral-800 dark:border-neutral-700\" role=\"tooltip\">");

                    // Header
                    userHtml.append("<div class=\"py-3 px-4 border-b border-gray-200 dark:border-neutral-700\">");
                    userHtml.append("<div class=\"flex items-center gap-x-3\">");

                    // Profile image or initials in header
                    if (logUser.getImageId() != null) {
                        userHtml.append("<img class=\"shrink-0 inline-block size-10 rounded-full ring-2 ring-white dark:ring-neutral-900\" src=\"").append(RoutesUtils.contextPath(request)).append(Routes.BE_IMAGE).append("?oid=").append(logUser.getImageId()).append("\" alt=\"Avatar\">");
                    } else {
                        String initials = "";
                        if (StringUtils.isNotBlank(logUser.getName())) {
                            initials += logUser.getName().substring(0, 1).toUpperCase();
                        }
                        if (StringUtils.isNotBlank(logUser.getLastname())) {
                            initials += logUser.getLastname().substring(0, 1).toUpperCase();
                        }
                        if (StringUtils.isBlank(initials)) {
                            initials = "U";
                        }
                        userHtml.append("<div class=\"shrink-0 inline-block size-10 rounded-full ring-2 ring-white dark:ring-neutral-900 bg-gray-100 flex items-center justify-center text-sm font-medium text-gray-600 dark:bg-neutral-800 dark:text-neutral-400\">").append(initials).append("</div>");
                    }

                    userHtml.append("<div class=\"grow\">");
                    userHtml.append("<h4 class=\"font-semibold text-gray-800 dark:text-white flex items-center gap-1\">");
                    userHtml.append(userName);

                    // Profile type badge
                    if (StringUtils.isNotBlank(logUser.getProfileType())) {
                        String profileTypeDisplay = StringUtils.capitalize(logUser.getProfileType().toLowerCase());
                        userHtml.append("<span class=\"ms-0.5 inline-flex items-center align-middle gap-x-1.5 py-0.5 px-1.5 rounded-md text-[11px] font-medium bg-gray-800 text-white dark:bg-white dark:text-neutral-800\">");
                        userHtml.append(profileTypeDisplay);
                        userHtml.append("</span>");
                    }

                    userHtml.append("</h4>");
                    userHtml.append("<p class=\"text-sm text-gray-500 dark:text-neutral-500\">");
                    userHtml.append(StringUtils.defaultIfBlank(logUser.getEmail(), "Utente"));
                    userHtml.append("</p>");
                    userHtml.append("</div>");
                    userHtml.append("</div>");
                    userHtml.append("</div>");

                    // List
                    userHtml.append("<ul class=\"py-3 px-4 space-y-1\">");

                    // Email
                    if (StringUtils.isNotBlank(logUser.getEmail())) {
                        userHtml.append("<li>");
                        userHtml.append("<div class=\"inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200\">");
                        userHtml.append("<svg class=\"shrink-0 size-4 text-gray-600 dark:text-neutral-400\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">");
                        userHtml.append("<rect width=\"20\" height=\"16\" x=\"2\" y=\"4\" rx=\"2\"></rect>");
                        userHtml.append("<path d=\"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7\"></path>");
                        userHtml.append("</svg>");
                        userHtml.append(logUser.getEmail());
                        userHtml.append("</div>");
                        userHtml.append("</li>");
                    }

                    // Phone number
                    if (StringUtils.isNotBlank(logUser.getPhoneNumber())) {
                        userHtml.append("<li>");
                        userHtml.append("<div class=\"inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200\">");
                        userHtml.append("<svg class=\"shrink-0 size-4 text-gray-600 dark:text-neutral-400\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">");
                        userHtml.append("<rect width=\"14\" height=\"20\" x=\"5\" y=\"2\" rx=\"2\" ry=\"2\"></rect>");
                        userHtml.append("<path d=\"M12 18h.01\"></path>");
                        userHtml.append("</svg>");
                        userHtml.append(logUser.getPhoneNumber());
                        userHtml.append("</div>");
                        userHtml.append("</li>");
                    }

                    userHtml.append("</ul>");
                    userHtml.append("</div>");
                    userHtml.append("</div>");
                    userHtml.append("</div>");
                } else {
                    // Fallback for when user data is not available
                    String userIdDisplay = tmpAccessLog.getUserId() != null ? tmpAccessLog.getUserId().toString() : "N.D.";
                    userHtml.append("<span class=\"font-medium text-gray-800 dark:text-neutral-200\">").append(userIdDisplay).append("</span>");
                }

                row.add(userHtml.toString());

                row.add(StringUtils.defaultIfBlank(tmpAccessLog.getFailureReason(), "N.D."));
                row.add(DateTimeUtils.dateToString(tmpAccessLog.getCreation(), "dd/MM/YYYY HH:mm"));
                row.add("Azioni");

                dataRows.add(row);
            }
        }

        jsonResponse.put("data", dataRows);
        return Core.serializeToJson(jsonResponse);
    };
}
