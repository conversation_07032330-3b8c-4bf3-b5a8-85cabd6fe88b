package controller;

import com.google.gson.JsonObject;
import com.mongodb.client.model.Filters;
import org.json.JSONObject;
import core.Core;
import core.Pages;
import dao.BaseDao;
import dao.DaoFilters;
import enums.LogType;
import enums.PermissionType;
import enums.StatusType;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Product;
import pojo.RoutesPermission;
import pojo.User;
import pojo.Warranty;
import pojo.WarrantyEntry;
import utils.WarrantyCommons;
import spark.*;
import utils.*;

import java.util.*;
import org.bson.conversions.Bson;
import pojo.QueryOptions;

/**
 *
 * <AUTHOR>
 */
public class ProductController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductController.class.getName());

    public static TemplateViewRoute be_product_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
                RoutesPermission.PRODUCT_MANAGEMENT.getCode(), PermissionType.VIEW);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_PRODUCT_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_product_edit = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
                RoutesPermission.PRODUCT_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("productId"));
        if (oid != null) {
            Product loadedProduct = BaseDao.getDocumentById(oid, Product.class);
            attributes.put("curProduct", loadedProduct);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Product loadedProduct = BaseDao.getDocumentByParentId(parentId, Product.class);
                if (loadedProduct != null) {
                    attributes.put("curProduct", loadedProduct);
                }
            }
        }

        return Core.render(Pages.BE_PRODUCT_EDIT, attributes, request);
    };

    public static TemplateViewRoute be_product_form = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
                RoutesPermission.PRODUCT_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("productId"));
        if (oid != null) {
            Product loadedProduct = BaseDao.getDocumentById(oid, Product.class);
            attributes.put("curProduct", loadedProduct);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Product loadedProduct = BaseDao.getDocumentByParentId(parentId, Product.class);
                if (loadedProduct != null) {
                    attributes.put("curProduct", loadedProduct);
                }
            }
        }

        // Return only the form content without the base template
        return Core.render(Pages.BE_PRODUCT_FORM, attributes, request);
    };

    /*public static Route be_product_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.PRODUCT_MANAGEMENT.getCode(), PermissionType.VIEW);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        // Prepare filters
        List<Bson> filters = new ArrayList<>();
        filters.add(Filters.eq("cancelled", false));
        if (!loadArchived) {
            filters.add(Filters.eq("archived", false));
        }

        // Search filters
        if (params.containsKey("search") && StringUtils.isNotBlank(params.get("search"))) {
            String searchTerm = params.get("search");
            List<Bson> searchFilters = new ArrayList<>();
            searchFilters.add(Filters.regex("name", searchTerm, "i"));
            searchFilters.add(Filters.regex("code", searchTerm, "i"));
            searchFilters.add(Filters.regex("description", searchTerm, "i"));
            filters.add(Filters.or(searchFilters));
        }

        // Status filter
        if (params.containsKey("status") && StringUtils.isNotBlank(params.get("status"))) {
            StatusType status = StatusType.valueOf(params.get("status"));
            filters.add(Filters.eq("status", status));
        }

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);
        List<Product> products = BaseDao.getDocumentsByFilters(Product.class, queryOptions);
        long totalCount = BaseDao.countDocuments(filters, Product.class);

        // Create response
        JsonObject jsonResponse = new JsonObject();
        jsonResponse.addProperty("draw", Integer.parseInt(params.getOrDefault("draw", "1")));
        jsonResponse.addProperty("recordsTotal", totalCount);
        jsonResponse.addProperty("recordsFiltered", totalCount);
        jsonResponse.add("data", new Gson().toJsonTree(products));

        response.type("application/json");
        return jsonResponse.toString();
    };*/

    public static Route be_product_save = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(params.get("productId"));
        String action = params.get("action"); // "draft" or "continue"

        // Determine if this is create or edit operation
        PermissionType requiredPermission = (oid != null) ? PermissionType.EDIT : PermissionType.CREATE;

        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
                RoutesPermission.PRODUCT_MANAGEMENT.getCode(), requiredPermission);

        JSONObject jsonResponse = new JSONObject();

        try {
            Product newProduct;
            if (oid != null) {
                newProduct = BaseDao.getDocumentById(oid, Product.class);
                if (newProduct == null) {
                    response.status(404);
                    jsonResponse.put("success", false);
                    jsonResponse.put("message", "Prodotto non trovato");
                    response.type("application/json");
                    return jsonResponse.toString();
                }
                RequestUtils.mergeFromParams(params, newProduct);
            } else {
                newProduct = RequestUtils.createFromParams(params, Product.class);
                // Set default values for new products
                if (newProduct != null) {
                    newProduct.setCreatedByUserId(user.getId());
                    newProduct.setVersion("1.0");
                    newProduct.setStatus(StatusType.DRAFT.name());
                }
            }

            if (newProduct != null) {
                // Validate required fields
                if (StringUtils.isBlank(newProduct.getCode())) {
                    response.status(400);
                    jsonResponse.put("success", false);
                    jsonResponse.put("message", "Il codice prodotto è obbligatorio");
                    response.type("application/json");
                    return jsonResponse.toString();
                }

                if (StringUtils.isBlank(newProduct.getName())) {
                    response.status(400);
                    jsonResponse.put("success", false);
                    jsonResponse.put("message", "Il nome prodotto è obbligatorio");
                    response.type("application/json");
                    return jsonResponse.toString();
                }

                // Validate product code uniqueness
                Product existingProduct = BaseDao.getDocumentByField("code", newProduct.getCode(), Product.class);
                if (existingProduct != null && !existingProduct.getId().equals(newProduct.getId())) {
                    response.status(400);
                    jsonResponse.put("success", false);
                    jsonResponse.put("message", "Codice prodotto già esistente. Utilizzare un codice diverso.");
                    response.type("application/json");
                    return jsonResponse.toString();
                }

                // Update status based on action for existing products
                /*if (oid != null && "continue".equals(action)) {
                    newProduct.setStatus(StatusType.ACTIVE);
                } else if (oid != null && "draft".equals(action)) {
                    newProduct.setStatus(StatusType.DRAFT);
                }*/

                if (newProduct.getStep() == null) {
                    newProduct.setStep(1);
                }
                newProduct.setStep(newProduct.getStep() + 1);

                // Handle database operations
                boolean needToUpdate = false;
                if (oid == null) {
                    oid = BaseDao.insertDocument(newProduct);
                    newProduct.setId(oid);
                    BaseDao.insertLog(user, newProduct, LogType.INSERT);
                } else {
                    needToUpdate = true;
                    BaseDao.insertLog(user, newProduct, LogType.UPDATE);
                }

                // Handle file uploads only for step 1
                if (params.containsKey("step")) {
                    Integer step = Integer.parseInt(params.get("step"));
                    if (step == 1) {
                        if (!files.isEmpty()) {
                            BaseDao.saveImage(files.entrySet().iterator().next().getValue(), newProduct, "logoImageId", false);
                        } else {
                            if (!params.containsKey("sameImage")) {
                                BaseDao.deleteImage(newProduct, "logoImageId");
                            }
                        }
                    }
                }

                if (needToUpdate) {
                    BaseDao.updateDocument(newProduct, user);
                }

                // Return success response
                jsonResponse.put("success", true);
                jsonResponse.put("message", "Prodotto salvato con successo");
                jsonResponse.put("productId", oid.toString());
                jsonResponse.put("action", action);

            } else {
                response.status(400);
                jsonResponse.put("success", false);
                jsonResponse.put("message", "Errore durante la creazione del prodotto");
            }

        } catch (Exception e) {
            response.status(500);
            jsonResponse.put("success", false);
            jsonResponse.put("message", "Errore interno del server: " + e.getMessage());
            e.printStackTrace();
        }

        response.type("application/json");
        return jsonResponse.toString();
    };

    public static Route be_product_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        PermissionType requiredPermission = PermissionType.EDIT;
        if (StringUtils.equalsIgnoreCase(operation, "delete")) {
            requiredPermission = PermissionType.DELETE;
        }

        User user = Core.initializeRoute(request, response, attributes,
                RoutesPermission.PRODUCT_MANAGEMENT.getCode(), requiredPermission);

        String productIds = params.get("productIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(productIds)) {
            String[] ids = productIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    Product tmpProduct = BaseDao.getDocumentById(oid, Product.class);
                    if (tmpProduct != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(tmpProduct, user);
                                BaseDao.insertLog(user, tmpProduct, LogType.DELETE);
                                break;
                            case "archive":
                                tmpProduct.setArchived(true);
                                BaseDao.updateDocument(tmpProduct, user);
                                BaseDao.insertLog(user, tmpProduct, LogType.UPDATE);
                                break;
                            case "unarchive":
                                tmpProduct.setArchived(false);
                                BaseDao.updateDocument(tmpProduct, user);
                                BaseDao.insertLog(user, tmpProduct, LogType.UPDATE);
                                break;
                        }
                    }
                }
            }
        }

        return "OK";
    };

    public static Route be_product_check_draft = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
                RoutesPermission.PRODUCT_MANAGEMENT.getCode(), PermissionType.VIEW);

        // Check for existing draft products for this user
        List<Bson> filters = new ArrayList<>();
        filters.add(Filters.eq("createdByUserId", user.getId()));
        filters.add(Filters.eq("status", StatusType.DRAFT.name()));

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 1, null, null);
        List<Product> draftProducts = BaseDao.getDocumentsByFilters(Product.class, queryOptions);

        JsonObject jsonResponse = new JsonObject();
        jsonResponse.addProperty("hasDraft", !draftProducts.isEmpty());
        if (!draftProducts.isEmpty()) {
            Product draftProduct = draftProducts.get(0); // Get the first draft
            jsonResponse.addProperty("draftId", draftProduct.getId().toString());
            jsonResponse.addProperty("draftName", StringUtils.isNotBlank(draftProduct.getName()) ?
                    draftProduct.getName() : "Prodotto senza nome");
        }

        response.type("application/json");
        return jsonResponse.toString();
    };

    public static Route be_product_delete_draft = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
                RoutesPermission.PRODUCT_MANAGEMENT.getCode(), PermissionType.DELETE);

        // Delete all draft products for this user
        List<Bson> filters = new ArrayList<>();
        filters.add(Filters.eq("createdByUserId", user.getId()));
        filters.add(Filters.eq("status", StatusType.DRAFT.name()));

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 1, null, null);
        List<Product> draftProducts = BaseDao.getDocumentsByFilters(Product.class, queryOptions);

        for (Product draftProduct : draftProducts) {
            BaseDao.deleteDocument(draftProduct, user);
            BaseDao.insertLog(user, draftProduct, LogType.DELETE);
        }

        JsonObject jsonResponse = new JsonObject();
        jsonResponse.addProperty("deleted", draftProducts.size());

        response.type("application/json");
        return jsonResponse.toString();
    };

    public static Route be_product_add_warranty = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
                RoutesPermission.PRODUCT_MANAGEMENT.getCode(), PermissionType.EDIT);

        JSONObject jsonResponse = new JSONObject();

        try {
            // Get parameters
            ObjectId productId = RequestUtils.toObjectId(params.get("productId"));
            ObjectId warrantyId = RequestUtils.toObjectId(params.get("warrantyId"));

            if (productId == null) {
                response.status(400);
                jsonResponse.put("success", false);
                jsonResponse.put("message", "ID prodotto mancante o non valido");
                response.type("application/json");
                return jsonResponse.toString();
            }

            if (warrantyId == null) {
                response.status(400);
                jsonResponse.put("success", false);
                jsonResponse.put("message", "ID garanzia mancante o non valido");
                response.type("application/json");
                return jsonResponse.toString();
            }

            // Load product
            Product product = BaseDao.getDocumentById(productId, Product.class);
            if (product == null) {
                response.status(404);
                jsonResponse.put("success", false);
                jsonResponse.put("message", "Prodotto non trovato");
                response.type("application/json");
                return jsonResponse.toString();
            }

            // Verify warranty exists
            Warranty warranty = BaseDao.getDocumentById(warrantyId, Warranty.class);
            if (warranty == null) {
                response.status(404);
                jsonResponse.put("success", false);
                jsonResponse.put("message", "Garanzia non trovata");
                response.type("application/json");
                return jsonResponse.toString();
            }

            // Initialize warrantyIds list if null
            if (product.getWarrantyIds() == null) {
                product.setWarrantyIds(new ArrayList<>());
            }

            // Check if warranty is already associated
            if (product.getWarrantyIds().contains(warrantyId)) {
                response.status(400);
                jsonResponse.put("success", false);
                jsonResponse.put("message", "Garanzia già associata al prodotto");
                response.type("application/json");
                return jsonResponse.toString();
            }

            // Add warranty to product
            product.getWarrantyIds().add(warrantyId);

            // Update product
            BaseDao.updateDocument(product, user);
            BaseDao.insertLog(user, product, LogType.UPDATE);

            // Return success response
            jsonResponse.put("success", true);
            jsonResponse.put("message", "Garanzia associata al prodotto con successo");
            jsonResponse.put("productId", productId.toString());
            jsonResponse.put("warrantyId", warrantyId.toString());

        } catch (Exception e) {
            LOGGER.error("Error adding warranty to product", e);
            response.status(500);
            jsonResponse.put("success", false);
            jsonResponse.put("message", "Errore interno del server: " + e.getMessage());
        }

        response.type("application/json");
        return jsonResponse.toString();
    };

    public static Route be_product_warranties = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
                RoutesPermission.PRODUCT_MANAGEMENT.getCode(), PermissionType.VIEW);

        JSONObject jsonResponse = new JSONObject();

        try {
            // Get parameters
            ObjectId productId = RequestUtils.toObjectId(params.get("productId"));

            if (productId == null) {
                response.status(400);
                jsonResponse.put("success", false);
                jsonResponse.put("message", "ID prodotto mancante o non valido");
                response.type("application/json");
                return jsonResponse.toString();
            }

            // Load product
            Product product = BaseDao.getDocumentById(productId, Product.class);
            if (product == null) {
                response.status(404);
                jsonResponse.put("success", false);
                jsonResponse.put("message", "Prodotto non trovato");
                response.type("application/json");
                return jsonResponse.toString();
            }

            // Get warranty entries
            List<WarrantyEntry> warrantyEntries = new ArrayList<>();
            if (product.getWarrantyIds() != null && !product.getWarrantyIds().isEmpty()) {
                for (ObjectId warrantyId : product.getWarrantyIds()) {
                    Warranty warranty = BaseDao.getDocumentById(warrantyId, Warranty.class);
                    if (warranty != null) {
                        WarrantyEntry entry = WarrantyCommons.toEntry(warranty);
                        if (entry != null) {
                            warrantyEntries.add(entry);
                        }
                    }
                }
            }

            // Return success response
            jsonResponse.put("success", true);
            jsonResponse.put("warranties", Core.serializeToJson(warrantyEntries));
            jsonResponse.put("count", warrantyEntries.size());

        } catch (Exception e) {
            LOGGER.error("Error loading product warranties", e);
            response.status(500);
            jsonResponse.put("success", false);
            jsonResponse.put("message", "Errore interno del server: " + e.getMessage());
        }

        response.type("application/json");
        return jsonResponse.toString();
    };

    public static Route be_product_remove_warranty = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
                RoutesPermission.PRODUCT_MANAGEMENT.getCode(), PermissionType.EDIT);

        JSONObject jsonResponse = new JSONObject();

        try {
            // Get parameters
            ObjectId productId = RequestUtils.toObjectId(params.get("productId"));
            ObjectId warrantyId = RequestUtils.toObjectId(params.get("warrantyId"));

            if (productId == null) {
                response.status(400);
                jsonResponse.put("success", false);
                jsonResponse.put("message", "ID prodotto mancante o non valido");
                response.type("application/json");
                return jsonResponse.toString();
            }

            if (warrantyId == null) {
                response.status(400);
                jsonResponse.put("success", false);
                jsonResponse.put("message", "ID garanzia mancante o non valido");
                response.type("application/json");
                return jsonResponse.toString();
            }

            // Load product
            Product product = BaseDao.getDocumentById(productId, Product.class);
            if (product == null) {
                response.status(404);
                jsonResponse.put("success", false);
                jsonResponse.put("message", "Prodotto non trovato");
                response.type("application/json");
                return jsonResponse.toString();
            }

            // Check if product has warranties
            if (product.getWarrantyIds() == null || product.getWarrantyIds().isEmpty()) {
                response.status(400);
                jsonResponse.put("success", false);
                jsonResponse.put("message", "Il prodotto non ha garanzie associate");
                response.type("application/json");
                return jsonResponse.toString();
            }

            // Check if warranty is associated with product
            if (!product.getWarrantyIds().contains(warrantyId)) {
                response.status(400);
                jsonResponse.put("success", false);
                jsonResponse.put("message", "Garanzia non associata al prodotto");
                response.type("application/json");
                return jsonResponse.toString();
            }

            // Remove warranty from product
            product.getWarrantyIds().remove(warrantyId);

            // Update product
            BaseDao.updateDocument(product, user);
            BaseDao.insertLog(user, product, LogType.UPDATE);

            // Return success response
            jsonResponse.put("success", true);
            jsonResponse.put("message", "Garanzia rimossa dal prodotto con successo");
            jsonResponse.put("productId", productId.toString());
            jsonResponse.put("warrantyId", warrantyId.toString());

        } catch (Exception e) {
            LOGGER.error("Error removing warranty from product", e);
            response.status(500);
            jsonResponse.put("success", false);
            jsonResponse.put("message", "Errore interno del server: " + e.getMessage());
        }

        response.type("application/json");
        return jsonResponse.toString();
    };

}
