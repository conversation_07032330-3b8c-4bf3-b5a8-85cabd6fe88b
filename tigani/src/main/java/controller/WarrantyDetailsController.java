package controller;

import com.mongodb.client.model.Filters;
import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import dao.WarrantyDao;
import enums.LogType;
import enums.ProfileType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.WarrantyDetails;
import pojo.User;
import spark.*;
import utils.*;

import java.util.*;
import org.bson.conversions.Bson;
import pojo.QueryOptions;

/**
 *
 * <AUTHOR>
 */
public class WarrantyDetailsController {

    private static final Logger LOGGER = LoggerFactory.getLogger(WarrantyDetailsController.class.getName());

    public static TemplateViewRoute be_warrantydetails_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_WARRANTYDETAILS_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_warrantydetails = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("warrantyDetailsId"));
        if (oid != null) {
            WarrantyDetails loadedWarrantyDetails = BaseDao.getDocumentById(oid, WarrantyDetails.class);
            attributes.put("curWarrantyDetails", loadedWarrantyDetails);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                WarrantyDetails loadedWarrantyDetails = BaseDao.getDocumentByParentId(parentId, WarrantyDetails.class);
                if (loadedWarrantyDetails != null) {
                    attributes.put("curWarrantyDetails", loadedWarrantyDetails);
                }
            }
        }

        // check if warrantyId is present in query params and add it to attributes
        ObjectId warrantyId = RequestUtils.toObjectId(request.queryParams("warrantyId"));
        if (warrantyId != null) {
            attributes.put("warrantyId", warrantyId);
        }

        return Core.render(Pages.BE_WARRANTYDETAILS, attributes, request);
    };

    public static Route be_warrantydetails_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<WarrantyDetails> loadedWarrantyDetails;
        List<Bson> filters = new ArrayList<>();

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);

        if (loadArchived) {
            loadedWarrantyDetails = BaseDao.getDocumentsByFilters(WarrantyDetails.class, queryOptions, loadArchived);
        } else {
            loadedWarrantyDetails = BaseDao.getDocumentsByFilters(WarrantyDetails.class, queryOptions);
        }
        
        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (!loadedWarrantyDetails.isEmpty()) {
            for (WarrantyDetails tmpWarrantyDetails : loadedWarrantyDetails) {
                json.append("[");
                json.append("\"").append("\","); // prima colonna vuota
                json.append("\"<a target='_blank' warrantyDetailsId='").append(tmpWarrantyDetails.getId()).append("' href='").append(RoutesUtils.contextPath(request)).append(Routes.BE_WARRANTYDETAILS).append("?warrantyDetailsId=").append(tmpWarrantyDetails.getId()).append("'>").append(tmpWarrantyDetails.getId()).append("</a>\",");

                // Handle province codes as list
                String provinceCodesDisplay = "N.D.";
                if (tmpWarrantyDetails.getProvinceCode() != null && !tmpWarrantyDetails.getProvinceCode().isEmpty()) {
                    provinceCodesDisplay = StringUtils.join(tmpWarrantyDetails.getProvinceCode(), ", ");
                }
                json.append("\"").append(provinceCodesDisplay).append("\",");

                // Handle claim number range display
                String claimNumberDisplay = "N.D.";
                if (tmpWarrantyDetails.getClaimNumber() != null && !tmpWarrantyDetails.getClaimNumber().isEmpty()) {
                    Integer min = tmpWarrantyDetails.getClaimNumberMin();
                    Integer max = tmpWarrantyDetails.getClaimNumberMax();
                    if (min.equals(max)) {
                        claimNumberDisplay = min.toString();
                    } else {
                        claimNumberDisplay = min + " - " + max;
                    }
                }
                json.append("\"").append(claimNumberDisplay).append("\",");

                // Handle universal class range display
                String universalClassDisplay = "N.D.";
                if (tmpWarrantyDetails.getUniversalClass() != null && !tmpWarrantyDetails.getUniversalClass().isEmpty()) {
                    Integer min = tmpWarrantyDetails.getUniversalClassMin();
                    Integer max = tmpWarrantyDetails.getUniversalClassMax();
                    if (min.equals(max)) {
                        universalClassDisplay = min.toString();
                    } else {
                        universalClassDisplay = min + " - " + max;
                    }
                }
                json.append("\"").append(universalClassDisplay).append("\",");
                json.append("\"").append(tmpWarrantyDetails.getPremiumValue() != null ? tmpWarrantyDetails.getPremiumValue().toString() : "N.D.").append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(tmpWarrantyDetails.getCreation(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(tmpWarrantyDetails.getLastUpdate(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append("Azioni").append("\",");
                json.append("\"").append("\""); // ultima colonna vuota
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovo ultima virgola array
        }
        json.append("]}");

        return json.toString();
    };

    public static Route be_warrantydetails_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("warrantyDetailsId"));
        WarrantyDetails newWarrantyDetails;
        if (oid != null) {
            newWarrantyDetails = BaseDao.getDocumentById(oid, WarrantyDetails.class);
            RequestUtils.mergeFromParams(params, newWarrantyDetails);
        } else {
            newWarrantyDetails = RequestUtils.createFromParams(params, WarrantyDetails.class);
        }

        if (newWarrantyDetails != null) {
            // Handle range inputs for claimNumber
            processRangeField(params, newWarrantyDetails, "claimNumber");
            // Handle range inputs for universalClass
            processRangeField(params, newWarrantyDetails, "universalClass");

            // Validate warranty details to prevent duplicates
            if (!isValidWarrantyDetails(newWarrantyDetails)) {
                response.status(400);
                return "Impossibile salvare i dati, esistono già dettagli di garanzia con le stesse impostazioni";
            }

            if (oid == null) {
                oid = BaseDao.insertDocument(newWarrantyDetails);
                newWarrantyDetails.setId(oid);

                BaseDao.insertLog(user, newWarrantyDetails, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newWarrantyDetails, user);
                BaseDao.insertLog(user, newWarrantyDetails, LogType.UPDATE);
            }

            if (!files.isEmpty()) {
                BaseDao.deleteImage(newWarrantyDetails, "imageId");
                BaseDao.saveImage(files.entrySet().iterator().next().getValue(), newWarrantyDetails, "imageId", true);
            }
        }

        // se errore ritorno Spark.halt()
        return oid;
    };

    public static Route be_warrantydetails_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        String warrantyDetailsIds = params.get("warrantyDetailsIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(warrantyDetailsIds)) {
            String[] ids = warrantyDetailsIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    WarrantyDetails tmpWarrantyDetails = BaseDao.getDocumentById(oid, WarrantyDetails.class);
                    if (tmpWarrantyDetails != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(tmpWarrantyDetails, user);
                                BaseDao.insertLog(user, tmpWarrantyDetails, LogType.DELETE);
                                break;
                            case "archive":
                                tmpWarrantyDetails.setArchived(true);
                                BaseDao.updateDocument(tmpWarrantyDetails, user);
                                BaseDao.insertLog(user, tmpWarrantyDetails, LogType.UPDATE);
                                break;
                            case "unarchive":
                                tmpWarrantyDetails.setArchived(false);
                                BaseDao.updateDocument(tmpWarrantyDetails, user);
                                BaseDao.insertLog(user, tmpWarrantyDetails, LogType.UPDATE);
                                break;
                        }
                    }
                }
            }
        }

        return "ok";
    };

    /**
     * Validates that the warranty details don't create a duplicate/conflicting premium
     * for the same warranty combination.
     *
     * @param warrantyDetails The warranty details to validate
     * @return true if valid (no conflicts), false if there are conflicts
     */
    public static boolean isValidWarrantyDetails(WarrantyDetails warrantyDetails) {
        try {
            List<WarrantyDetails> conflictingDetails = WarrantyDao.findConflictingWarrantyDetails(warrantyDetails);

            if (!conflictingDetails.isEmpty()) {
                LOGGER.warn("Found {} conflicting warranty details for warranty ID: {}",
                    conflictingDetails.size(), warrantyDetails.getWarrantyId());

                for (WarrantyDetails conflict : conflictingDetails) {
                    LOGGER.warn("Conflicting detail ID: {} with premium: {}",
                        conflict.getId(), conflict.getPremiumValue());
                }

                return false;
            }

            return true;

        } catch (Exception e) {
            LOGGER.error("Error validating warranty details", e);
            return false;
        }
    }

    private static void processRangeField(Map<String, String> params, WarrantyDetails warrantyDetails, String fieldName) {
        String fromParam = fieldName + "From";
        String toParam = fieldName + "To";

        String fromValue = params.get(fromParam);
        String toValue = params.get(toParam);

        if (StringUtils.isNotBlank(fromValue) && StringUtils.isNotBlank(toValue)) {
            try {
                Integer from = Integer.parseInt(fromValue);
                Integer to = Integer.parseInt(toValue);

                // Ensure from <= to
                if (from > to) {
                    Integer temp = from;
                    from = to;
                    to = temp;
                }

                // Generate range list
                List<Integer> rangeList = new ArrayList<>();
                for (int i = from; i <= to; i++) {
                    rangeList.add(i);
                }

                // Set the appropriate field
                if (StringUtils.equals(fieldName, "claimNumber")) {
                    warrantyDetails.setClaimNumber(rangeList);
                } else if (StringUtils.equals(fieldName, "universalClass")) {
                    warrantyDetails.setUniversalClass(rangeList);
                }

            } catch (NumberFormatException e) {
                LOGGER.error("Error parsing range values for field: " + fieldName, e);
            }
        }
    }
}
