package controller;

import core.Core;
import core.Pages;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.PermissionType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.*;
import spark.*;
import utils.*;

import java.util.*;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.conversions.Bson;
import static com.mongodb.client.model.Filters.*;
import utils.WarrantyCommons;

/**
 *
 * <AUTHOR>
 */
public class WarrantyController {

    private static final Logger LOGGER = LoggerFactory.getLogger(WarrantyController.class.getName());

    public static TemplateViewRoute be_warranty_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.WARRANTY_MANAGEMENT.getCode(), PermissionType.VIEW);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_WARRANTY_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_warranty = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.WARRANTY_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("warrantyId"));
        if (oid != null) {
            Warranty loadedWarranty = BaseDao.getDocumentById(oid, Warranty.class);
            attributes.put("curWarranty", loadedWarranty);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Warranty loadedWarranty = BaseDao.getDocumentByParentId(parentId, Warranty.class);
                if (loadedWarranty != null) {
                    attributes.put("curWarranty", loadedWarranty);
                }
            }
        }

        return Core.render(Pages.BE_WARRANTY, attributes, request);
    };

    public static TemplateViewRoute be_warranty_form = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.WARRANTY_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("warrantyId"));
        if (oid != null) {
            Warranty loadedWarranty = BaseDao.getDocumentById(oid, Warranty.class);
            attributes.put("curWarranty", loadedWarranty);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Warranty loadedWarranty = BaseDao.getDocumentByParentId(parentId, Warranty.class);
                if (loadedWarranty != null) {
                    attributes.put("curWarranty", loadedWarranty);
                }
            }
        }

        // Load warranty types and insurance companies for dropdown selection
        List<WarrantyType> warrantyTypes = BaseDao.getDocumentsByClass(WarrantyType.class);
        attributes.put("warrantyTypes", warrantyTypes);

        List<InsuranceCompany> insuranceCompanies = BaseDao.getDocumentsByClass(InsuranceCompany.class);
        attributes.put("insuranceCompanies", insuranceCompanies);

        // Return only the form content without the base template
        return Core.render(Pages.BE_WARRANTY_FORM, attributes, request);
    };

    public static TemplateViewRoute be_warranty_search_form = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.WARRANTY_MANAGEMENT.getCode(), PermissionType.VIEW);

        // Load sample warranty data for search results (this could be enhanced to load actual search results)
        // For now, we'll just provide the basic attributes needed for the search form

        // Return only the search form content without the base template
        return Core.render(Pages.BE_WARRANTY_SEARCH_FORM, attributes, request);
    };

    public static Route be_warranty_search = (Request request, Response response) -> {
        response.type("application/json");

        try {
            // logged user
            Map<String, Object> attributes = new HashMap<>();
            User user = Core.initializeRoute(request, response, attributes,
                RoutesPermission.WARRANTY_MANAGEMENT.getCode(), PermissionType.VIEW);

            // Get search parameters
            String searchQuery = request.queryParams("q");
            if (StringUtils.isBlank(searchQuery)) {
                return Core.serializeToJson(Map.of("results", new ArrayList<>()));
            }

            // Search warranties by title using regex pattern (case-insensitive)
            List<Bson> filters = new ArrayList<>();
            filters.add(ne("cancelled", true));
            filters.add(ne("archived", true));

            // Add title search filter using regex for partial matching
            filters.add(regex("title", ".*" + searchQuery + ".*", "i"));
            if (request.queryParams("productId") != null) {
                Product product = BaseDao.getDocumentById(RequestUtils.toObjectId(request.queryParams("productId")), Product.class);
                if (product != null && product.getWarrantyIds() != null && !product.getWarrantyIds().isEmpty()) {
                    filters.add(nin("_id", product.getWarrantyIds()));
                }
            }
            QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);
            List<Warranty> warranties = BaseDao.getDocumentsByFilters(Warranty.class, queryOptions, null, false, false);

            // Convert warranties to WarrantyEntry objects with complete related data
            List<WarrantyEntry> warrantyEntries = WarrantyCommons.toEntries(warranties);

            // Build response
            Map<String, Object> jsonResponse = new HashMap<>();
            jsonResponse.put("results", warrantyEntries);
            jsonResponse.put("totalResults", warrantyEntries.size());

            LOGGER.debug("Warranty search for '{}' returned {} results", searchQuery, warrantyEntries.size());

            return Core.serializeToJson(jsonResponse);

        } catch (Exception e) {
            LOGGER.error("Error performing warranty search", e);
            response.status(500);
            return Core.serializeToJson(Map.of("error", "Internal server error"));
        }
    };

    public static Route be_warranty_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.WARRANTY_MANAGEMENT.getCode(), PermissionType.VIEW);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Warranty> loadedWarranties;
        List<Bson> filters = new ArrayList<>();

        // Add date range filtering if parameters are provided
        if (params.containsKey("startDate") && StringUtils.isNotBlank(params.get("startDate"))) {
            Date startDate = DateTimeUtils.stringToDate(params.get("startDate"), "dd/MM/yyyy");
            if (startDate != null) {
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.GTE, startDate));
            }
        }

        if (params.containsKey("endDate") && StringUtils.isNotBlank(params.get("endDate"))) {
            Date endDate = DateTimeUtils.stringToDate(params.get("endDate"), "dd/MM/yyyy");
            if (endDate != null) {
                // Add one day to include the entire end date
                Date endOfDay = DateUtils.addDays(endDate, 1);
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.LTE, endOfDay));
            }
        }

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);

        if (loadArchived) {
            loadedWarranties = BaseDao.getDocumentsByFilters(Warranty.class, queryOptions, loadArchived);
        } else {
            loadedWarranties = BaseDao.getDocumentsByFilters(Warranty.class, queryOptions);
        }

        // Load warranty types and insurance companies for display purposes
        Map<ObjectId, WarrantyType> warrantyTypeMap = new HashMap<>();
        List<WarrantyType> warrantyTypes = BaseDao.getDocumentsByClass(WarrantyType.class);
        for (WarrantyType warrantyType : warrantyTypes) {
            warrantyTypeMap.put(warrantyType.getId(), warrantyType);
        }

        Map<ObjectId, InsuranceCompany> insuranceCompanyMap = new HashMap<>();
        List<InsuranceCompany> insuranceCompanies = BaseDao.getDocumentsByClass(InsuranceCompany.class);
        for (InsuranceCompany company : insuranceCompanies) {
            insuranceCompanyMap.put(company.getId(), company);
        }

        // Build response using JsonObject instead of manual String concatenation
        Map<String, Object> jsonResponse = new HashMap<>();
        List<List<String>> dataRows = new ArrayList<>();

        if (!loadedWarranties.isEmpty()) {
            for (Warranty tmpWarranty : loadedWarranties) {
                List<String> row = new ArrayList<>();
                row.add(tmpWarranty.getId().toString()); // ID for row identification

                // Title con link
                String titleLink = "<a class='text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline' warrantyId='" +
                    tmpWarranty.getId() + "'>" +
                    StringUtils.defaultIfBlank(tmpWarranty.getTitle(), "N.D.") + "</a>";
                row.add(titleLink);

                row.add(StringUtils.defaultIfBlank(tmpWarranty.getCode(), "N.D."));
                row.add(StringUtils.defaultIfBlank(tmpWarranty.getDescription(), "N.D."));

                // Warranty Type name
                String warrantyTypeName = "N.D.";
                if (tmpWarranty.getWarrantyTypeId() != null && warrantyTypeMap.containsKey(tmpWarranty.getWarrantyTypeId())) {
                    warrantyTypeName = warrantyTypeMap.get(tmpWarranty.getWarrantyTypeId()).getName();
                }
                row.add(warrantyTypeName);

                row.add(DateTimeUtils.dateToString(tmpWarranty.getCreation(), "dd/MM/YYYY"));
                row.add(DateTimeUtils.dateToString(tmpWarranty.getLastUpdate(), "dd/MM/YYYY"));
                row.add("Azioni");

                dataRows.add(row);
            }
        }

        jsonResponse.put("data", dataRows);
        return Core.serializeToJson(jsonResponse);
    };

    public static Route be_warranty_save = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("warrantyId"));

        // Determine if this is create or edit operation
        PermissionType requiredPermission = (oid != null) ? PermissionType.EDIT : PermissionType.CREATE;

        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.WARRANTY_MANAGEMENT.getCode(), requiredPermission);

        Warranty newWarranty;
        if (oid != null) {
            newWarranty = BaseDao.getDocumentById(oid, Warranty.class);
            RequestUtils.mergeFromParams(params, newWarranty);
        } else {
            newWarranty = RequestUtils.createFromParams(params, Warranty.class);
        }

        if (newWarranty != null) {
            if (oid == null) {
                oid = BaseDao.insertDocument(newWarranty);
                newWarranty.setId(oid);

                BaseDao.insertLog(user, newWarranty, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newWarranty, user);
                BaseDao.insertLog(user, newWarranty, LogType.UPDATE);
            }
        }

        // se errore ritorno Spark.halt()
        return oid;
    };

    public static Route be_warranty_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        PermissionType requiredPermission = PermissionType.EDIT;
        if (StringUtils.equalsIgnoreCase(operation, "delete")) {
            requiredPermission = PermissionType.DELETE;
        }

        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.WARRANTY_MANAGEMENT.getCode(), requiredPermission);

        String warrantyIds = params.get("warrantyIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(warrantyIds)) {
            String[] ids = warrantyIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    Warranty tmpWarranty = BaseDao.getDocumentById(oid, Warranty.class);
                    if (tmpWarranty != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(tmpWarranty, user);
                                BaseDao.insertLog(user, tmpWarranty, LogType.DELETE);
                                break;
                            case "archive":
                                tmpWarranty.setArchived(true);
                                BaseDao.updateDocument(tmpWarranty, user);
                                BaseDao.insertLog(user, tmpWarranty, LogType.UPDATE);
                                break;
                            case "unarchive":
                                tmpWarranty.setArchived(false);
                                BaseDao.updateDocument(tmpWarranty, user);
                                BaseDao.insertLog(user, tmpWarranty, LogType.UPDATE);
                                break;
                        }
                    }
                }
            }
        }

        return "ok";
    };
}
