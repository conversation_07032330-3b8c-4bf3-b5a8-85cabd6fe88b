package controller;

import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import dao.BusinessDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.StatusType;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.*;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.TemplateViewRoute;
import utils.Defaults;
import utils.RoutesUtils;

/**
 *
 * <AUTHOR>
 */
public class HomeController {

    private static final Logger LOGGER = LoggerFactory.getLogger(HomeController.class.getName());

    public static Route error_page = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        response.redirect(RoutesUtils.contextPath(request) + Routes.ERROR_404);
        return "ok";
    };

    public static TemplateViewRoute error_404 = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.ERROR_404, attributes, request);
    };
    
}
