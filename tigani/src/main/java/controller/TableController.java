package controller;

import core.Core;
import core.Pages;
import enums.ProfileType;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class TableController {

    private static final Logger LOGGER = LoggerFactory.getLogger(TableController.class.getName());

    public static TemplateViewRoute be_table_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);        

        return Core.render(Pages.BE_TABLE_COLLECTION, attributes, request);
    };
   
}
