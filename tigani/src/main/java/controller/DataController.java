package controller;

import com.mongodb.client.model.Filters;
import core.Core;
import core.Routes;
import dao.BaseDao;
import dao.DaoFilters;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.*;
import spark.Request;
import spark.Response;
import spark.Route;
import utils.RequestUtils;
import utils.RoutesUtils;
import utils.TinUtils;
import utils.TimeUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * DataController provides AJAX endpoints for populating select fields
 * with remote data from Country, City, Province entities.
 * Supports search functionality and pagination for Preline UI Advanced Select.
 *
 * <AUTHOR>
 */
public class DataController {

    private static final Logger LOGGER = LoggerFactory.getLogger(DataController.class.getName());

    /**
     * Endpoint for Country data with search and pagination support.
     * Returns JSON format compatible with Preline UI Advanced Select.
     *
     * Parameters:
     * - q: search query (searches in description field)
     * - page: page number (default: 1)
     * - limit: items per page (default: 20)
     * - selected: preselected value that must be included in first page results
     */
    public static Route be_data_countries = (Request request, Response response) -> {
        response.type("application/json");

        try {
            // Parse request parameters
            Map<String, String> params = new LinkedHashMap<>();
            RequestUtils.parseRequest(request, params, null);

            String searchQuery = params.getOrDefault("q", "");
            String selected = params.getOrDefault("selected", "");
            int page = Integer.parseInt(params.getOrDefault("page", "1"));
            int limit = Integer.parseInt(params.getOrDefault("limit", "20"));
            int skip = (page - 1) * limit;

            LOGGER.debug("Country data request - query: '{}', selected: '{}', page: {}, limit: {}",
                    searchQuery, selected, page, limit);

            List<Country> countries = new ArrayList<>();
            Set<String> addedCodes = new HashSet<>(); // Track added items to avoid duplicates

            // Strategy: For first page with selected value, ensure selected is included
            if (page == 1 && StringUtils.isNotBlank(selected) && StringUtils.isBlank(searchQuery)) {
                // First, try to get the selected country by code (primary key)
                List<Bson> selectedFilters = Arrays.asList(Filters.eq("code", selected));
                QueryOptions selectedQuery = DaoFilters.createQueryWithOptions(
                        selectedFilters, 0, 1, "description", "asc");
                List<Country> selectedCountries = BaseDao.getDocumentsByFilters(Country.class, selectedQuery, null, false, false);

                // If not found by code, try by description
                if (selectedCountries.isEmpty()) {
                    selectedFilters = Arrays.asList(Filters.eq("description", selected));
                    selectedQuery = DaoFilters.createQueryWithOptions(
                            selectedFilters, 0, 1, "description", "asc");
                    selectedCountries = BaseDao.getDocumentsByFilters(Country.class, selectedQuery, null, false, false);
                }

                // Add selected country first
                for (Country country : selectedCountries) {
                    countries.add(country);
                    addedCodes.add(country.getCode());
                }
            }

            // Calculate remaining slots for search results
            int remainingLimit = limit - countries.size();

            if (remainingLimit > 0) {
                // Build search filters
                List<Bson> searchFilters = new ArrayList<>();

                if (StringUtils.isNotBlank(searchQuery)) {
                    searchFilters.add(Filters.regex("description", "(?i).*" + searchQuery.trim() + ".*"));
                }

                // Exclude already added countries to avoid duplicates
                if (!addedCodes.isEmpty()) {
                    searchFilters.add(Filters.nin("code", addedCodes));
                }

                // Create query for remaining results
                QueryOptions searchQueryOptions = DaoFilters.createQueryWithOptions(
                        searchFilters, skip, remainingLimit, "description", "asc");

                List<Country> searchResults = BaseDao.getDocumentsByFilters(Country.class, searchQueryOptions, null, false, false);
                countries.addAll(searchResults);
            }

            // Build response in Preline UI format
            Map<String, Object> jsonResponse = new HashMap<>();
            List<Map<String, Object>> results = new ArrayList<>();

            for (Country country : countries) {
                Map<String, Object> item = new HashMap<>();
                item.put("id", country.getCode());  // Use code as ID
                item.put("text", country.getDescription());  // Use description as display text
                item.put("thumbnail", RoutesUtils.contextPath(request) + "/img/lang/" + country.getCode().toLowerCase() + ".svg");
                results.add(item);
            }

            jsonResponse.put("results", results);

            // Add pagination info for infinite scroll support
            boolean hasMore = countries.size() == limit;  // If we got full page, there might be more
            jsonResponse.put("pagination", Map.of("more", hasMore));

            LOGGER.debug("Returning {} countries for query '{}' (selected: '{}')", results.size(), searchQuery, selected);

            return Core.serializeToJson(jsonResponse);

        } catch (Exception e) {
            LOGGER.error("Error retrieving country data", e);
            response.status(500);
            return Core.serializeToJson(Map.of("error", "Internal server error"));
        }
    };

    /**
     * Endpoint for City data with search and pagination support.
     * Returns JSON format compatible with Preline UI Advanced Select.
     *
     * Parameters:
     * - q: search query (searches in name field)
     * - page: page number (default: 1)
     * - limit: items per page (default: 20)
     * - selected: preselected value that must be included in first page results
     * - provinceCode: optional filter by province code
     * - countryCode: optional filter by country code
     */
    public static Route be_data_cities = (Request request, Response response) -> {
        response.type("application/json");

        try {
            // Parse request parameters
            Map<String, String> params = new LinkedHashMap<>();
            RequestUtils.parseRequest(request, params, null);

            String searchQuery = params.getOrDefault("q", "");
            String selected = params.getOrDefault("selected", "");
            int page = Integer.parseInt(params.getOrDefault("page", "1"));
            int limit = Integer.parseInt(params.getOrDefault("limit", "20"));
            int skip = (page - 1) * limit;
            String provinceCode = params.get("provinceCode");
            String countryCode = params.get("countryCode");

            LOGGER.debug("City data request - query: '{}', selected: '{}', page: {}, limit: {}, provinceCode: '{}', countryCode: '{}'",
                    searchQuery, selected, page, limit, provinceCode, countryCode);

            List<City> cities = new ArrayList<>();
            Set<String> addedNames = new HashSet<>(); // Track added items to avoid duplicates

            // Strategy: For first page with selected value, ensure selected is included
            if (page == 1 && StringUtils.isNotBlank(selected) && StringUtils.isBlank(searchQuery)) {
                // Build filters for selected city
                List<Bson> selectedFilters = new ArrayList<>();
                selectedFilters.add(Filters.eq("name", selected));

                // Apply province/country filters if specified
                if (StringUtils.isNotBlank(provinceCode)) {
                    selectedFilters.add(Filters.eq("provinceCode", provinceCode));
                }
                if (StringUtils.isNotBlank(countryCode)) {
                    selectedFilters.add(Filters.eq("countryCode", countryCode));
                }

                QueryOptions selectedQuery = DaoFilters.createQueryWithOptions(
                        selectedFilters, 0, 1, "name", "asc");
                List<City> selectedCities = BaseDao.getDocumentsByFilters(City.class, selectedQuery, null, false, false);

                // Add selected city first
                for (City city : selectedCities) {
                    cities.add(city);
                    addedNames.add(city.getName());
                }
            }

            // Calculate remaining slots for search results
            int remainingLimit = limit - cities.size();

            if (remainingLimit > 0) {
                // Build search filters
                List<Bson> searchFilters = new ArrayList<>();

                if (StringUtils.isNotBlank(searchQuery)) {
                    searchFilters.add(Filters.regex("name", "(?i).*" + searchQuery.trim() + ".*"));
                }

                // Apply province/country filters
                if (StringUtils.isNotBlank(provinceCode)) {
                    searchFilters.add(Filters.eq("provinceCode", provinceCode));
                }
                if (StringUtils.isNotBlank(countryCode)) {
                    searchFilters.add(Filters.eq("countryCode", countryCode));
                }

                // Exclude already added cities to avoid duplicates
                if (!addedNames.isEmpty()) {
                    searchFilters.add(Filters.nin("name", addedNames));
                }

                // Create query for remaining results
                QueryOptions searchQueryOptions = DaoFilters.createQueryWithOptions(
                        searchFilters, skip, remainingLimit, "name", "asc");

                List<City> searchResults = BaseDao.getDocumentsByFilters(City.class, searchQueryOptions, null, false, false);
                cities.addAll(searchResults);
            }

            // Build response in Preline UI format
            Map<String, Object> jsonResponse = new HashMap<>();
            List<Map<String, Object>> results = new ArrayList<>();

            for (City city : cities) {
                Map<String, Object> item = new HashMap<>();
                item.put("id", city.getName());  // Use name as ID
                // Display format: "CityName (Province)"
                String displayText = city.getName();
                if (StringUtils.isNotBlank(city.getProvince())) {
                    displayText += " (" + city.getProvince() + ")";
                }
                item.put("text", displayText);
                results.add(item);
            }

            jsonResponse.put("results", results);

            // Add pagination info for infinite scroll support
            boolean hasMore = cities.size() == limit;  // If we got full page, there might be more
            jsonResponse.put("pagination", Map.of("more", hasMore));

            LOGGER.debug("Returning {} cities for query '{}' (selected: '{}')", results.size(), searchQuery, selected);

            return Core.serializeToJson(jsonResponse);

        } catch (Exception e) {
            LOGGER.error("Error retrieving city data", e);
            response.status(500);
            return Core.serializeToJson(Map.of("error", "Internal server error"));
        }
    };

    /**
     * Endpoint for Province data with search and pagination support.
     * Returns JSON format compatible with Preline UI Advanced Select.
     *
     * Parameters:
     * - q: search query (searches in description field)
     * - page: page number (default: 1)
     * - limit: items per page (default: 20)
     * - selected: preselected value that must be included in first page results
     */
    public static Route be_data_provinces = (Request request, Response response) -> {
        response.type("application/json");

        try {
            // Parse request parameters
            Map<String, String> params = new LinkedHashMap<>();
            RequestUtils.parseRequest(request, params, null);

            String searchQuery = params.getOrDefault("q", "");
            String selected = params.getOrDefault("selected", "");
            int page = Integer.parseInt(params.getOrDefault("page", "1"));
            int limit = Integer.parseInt(params.getOrDefault("limit", "20"));
            int skip = (page - 1) * limit;

            LOGGER.debug("Province data request - query: '{}', selected: '{}', page: {}, limit: {}",
                    searchQuery, selected, page, limit);

            List<Province> provinces = new ArrayList<>();
            Set<String> addedCodes = new HashSet<>(); // Track added items to avoid duplicates

            // Strategy: For first page with selected value, ensure selected is included
            if (page == 1 && StringUtils.isNotBlank(selected) && StringUtils.isBlank(searchQuery)) {
                // First, try to get the selected province by code (primary key)
                List<Bson> selectedFilters = Arrays.asList(Filters.eq("code", selected));
                QueryOptions selectedQuery = DaoFilters.createQueryWithOptions(
                        selectedFilters, 0, 1, "description", "asc");
                List<Province> selectedProvinces = BaseDao.getDocumentsByFilters(Province.class, selectedQuery, null, false, false);

                // If not found by code, try by description
                if (selectedProvinces.isEmpty()) {
                    selectedFilters = Arrays.asList(Filters.eq("description", selected));
                    selectedQuery = DaoFilters.createQueryWithOptions(
                            selectedFilters, 0, 1, "description", "asc");
                    selectedProvinces = BaseDao.getDocumentsByFilters(Province.class, selectedQuery, null, false, false);
                }

                // Add selected province first
                for (Province province : selectedProvinces) {
                    provinces.add(province);
                    addedCodes.add(province.getCode());
                }
            }

            // Calculate remaining slots for search results
            int remainingLimit = limit - provinces.size();

            if (remainingLimit > 0) {
                // Build search filters
                List<Bson> searchFilters = new ArrayList<>();

                if (StringUtils.isNotBlank(searchQuery)) {
                    searchFilters.add(Filters.regex("description", "(?i).*" + searchQuery.trim() + ".*"));
                }

                // Exclude already added provinces to avoid duplicates
                if (!addedCodes.isEmpty()) {
                    searchFilters.add(Filters.nin("code", addedCodes));
                }

                // Create query for remaining results
                QueryOptions searchQueryOptions = DaoFilters.createQueryWithOptions(
                        searchFilters, skip, remainingLimit, "description", "asc");

                List<Province> searchResults = BaseDao.getDocumentsByFilters(Province.class, searchQueryOptions, null, false, false);
                provinces.addAll(searchResults);
            }

            // Build response in Preline UI format
            Map<String, Object> jsonResponse = new HashMap<>();
            List<Map<String, Object>> results = new ArrayList<>();

            for (Province province : provinces) {
                Map<String, Object> item = new HashMap<>();
                item.put("id", province.getCode());  // Use code as ID
                item.put("text", province.getDescription());  // Use description as display text
                results.add(item);
            }

            jsonResponse.put("results", results);

            // Add pagination info for infinite scroll support
            boolean hasMore = provinces.size() == limit;  // If we got full page, there might be more
            jsonResponse.put("pagination", Map.of("more", hasMore));

            LOGGER.debug("Returning {} provinces for query '{}' (selected: '{}')", results.size(), searchQuery, selected);

            return Core.serializeToJson(jsonResponse);

        } catch (Exception e) {
            LOGGER.error("Error retrieving province data", e);
            response.status(500);
            return Core.serializeToJson(Map.of("error", "Internal server error"));
        }
    };

    /**
     * Endpoint for retrieving a single entity (city, province, or country) by type and value.
     * Returns the entity only if exactly one match is found.
     *
     * Parameters:
     * - entityType: type of entity to search ("city", "province", "country")
     * - value: search value
     *
     * Search behavior:
     * - Province/Country: searches by "code" field (exact match)
     * - City: searches by "name" field (case-insensitive regex) with optional provinceCode/countryCode filters
     */
    public static Route be_data_entity = (Request request, Response response) -> {
        response.type("application/json");

        try {
            // Parse request parameters
            Map<String, String> params = new LinkedHashMap<>();
            RequestUtils.parseRequest(request, params, null);

            String entityType = params.get("entityType");
            String value = params.get("value");

            LOGGER.debug("Entity data request - entityType: '{}', value: '{}'", entityType, value);

            // Validate required parameters
            if (StringUtils.isBlank(entityType)) {
                response.status(400);
                return Core.serializeToJson(Map.of("error", "entityType parameter is required"));
            }

            if (StringUtils.isBlank(value)) {
                response.status(400);
                return Core.serializeToJson(Map.of("error", "value parameter is required"));
            }

            // Switch case for different entity types
            Object result = null;
            switch (entityType.toLowerCase()) {
                case "country":
                    result = searchCountryByCode(value);
                    break;
                case "province":
                    result = searchProvinceByCode(value);
                    break;
                case "city":
                    String provinceCode = params.get("provinceCode");
                    String countryCode = params.get("countryCode");
                    result = searchCityByName(value, provinceCode, countryCode);
                    break;
                default:
                    response.status(400);
                    return Core.serializeToJson(Map.of("error", "Invalid entityType. Allowed values: city, province, country"));
            }

            if (result == null) {
                response.status(404);
                return Core.serializeToJson(Map.of("error", "No entity found"));
            }

            LOGGER.debug("Found entity for entityType '{}' and value '{}'", entityType, value);
            return Core.serializeToJson(result);

        } catch (Exception e) {
            LOGGER.error("Error retrieving entity data", e);
            response.status(500);
            return Core.serializeToJson(Map.of("error", "Internal server error"));
        }
    };

    /**
     * Search for a country by code (exact match).
     * Returns the country if exactly one match is found, null otherwise.
     */
    private static Country searchCountryByCode(String code) throws Exception {
        List<Bson> filters = List.of(Filters.eq("code", code));
        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 2, null, null);

        List<Country> countries = BaseDao.getDocumentsByFilters(Country.class, queryOptions, null, false, false);

        // Return only if exactly one result found
        return (countries != null && countries.size() == 1) ? countries.get(0) : null;
    }

    /**
     * Search for a province by code (exact match).
     * Returns the province if exactly one match is found, null otherwise.
     */
    private static Province searchProvinceByCode(String code) throws Exception {
        List<Bson> filters = List.of(Filters.eq("code", code));
        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 2, null, null);

        List<Province> provinces = BaseDao.getDocumentsByFilters(Province.class, queryOptions, null, false, false);

        // Return only if exactly one result found
        return (provinces != null && provinces.size() == 1) ? provinces.get(0) : null;
    }

    /**
     * Search for a city by name (case-insensitive regex) with optional province and country filters.
     * Returns the city if exactly one match is found, null otherwise.
     */
    private static City searchCityByName(String name, String provinceCode, String countryCode) throws Exception {
        List<Bson> filters = new ArrayList<>();

        // Case-insensitive regex search on name field - using partial match as requested
        filters.add(Filters.regex("name", "(?i).*" + name.trim() + ".*"));

        // Add optional province filter
        if (StringUtils.isNotBlank(provinceCode)) {
            filters.add(Filters.eq("provinceCode", provinceCode));
        }

        // Add optional country filter
        if (StringUtils.isNotBlank(countryCode)) {
            filters.add(Filters.eq("countryCode", countryCode));
        }

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 2, null, null);

        List<City> cities = BaseDao.getDocumentsByFilters(City.class, queryOptions, null, false, false);

        // Return only if exactly one result found
        return (cities != null && cities.size() == 1) ? cities.get(0) : null;
    }

    /**
     * Endpoint for calculating Italian tax code (codice fiscale) based on personal information.
     *
     * Parameters:
     * - firstName: first name (required)
     * - lastName: last name (required)
     * - gender: gender - M for male, F for female (required)
     * - birthDate: birth date in format dd/MM/yyyy (required)
     * - birthCity: birth city name (required)
     *
     * Returns JSON with calculated tax code or error message.
     */
    public static Route be_data_calculate_tin = (Request request, Response response) -> {
        response.type("application/json");

        try {
            // Parse request parameters
            Map<String, String> params = new LinkedHashMap<>();
            RequestUtils.parseRequest(request, params, null);

            String firstName = params.get("firstName");
            String lastName = params.get("lastName");
            String gender = params.get("gender");
            String birthDateStr = params.get("birthDate");
            String birthCity = params.get("birthCity");

            LOGGER.debug("Tax code calculation request - firstName: '{}', lastName: '{}', gender: '{}', birthDate: '{}', birthCity: '{}'",
                    firstName, lastName, gender, birthDateStr, birthCity);

            // Validate required parameters
            List<String> missingFields = new ArrayList<>();

            if (StringUtils.isBlank(firstName)) {
                missingFields.add("Nome");
            }
            if (StringUtils.isBlank(lastName)) {
                missingFields.add("Cognome");
            }
            if (StringUtils.isBlank(gender)) {
                missingFields.add("Genere");
            }
            if (StringUtils.isBlank(birthDateStr)) {
                missingFields.add("Data di nascita");
            }
            if (StringUtils.isBlank(birthCity)) {
                missingFields.add("Città di nascita");
            }

            if (!missingFields.isEmpty()) {
                response.status(400);
                String errorMessage = "Campi mancanti per il calcolo del codice fiscale: " + String.join(", ", missingFields);
                return Core.serializeToJson(Map.of("error", errorMessage, "missingFields", missingFields));
            }

            // Validate gender format
            if (!StringUtils.equalsIgnoreCase(gender, "M") && !StringUtils.equalsIgnoreCase(gender, "F") &&
                    !StringUtils.equalsIgnoreCase(gender, "MALE") && !StringUtils.equalsIgnoreCase(gender, "FEMALE")) {
                response.status(400);
                return Core.serializeToJson(Map.of("error", "Genere non valido. Utilizzare M/F o MALE/FEMALE"));
            }

            // Normalize gender to M/F format expected by TinUtils
            String normalizedGender = (StringUtils.equalsIgnoreCase(gender, "MALE") || StringUtils.equalsIgnoreCase(gender, "M")) ? "M" : "F";

            // Parse birth date
            Date birthDate;
            try {
                birthDate = TimeUtils.toDate(birthDateStr);
                if (birthDate == null) {
                    throw new IllegalArgumentException("Invalid date format");
                }
            } catch (Exception e) {
                response.status(400);
                return Core.serializeToJson(Map.of("error", "Formato data non valido. Utilizzare dd/MM/yyyy"));
            }

            // Find city and get codiceBelfiore
            List<Bson> cityFilters = List.of(Filters.eq("name", birthCity.trim()));
            QueryOptions cityQuery = DaoFilters.createQueryWithOptions(cityFilters, 0, 10, null, null);
            List<City> cities = BaseDao.getDocumentsByFilters(City.class, cityQuery, null, false, false);

            if (cities == null || cities.isEmpty()) {
                response.status(400);
                return Core.serializeToJson(Map.of("error", "Città di nascita non trovata: " + birthCity));
            }

            if (cities.size() > 1) {
                // Multiple cities found, provide list for user to choose
                List<String> cityOptions = new ArrayList<>();
                for (City city : cities) {
                    String displayName = city.getName();
                    if (StringUtils.isNotBlank(city.getProvince())) {
                        displayName += " (" + city.getProvince() + ")";
                    }
                    cityOptions.add(displayName);
                }
                response.status(400);
                return Core.serializeToJson(Map.of(
                        "error", "Trovate più città con questo nome. Specificare la provincia:",
                        "cityOptions", cityOptions
                ));
            }

            City city = cities.get(0);
            String codiceBelfiore = city.getCodiceBelfiore();

            if (StringUtils.isBlank(codiceBelfiore)) {
                response.status(400);
                return Core.serializeToJson(Map.of("error", "Codice Belfiore non disponibile per la città: " + birthCity));
            }

            // Calculate tax code using TinUtils
            String calculatedTin = TinUtils.tin(lastName.trim(), firstName.trim(), normalizedGender, birthDate, codiceBelfiore);

            if (StringUtils.isBlank(calculatedTin)) {
                response.status(500);
                return Core.serializeToJson(Map.of("error", "Errore nel calcolo del codice fiscale"));
            }

            // Return success response
            Map<String, Object> successResponse = new HashMap<>();
            successResponse.put("success", true);
            successResponse.put("taxCode", calculatedTin);
            successResponse.put("message", "Codice fiscale calcolato con successo: " + calculatedTin);

            LOGGER.info("Tax code calculated successfully for {} {}: {}", firstName, lastName, calculatedTin);

            return Core.serializeToJson(successResponse);

        } catch (Exception e) {
            LOGGER.error("Error calculating tax code", e);
            response.status(500);
            return Core.serializeToJson(Map.of("error", "Errore interno del server"));
        }
    };

    /**
     * Endpoint for Brand data with search and pagination support.
     * Returns JSON format compatible with Preline UI Advanced Select.
     *
     * Parameters:
     * - q: search query (searches in description field)
     * - page: page number (default: 1)
     * - limit: items per page (default: 20)
     * - selected: preselected value that must be included in first page results
     */
    public static Route be_data_brands = (Request request, Response response) -> {
        response.type("application/json");

        try {
            // Parse request parameters
            Map<String, String> params = new LinkedHashMap<>();
            RequestUtils.parseRequest(request, params, null);

            String searchQuery = params.getOrDefault("q", "");
            String selected = params.getOrDefault("selected", "");
            int page = Integer.parseInt(params.getOrDefault("page", "1"));
            int limit = Integer.parseInt(params.getOrDefault("limit", "20"));
            int skip = (page - 1) * limit;

            LOGGER.debug("Brand data request - query: '{}', selected: '{}', page: {}, limit: {}",
                    searchQuery, selected, page, limit);

            List<Brand> brands = new ArrayList<>();
            Set<String> addedCodes = new HashSet<>(); // Track added items to avoid duplicates

            // Strategy: For first page with selected value, ensure selected is included
            if (page == 1 && StringUtils.isNotBlank(selected) && StringUtils.isBlank(searchQuery)) {
                // First, try to get the selected brand by id (primary key)
                // check if selected contains a comma. If so, split and load all
                List<String> selectedValues = Arrays.asList(StringUtils.split(selected, ","));
                List<ObjectId> selectedIds = selectedValues.stream()
                        .map(RequestUtils::toObjectId)
                        .collect(Collectors.toList());
                List<Bson> selectedFilters = Arrays.asList(Filters.in("_id", selectedIds));
                QueryOptions selectedQuery = DaoFilters.createQueryWithOptions(
                        selectedFilters, 0, selectedValues.size(), "descrizione", "asc");
                List<Brand> selectedBrands = BaseDao.getDocumentsByFilters(Brand.class, selectedQuery, null, false, false);

                // If not found by codice, try by descrizione
                /*if (selectedBrands.isEmpty()) {
                    selectedFilters = Arrays.asList(Filters.in("descrizione", selectedValues));
                    selectedQuery = DaoFilters.createQueryWithOptions(
                            selectedFilters, 0, selectedValues.size(), "descrizione", "asc");
                    selectedBrands = BaseDao.getDocumentsByFilters(Brand.class, selectedQuery, null, false, false);
                }*/

                // Add selected brand first
                for (Brand brand : selectedBrands) {
                    brands.add(brand);
                    addedCodes.add(brand.getCodice());
                }
            }

            // Calculate remaining slots for search results
            int remainingLimit = limit - brands.size();

            if (remainingLimit > 0) {
                // Build search filters
                List<Bson> searchFilters = new ArrayList<>();

                if (StringUtils.isNotBlank(searchQuery)) {
                    searchFilters.add(Filters.regex("descrizione", "(?i).*" + searchQuery.trim() + ".*"));
                }

                // Exclude already added brands to avoid duplicates
                if (!addedCodes.isEmpty()) {
                    searchFilters.add(Filters.nin("codice", addedCodes));
                }

                // Create query for remaining results
                QueryOptions searchQueryOptions = DaoFilters.createQueryWithOptions(
                        searchFilters, skip, remainingLimit, "descrizione", "asc");

                List<Brand> searchResults = BaseDao.getDocumentsByFilters(Brand.class, searchQueryOptions, null, false, false);
                brands.addAll(searchResults);
            }

            // Build response in Preline UI format
            Map<String, Object> jsonResponse = new HashMap<>();
            List<Map<String, Object>> results = new ArrayList<>();

            for (Brand brand : brands) {
                Map<String, Object> item = new HashMap<>();
                item.put("id", brand.getId().toString());
                item.put("text", brand.getDescrizione());  // Use descrizione as display text
                item.put("thumbnail", RoutesUtils.contextPath(request) + Routes.BE_IMAGE + "?oid=" + brand.getImageId());
                results.add(item);
            }

            jsonResponse.put("results", results);

            // Add pagination info for infinite scroll support
            boolean hasMore = brands.size() == limit;  // If we got full page, there might be more
            jsonResponse.put("pagination", Map.of("more", hasMore));

            LOGGER.debug("Returning {} brands for query '{}' (selected: '{}')", results.size(), searchQuery, selected);

            return Core.serializeToJson(jsonResponse);

        } catch (Exception e) {
            LOGGER.error("Error retrieving brand data", e);
            response.status(500);
            return Core.serializeToJson(Map.of("error", "Internal server error"));
        }
    };
}
