package controller;

import com.mongodb.client.model.Filters;
import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.PermissionType;
import enums.ProfileType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Permission;
import pojo.RoutesPermission;
import pojo.User;
import pojo.UserPermission;
import spark.*;
import utils.*;

import java.util.*;
import org.bson.conversions.Bson;
import pojo.QueryOptions;

/**
 * Controller for managing user permissions and system permission initialization
 *
 * <AUTHOR>
 */
public class PermissionController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PermissionController.class.getName());

    /**
     * User Permissions Manager Page - dedicated page for managing user permissions
     */
    public static TemplateViewRoute be_user_permissions_manager = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
                RoutesPermission.PERMISSION_MANAGEMENT.getCode(), PermissionType.EDIT);

        return Core.render(Pages.BE_USER_PERMISSIONS_MANAGER, attributes, request);
    };

    /**
     * Get all users for the permissions manager dropdown
     */
    public static Route be_user_permissions_manager_users = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
                RoutesPermission.PERMISSION_MANAGEMENT.getCode(), PermissionType.VIEW);

        try {
            // Get all users non system
            List<Bson> filters = new ArrayList<>();
            filters.add(DaoFilters.getFilter("profileType", DaoFiltersOperation.NE, ProfileType.SYSTEM.name().toLowerCase()));
            QueryOptions query = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);
            List<User> users = BaseDao.getDocumentsByFilters(User.class, query, null, false, false);

            StringBuilder json = new StringBuilder();
            json.append("{\"users\":[");
            boolean first = true;
            for (User u : users) {
                if (!first) {
                    json.append(",");
                }
                first = false;

                json.append("{");
                json.append("\"id\":\"").append(u.getId()).append("\",");
                json.append("\"name\":\"").append(StringUtils.defaultString(u.getName())).append("\",");
                json.append("\"lastname\":\"").append(StringUtils.defaultString(u.getLastname())).append("\",");
                json.append("\"email\":\"").append(StringUtils.defaultString(u.getEmail())).append("\",");
                json.append("\"profileType\":\"").append(StringUtils.defaultString(u.getProfileType())).append("\"");
                json.append("}");
            }
            json.append("]}");

            return json.toString();
        } catch (Exception ex) {
            LOGGER.error("Error loading users for permissions manager", ex);
            return "{\"error\": \"" + ex.getMessage() + "\"}";
        }
    };

    /**
     * Get user permissions for permission assignment interface
     */
    public static Route be_user_permissions = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.PERMISSION_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId userId = RequestUtils.toObjectId(request.queryParams("userId"));
        if (userId == null) {
            return "{\"error\": \"User ID required\"}";
        }

        try {
            // Get all available permissions
            Permission[] allPermissions = RoutesPermission.getAllPermissions();

            // Get user's current permissions
            List<Bson> filters = new ArrayList<>();
            filters.add(DaoFilters.getFilter("userId", DaoFiltersOperation.EQ, userId));
            QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);
            List<UserPermission> userPermissions = BaseDao.getDocumentsByFilters(UserPermission.class, queryOptions, null, false, false);
            Map<String, List<String>> userPermissionMap = new HashMap<>();

            for (UserPermission userPermission : userPermissions) {
                if (userPermission.getUserId() != null && userPermission.getUserId().equals(userId)) {
                    userPermissionMap.computeIfAbsent(userPermission.getPermissionCode(), k -> new ArrayList<>())
                        .add(userPermission.getPermissionType());
                }
            }

            StringBuilder json = new StringBuilder();
            json.append("{\"permissions\":[");
            boolean first = true;
            for (Permission permission : allPermissions) {
                if (!first) {
                    json.append(",");
                }
                first = false;

                List<String> userTypes = userPermissionMap.getOrDefault(permission.getCode(), new ArrayList<>());

                json.append("{");
                json.append("\"code\":\"").append(StringUtils.defaultString(permission.getCode())).append("\",");
                json.append("\"name\":\"").append(StringUtils.defaultString(permission.getName())).append("\",");
                json.append("\"description\":\"").append(StringUtils.defaultString(permission.getDescription())).append("\",");
                json.append("\"userPermissions\":{");
                json.append("\"view\":").append(userTypes.contains("view")).append(",");
                json.append("\"create\":").append(userTypes.contains("create")).append(",");
                json.append("\"edit\":").append(userTypes.contains("edit")).append(",");
                json.append("\"delete\":").append(userTypes.contains("delete"));
                json.append("}}");
            }
            json.append("]}");

            return json.toString();
        } catch (Exception ex) {
            LOGGER.error("Error loading user permissions", ex);
            return "{\"error\": \"" + ex.getMessage() + "\"}";
        }
    };

    /**
     * Save user permissions
     */
    public static Route be_user_permissions_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.PERMISSION_MANAGEMENT.getCode(), PermissionType.EDIT);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        ObjectId userId = RequestUtils.toObjectId(params.get("userId"));
        if (userId == null) {
            return "{\"error\": \"User ID required\"}";
        }

        try {
            // Delete existing user permissions
            List<Bson> filters = new ArrayList<>();
            filters.add(DaoFilters.getFilter("userId", DaoFiltersOperation.EQ, userId));
            QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);
            List<UserPermission> existingPermissions = BaseDao.getDocumentsByFilters(UserPermission.class, queryOptions, null, false, false);
            for (UserPermission existing : existingPermissions) {
                if (existing.getUserId() != null && existing.getUserId().equals(userId)) {
                    BaseDao.deleteDocument(existing, user);
                }
            }

            // Add new permissions
            int savedCount = 0;
            for (String key : params.keySet()) {
                if (key.startsWith("permission|")) {
                    String[] parts = key.split("\\|");
                    if (parts.length == 3) {
                        String permissionCode = parts[1];
                        String permissionType = parts[2];

                        if (StringUtils.equals(params.get(key), "true")) {
                            UserPermission userPermission = new UserPermission();
                            userPermission.setUserId(userId);
                            userPermission.setPermissionCode(permissionCode);
                            userPermission.setPermissionType(permissionType);

                            BaseDao.insertDocument(userPermission);
                            BaseDao.insertLog(user, userPermission, LogType.INSERT);
                            savedCount++;
                        }
                    }
                }
            }

            return "{\"success\": true, \"message\": \"Saved " + savedCount + " permissions\"}";
        } catch (Exception ex) {
            LOGGER.error("Error saving user permissions", ex);
            return "{\"error\": \"" + ex.getMessage() + "\"}";
        }
    };
}
