package enums;

/**
 *
 * <AUTHOR>
 */
public enum CriteriaType {

    PROVINCE("provinceCode", "Provincia"),
    CLAIM_NUMBER("claimNumber", "Numero Sinistri"),
    INSURANCE_PROVENANCE_TYPE("insuranceProvenanceTypeId", "Tipo Provenienza Assicurativa"),
    UNIVERSAL_CLASS("universalClass", "Classe Universale");

    private final String code;
    private final String label;

    CriteriaType(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }
}
