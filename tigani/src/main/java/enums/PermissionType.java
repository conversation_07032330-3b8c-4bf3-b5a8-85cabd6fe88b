package enums;

import org.apache.commons.lang.StringUtils;

/**
 * Enum representing different types of permissions that can be granted to users
 * for accessing specific resources or performing specific actions.
 * 
 * <AUTHOR>
 */
public enum PermissionType {

    VIEW("view", "View"),
    CREATE("create", "Create"),
    EDIT("edit", "Edit"),
    DELETE("delete", "Delete");
    
    private final String code;
    private final String label;

    PermissionType(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }
    
    /**
     * Get PermissionType by code string
     * @param code The permission type code
     * @return PermissionType or null if not found
     */
    public static PermissionType getByCode(String code) {
        for (PermissionType type : PermissionType.values()) {
            if (StringUtils.equalsIgnoreCase(type.getCode(), code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * Get PermissionType by name string
     * @param name The permission type name
     * @return PermissionType or null if not found
     */
    public static PermissionType getByName(String name) {
        for (PermissionType type : PermissionType.values()) {
            if (StringUtils.equalsIgnoreCase(type.name(), name)) {
                return type;
            }
        }
        return null;
    }
}
