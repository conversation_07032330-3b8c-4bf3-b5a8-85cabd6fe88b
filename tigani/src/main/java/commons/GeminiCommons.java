package commons;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import utils.Defaults;
import utils.HttpUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Common utilities for Gemini AI API integration
 * Provides functionality to generate translations and other AI-powered features
 * 
 * <AUTHOR>
 */
public class GeminiCommons {

    private static final Logger LOGGER = LoggerFactory.getLogger(GeminiCommons.class.getName());
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Generate Italian translations for field names using Gemini AI
     * 
     * @param fieldNames List of field names to translate
     * @return Map of field name -> Italian translation
     * @throws IOException If API call fails
     */
    public static Map<String, String> generateFieldTranslations(List<String> fieldNames) throws IOException {
        if (!Defaults.ENABLE_FIELD_TRANSLATIONS) {
            LOGGER.info("Field translations are disabled in configuration");
            return new HashMap<>();
        }

        if (fieldNames == null || fieldNames.isEmpty()) {
            LOGGER.warn("No field names provided for translation");
            return new HashMap<>();
        }

        if (StringUtils.isBlank(Defaults.GEMINI_API_KEY) || "inserire_chiave_gemini".equals(Defaults.GEMINI_API_KEY)) {
            LOGGER.error("Gemini API key not configured properly");
            throw new IllegalStateException("Gemini API key not configured");
        }

        LOGGER.info("Generating translations for {} field names using Gemini AI", fieldNames.size());

        // Build the prompt
        String fieldList = String.join(", ", fieldNames);
        String prompt = fieldList + "\n\n" +
                "Sei uno sviluppatore Senior in Java, specializzato nel settore assicurativo, e devi tradurre queste variabili per mostrare un campo di nome sensato per l'utente finale. Queste etichette verranno usate per mostrare i vari log delle modifiche attuate.\n" +
                "Esempio: \"claimNumber\" -> \"Numero Sinistro\".\n" +
                "Ritorna il risultato in formato json solo con le traduzioni.\n" +
                "Evita di inserire nella traduzione \"ID\" o riferimenti che non sono comprensibili da un utente non tecnico";

        // Build request body
        Map<String, Object> requestBody = buildGeminiRequestBody(prompt);
        String jsonBody = objectMapper.writeValueAsString(requestBody);

        // Set headers
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");

        // Build URL with API key
        String urlWithKey = Defaults.GEMINI_API_ENDPOINT + "?key=" + Defaults.GEMINI_API_KEY;

        try {
            // Make API call
            LOGGER.debug("Calling Gemini API for field translations");
            String response = HttpUtils.post(urlWithKey, jsonBody, headers);
            LOGGER.debug("Gemini API response received: {}", response);

            // Parse response
            return parseGeminiTranslationResponse(response);

        } catch (IOException e) {
            LOGGER.error("Failed to call Gemini API for field translations", e);
            throw e;
        }
    }

    /**
     * Build the request body for Gemini API
     */
    private static Map<String, Object> buildGeminiRequestBody(String prompt) {
        Map<String, Object> requestBody = new HashMap<>();
        
        // Contents array
        Map<String, Object> content = new HashMap<>();
        Map<String, Object> part = new HashMap<>();
        part.put("text", prompt);
        content.put("parts", new Object[]{part});
        requestBody.put("contents", new Object[]{content});

        // Generation config
        Map<String, Object> generationConfig = new HashMap<>();
        generationConfig.put("temperature", 0.1);
        generationConfig.put("topK", 1);
        generationConfig.put("topP", 1);
        generationConfig.put("maxOutputTokens", 2048);
        requestBody.put("generationConfig", generationConfig);

        // Safety settings
        Map<String, Object> safetySettings = new HashMap<>();
        safetySettings.put("category", "HARM_CATEGORY_HARASSMENT");
        safetySettings.put("threshold", "BLOCK_MEDIUM_AND_ABOVE");
        requestBody.put("safetySettings", new Object[]{safetySettings});

        return requestBody;
    }

    /**
     * Parse Gemini API response and extract translations
     */
    private static Map<String, String> parseGeminiTranslationResponse(String response) throws IOException {
        try {
            JsonNode rootNode = objectMapper.readTree(response);
            
            // Navigate to the text content
            JsonNode candidatesNode = rootNode.get("candidates");
            if (candidatesNode == null || !candidatesNode.isArray() || candidatesNode.size() == 0) {
                throw new IOException("Invalid Gemini API response: no candidates found");
            }

            JsonNode firstCandidate = candidatesNode.get(0);
            JsonNode contentNode = firstCandidate.get("content");
            if (contentNode == null) {
                throw new IOException("Invalid Gemini API response: no content found");
            }

            JsonNode partsNode = contentNode.get("parts");
            if (partsNode == null || !partsNode.isArray() || partsNode.size() == 0) {
                throw new IOException("Invalid Gemini API response: no parts found");
            }

            JsonNode firstPart = partsNode.get(0);
            JsonNode textNode = firstPart.get("text");
            if (textNode == null) {
                throw new IOException("Invalid Gemini API response: no text found");
            }

            String translationText = textNode.asText();
            LOGGER.debug("Raw translation text from Gemini: {}", translationText);

            // Extract JSON from the response (it might be wrapped in markdown code blocks)
            String jsonText = extractJsonFromText(translationText);
            
            // Parse the JSON translations
            JsonNode translationsNode = objectMapper.readTree(jsonText);
            Map<String, String> translations = new HashMap<>();
            
            translationsNode.fields().forEachRemaining(entry -> {
                translations.put(entry.getKey(), entry.getValue().asText());
            });

            LOGGER.info("Successfully parsed {} translations from Gemini API", translations.size());
            return translations;

        } catch (Exception e) {
            LOGGER.error("Failed to parse Gemini API response: {}", response, e);
            throw new IOException("Failed to parse Gemini API response", e);
        }
    }

    /**
     * Extract JSON content from text that might be wrapped in markdown code blocks
     */
    private static String extractJsonFromText(String text) {
        if (StringUtils.isBlank(text)) {
            return "{}";
        }

        // Remove markdown code blocks if present
        String cleanText = text.trim();
        if (cleanText.startsWith("```json")) {
            cleanText = cleanText.substring(7);
        } else if (cleanText.startsWith("```")) {
            cleanText = cleanText.substring(3);
        }
        
        if (cleanText.endsWith("```")) {
            cleanText = cleanText.substring(0, cleanText.length() - 3);
        }

        cleanText = cleanText.trim();

        // Find the first { and last } to extract JSON
        int firstBrace = cleanText.indexOf('{');
        int lastBrace = cleanText.lastIndexOf('}');
        
        if (firstBrace >= 0 && lastBrace > firstBrace) {
            return cleanText.substring(firstBrace, lastBrace + 1);
        }

        return cleanText;
    }
}
