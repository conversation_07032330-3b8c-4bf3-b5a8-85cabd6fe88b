package core;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.IOException;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class ObjectIdJsonDeserializer extends JsonDeserializer<ObjectId> {

    @Override
    public ObjectId deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        ObjectCodec oc = p.getCodec();
        JsonNode node = oc.readTree(p);
        
        ObjectId oid;
        if (node.has("$oid")) {
            String id = node.get("$oid").asText();
            oid = new ObjectId(id);
        } else {
            int timestamp = node.get("timestamp").asInt();
            int machineIdentifier = node.get("machineIdentifier").asInt();
            short processIdentifier = (short) node.get("processIdentifier").asInt();
            int counter = node.get("counter").asInt();
            oid = new ObjectId(timestamp, machineIdentifier, processIdentifier, counter);
        }

        return oid;
    }
    
}
