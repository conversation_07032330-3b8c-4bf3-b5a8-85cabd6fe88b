package core;

import org.bson.BsonReader;
import org.bson.BsonType;
import org.bson.BsonWriter;
import org.bson.codecs.Codec;
import org.bson.codecs.DecoderContext;
import org.bson.codecs.EncoderContext;
import org.apache.commons.lang3.time.DateUtils;

import java.text.ParseException;
import java.util.Date;
import java.util.TimeZone;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Custom MongoDB codec for Date handling that applies the same timezone
 * adjustments as the DateJsonDeserializer used in Core.fromDocument()
 * 
 * <AUTHOR>
 */
public class CustomDateCodec implements Codec<Date> {
    
    private static final Logger LOGGER = Logger.getLogger(CustomDateCodec.class.getName());
    private static final long GMT_WORKAROUND = (1000 * 60 * 60);

    @Override
    public Date decode(BsonReader reader, DecoderContext decoderContext) {
        BsonType bsonType = reader.getCurrentBsonType();
        
        if (bsonType == BsonType.NULL) {
            reader.readNull();
            return null;
        }
        
        if (bsonType == BsonType.DATE_TIME) {
            long millies = reader.readDateTime();
            if (millies != 0L) {
                // Apply the same GMT workaround as DateJsonDeserializer
                Date date = new Date(millies - GMT_WORKAROUND);
                if (TimeZone.getDefault().inDaylightTime(date)) {
                    date = new Date(millies - GMT_WORKAROUND - GMT_WORKAROUND);
                }
                return date;
            } else {
                return new Date(0L);
            }
        }
        
        if (bsonType == BsonType.STRING) {
            String dateString = reader.readString();
            try {
                Date date = DateUtils.parseDate(dateString, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
                if (date != null) {
                    date = new Date(date.getTime() - GMT_WORKAROUND);
                    if (TimeZone.getDefault().inDaylightTime(date)) {
                        date = new Date(date.getTime() - GMT_WORKAROUND);
                    }
                }
                return date;
            } catch (ParseException ex) {
                LOGGER.log(Level.SEVERE, "Error parsing date string: " + dateString, ex);
                return null;
            }
        }
        
        if (bsonType == BsonType.INT64) {
            long millies = reader.readInt64();
            if (millies != 0L) {
                Date date = new Date(millies - GMT_WORKAROUND);
                if (TimeZone.getDefault().inDaylightTime(date)) {
                    date = new Date(millies - GMT_WORKAROUND - GMT_WORKAROUND);
                }
                return date;
            } else {
                return new Date(0L);
            }
        }
        
        if (bsonType == BsonType.INT32) {
            long millies = reader.readInt32();
            if (millies != 0L) {
                Date date = new Date(millies - GMT_WORKAROUND);
                if (TimeZone.getDefault().inDaylightTime(date)) {
                    date = new Date(millies - GMT_WORKAROUND - GMT_WORKAROUND);
                }
                return date;
            } else {
                return new Date(0L);
            }
        }
        
        // Fallback - read as default and return without timezone adjustment
        LOGGER.warning("Unexpected BSON type for Date field: " + bsonType + ". Using default handling.");
        return new Date(reader.readDateTime());
    }

    @Override
    public void encode(BsonWriter writer, Date value, EncoderContext encoderContext) {
        if (value == null) {
            writer.writeNull();
        } else {
            // Apply reverse GMT workaround when encoding
            long adjustedTime = value.getTime() + GMT_WORKAROUND;
            if (TimeZone.getDefault().inDaylightTime(value)) {
                adjustedTime += GMT_WORKAROUND;
            }
            writer.writeDateTime(adjustedTime);
        }
    }

    @Override
    public Class<Date> getEncoderClass() {
        return Date.class;
    }
}
