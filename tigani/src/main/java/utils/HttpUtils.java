package utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.HttpsURLConnection;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * Utility class for making HTTP requests to external APIs
 * 
 * <AUTHOR>
 */
public class HttpUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(HttpUtils.class.getName());
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Make a GET request to the specified URL with query parameters
     * 
     * @param baseUrl The base URL
     * @param queryParams Query parameters to append to the URL
     * @param headers HTTP headers to include in the request
     * @return The response body as a string
     * @throws IOException If an error occurs during the request
     */
    public static String get(String baseUrl, Map<String, String> queryParams, Map<String, String> headers) throws IOException {
        String fullUrl = buildUrlWithParams(baseUrl, queryParams);
        
        LOGGER.debug("Making GET request to: {}", fullUrl);
        
        URL url = new URL(fullUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        try {
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(30000); // 30 seconds
            connection.setReadTimeout(30000); // 30 seconds
            
            // Set headers
            if (headers != null) {
                for (Map.Entry<String, String> header : headers.entrySet()) {
                    connection.setRequestProperty(header.getKey(), header.getValue());
                }
            }
            
            // Set default headers
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("User-Agent", "Tigani-Application/1.0");
            
            int responseCode = connection.getResponseCode();
            LOGGER.debug("Response code: {}", responseCode);
            
            if (responseCode >= 200 && responseCode < 300) {
                return readResponse(connection);
            } else {
                String errorResponse = readErrorResponse(connection);
                LOGGER.error("HTTP request failed with code {}: {}", responseCode, errorResponse);
                throw new IOException("HTTP request failed with code " + responseCode + ": " + errorResponse);
            }
            
        } finally {
            connection.disconnect();
        }
    }

    /**
     * Make a POST request to the specified URL with JSON body
     * 
     * @param baseUrl The base URL
     * @param jsonBody The JSON body to send
     * @param headers HTTP headers to include in the request
     * @return The response body as a string
     * @throws IOException If an error occurs during the request
     */
    public static String post(String baseUrl, String jsonBody, Map<String, String> headers) throws IOException {
        LOGGER.debug("Making POST request to: {}", baseUrl);
        
        URL url = new URL(baseUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        try {
            connection.setRequestMethod("POST");
            connection.setConnectTimeout(30000); // 30 seconds
            connection.setReadTimeout(30000); // 30 seconds
            connection.setDoOutput(true);
            
            // Set headers
            if (headers != null) {
                for (Map.Entry<String, String> header : headers.entrySet()) {
                    connection.setRequestProperty(header.getKey(), header.getValue());
                }
            }
            
            // Set default headers
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("User-Agent", "Tigani-Application/1.0");
            
            // Write body
            if (StringUtils.isNotBlank(jsonBody)) {
                try (OutputStream os = connection.getOutputStream()) {
                    byte[] input = jsonBody.getBytes(StandardCharsets.UTF_8);
                    os.write(input, 0, input.length);
                }
            }
            
            int responseCode = connection.getResponseCode();
            LOGGER.debug("Response code: {}", responseCode);
            
            if (responseCode >= 200 && responseCode < 300) {
                return readResponse(connection);
            } else {
                String errorResponse = readErrorResponse(connection);
                LOGGER.error("HTTP request failed with code {}: {}", responseCode, errorResponse);
                throw new IOException("HTTP request failed with code " + responseCode + ": " + errorResponse);
            }
            
        } finally {
            connection.disconnect();
        }
    }

    /**
     * Parse JSON response to the specified class
     * 
     * @param jsonResponse The JSON response string
     * @param clazz The class to parse the response to
     * @return The parsed object
     * @throws IOException If parsing fails
     */
    public static <T> T parseJson(String jsonResponse, Class<T> clazz) throws IOException {
        try {
            return objectMapper.readValue(jsonResponse, clazz);
        } catch (Exception ex) {
            LOGGER.error("Failed to parse JSON response: {}", jsonResponse, ex);
            throw new IOException("Failed to parse JSON response", ex);
        }
    }

    /**
     * Build URL with query parameters
     */
    private static String buildUrlWithParams(String baseUrl, Map<String, String> queryParams) throws IOException {
        if (queryParams == null || queryParams.isEmpty()) {
            return baseUrl;
        }
        
        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        urlBuilder.append("?");
        
        boolean first = true;
        for (Map.Entry<String, String> param : queryParams.entrySet()) {
            if (!first) {
                urlBuilder.append("&");
            }
            urlBuilder.append(URLEncoder.encode(param.getKey(), StandardCharsets.UTF_8.toString()));
            urlBuilder.append("=");
            urlBuilder.append(URLEncoder.encode(param.getValue(), StandardCharsets.UTF_8.toString()));
            first = false;
        }
        
        return urlBuilder.toString();
    }

    /**
     * Read successful response from connection
     */
    private static String readResponse(HttpURLConnection connection) throws IOException {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            return response.toString();
        }
    }

    /**
     * Read error response from connection
     */
    private static String readErrorResponse(HttpURLConnection connection) {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            return response.toString();
        } catch (Exception ex) {
            return "Unable to read error response";
        }
    }
}
