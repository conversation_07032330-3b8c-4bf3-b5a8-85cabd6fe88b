package utils;

import java.util.Date;
import java.util.TimeZone;
import org.apache.commons.lang3.time.DateUtils;

/**
 *
 * <AUTHOR>
 */
public class MongoUtils {
    
    public static Date mongoDate(Date date) {
        
        // mongodb stores dates in UTC format... otherwise our jackson parser
        // forces local date... this causes a conflict when the client parses
        // out date in a query
        //
        // given the situation:
        // - our order is saved as "2018-06-13T22:01:24.123Z CEST"
        // - our filter is until "2018-06-13T23:59:59:999Z"
        // - the f*****g mongo client parses the date to query adjusting time
        //   as UTC... due subtracting 2 hours from CEST to UTC....
        // - but when we passes a CET time, without DTS (daylight time saving)
        //   the client parses the date to query adjusting time as UTC...
        //   due subtracting 1 hours from CET to UTC....
        //
        // the workaround:
        // - we counter adjust date adding UTC offset from local time...
        //   based on the date passed
        
        /*if (date != null) {
            int offset = TimeZone.getDefault().getOffset(date.getTime()) / 1000 / 60 / 60;
            date = DateUtils.addHours(date, -offset);
        }*/
        return date;
    }
}
