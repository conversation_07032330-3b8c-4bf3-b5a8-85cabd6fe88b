package utils;

import pojo.BasePojo;

/**
 *
 * <AUTHOR>
 */
public class UploadedFile extends BasePojo {
    
    private String name;
    private String contentType;
    private String extension;
    private byte[] content;

    public UploadedFile(String name, String contentType, String extension, byte[] content) {
        this.name = name;
        this.contentType = contentType;
        this.extension = extension;
        this.content = content;
    }
    
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getExtension() {
        return extension;
    }

    public void setExtension(String extension) {
        this.extension = extension;
    }

    public byte[] getContent() {
        return content;
    }

    public void setContent(byte[] content) {
        this.content = content;
    }
}
