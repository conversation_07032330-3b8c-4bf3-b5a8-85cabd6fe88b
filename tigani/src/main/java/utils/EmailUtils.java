package utils;

import com.sendgrid.*;
import com.sendgrid.helpers.mail.Mail;
import com.sendgrid.helpers.mail.objects.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Message;

import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Utility class per l'invio di email tramite SendGrid
 */
public class EmailUtils {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(EmailUtils.class.getName());
    
    // Configurazione SendGrid
    private static final String SENDGRID_API_KEY = System.getenv("SENDGRID_API_KEY") != null ?
        System.getenv("SENDGRID_API_KEY") : "*********************************************************************";
    private static final String DEFAULT_FROM_EMAIL = System.getenv("DEFAULT_FROM_EMAIL") != null ?
        System.getenv("DEFAULT_FROM_EMAIL") : "<EMAIL>";
    private static final String DEFAULT_FROM_NAME = System.getenv("DEFAULT_FROM_NAME") != null ?
        System.getenv("DEFAULT_FROM_NAME") : "<EMAIL>";
    private static final String SENDGRID_WEBHOOK_URL = System.getenv("SENDGRID_WEBHOOK_URL") != null ?
        System.getenv("SENDGRID_WEBHOOK_URL") : "https://isapo-158-47-225-145.a.free.pinggy.link/tigani/api/message/sendgrid/callback";
    
    private static SendGrid sendGridClient;

    static {
        if (SENDGRID_API_KEY != null && !SENDGRID_API_KEY.isEmpty()) {
            sendGridClient = new SendGrid(SENDGRID_API_KEY);
        } else {
            LOGGER.warn("SENDGRID_API_KEY non configurata. EmailUtils non funzionerà correttamente.");
        }
    }
    
    /**
     * Invia una email semplice
     */
    public static boolean sendSimpleEmail(String to, String subject, String body) {
        return sendSimpleEmail(to, null, subject, body);
    }
    
    /**
     * Invia una email semplice con nome destinatario
     */
    public static boolean sendSimpleEmail(String to, String toName, String subject, String body) {
        try {
            Email from = new Email(DEFAULT_FROM_EMAIL, DEFAULT_FROM_NAME);
            Email toEmail = new Email(to, toName);
            Content content = new Content("text/plain", body);

            Mail mail = new Mail(from, subject, toEmail, content);

            // Genera un ID univoco per il tracking
            String trackingId = java.util.UUID.randomUUID().toString();

            // Configura il tracking
            TrackingSettings trackingSettings = configureTracking(trackingId);
            mail.setTrackingSettings(trackingSettings);

            // Configura webhook e custom arguments
            configureWebhook(mail);
            addCustomArguments(mail, trackingId, "simple_email");

            Request request = new Request();
            request.setMethod(Method.POST);
            request.setEndpoint("mail/send");
            request.setBody(mail.build());

            Response response = sendGridClient.api(request);

            if (response.getStatusCode() >= 200 && response.getStatusCode() < 300) {
                LOGGER.info("Email inviata con successo a: {} (Tracking ID: {})", to, trackingId);
                return true;
            } else {
                LOGGER.error("Errore nell'invio email. Status: {}, Body: {}",
                    response.getStatusCode(), response.getBody());
                return false;
            }

        } catch (IOException e) {
            LOGGER.error("Errore nell'invio email a {}: {}", to, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Invia una email HTML
     */
    public static boolean sendHtmlEmail(String to, String toName, String subject, 
                                       String textBody, String htmlBody) {
        try {
            Email from = new Email(DEFAULT_FROM_EMAIL, DEFAULT_FROM_NAME);
            Email toEmail = new Email(to, toName);
            
            Mail mail = new Mail();
            mail.setFrom(from);
            mail.setSubject(subject);
            
            // Aggiungi contenuto testuale
            if (textBody != null && !textBody.isEmpty()) {
                Content textContent = new Content("text/plain", textBody);
                mail.addContent(textContent);
            }
            
            // Aggiungi contenuto HTML
            if (htmlBody != null && !htmlBody.isEmpty()) {
                Content htmlContent = new Content("text/html", htmlBody);
                mail.addContent(htmlContent);
            }
            
            // Aggiungi destinatario
            Personalization personalization = new Personalization();
            personalization.addTo(toEmail);
            mail.addPersonalization(personalization);

            // Genera un ID univoco per il tracking
            String trackingId = java.util.UUID.randomUUID().toString();

            // Configura il tracking
            TrackingSettings trackingSettings = configureTracking(trackingId);
            mail.setTrackingSettings(trackingSettings);

            // Configura webhook e custom arguments
            configureWebhook(mail);
            addCustomArguments(mail, trackingId, "html_email");

            Request request = new Request();
            request.setMethod(Method.POST);
            request.setEndpoint("mail/send");
            request.setBody(mail.build());
            
            Response response = sendGridClient.api(request);
            
            if (response.getStatusCode() >= 200 && response.getStatusCode() < 300) {
                LOGGER.info("Email HTML inviata con successo a: {}", to);
                return true;
            } else {
                LOGGER.error("Errore nell'invio email HTML. Status: {}, Body: {}", 
                    response.getStatusCode(), response.getBody());
                return false;
            }
            
        } catch (IOException e) {
            LOGGER.error("Errore nell'invio email HTML a {}: {}", to, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Invia email utilizzando un oggetto Message
     */
    public static String sendMessage(Message message) {
        if (sendGridClient == null) {
            LOGGER.error("SendGrid client non inizializzato. Verificare SENDGRID_API_KEY.");
            return null;
        }
        
        if (!message.isEmail()) {
            LOGGER.error("Il messaggio non è di tipo EMAIL");
            return null;
        }
        
        try {
            String fromEmail = message.getFromAddress() != null ? message.getFromAddress() : DEFAULT_FROM_EMAIL;
            String fromName = message.getFromName() != null ? message.getFromName() : DEFAULT_FROM_NAME;
            
            Email from = new Email(fromEmail, fromName);
            Email to = new Email(message.getToAddress(), message.getToName());
            
            Mail mail = new Mail();
            mail.setFrom(from);
            mail.setSubject(message.getSubject());
            
            // Aggiungi contenuto
            if (message.getBody() != null && !message.getBody().isEmpty()) {
                Content textContent = new Content("text/plain", message.getBody());
                mail.addContent(textContent);
            }
            
            if (message.getHtmlBody() != null && !message.getHtmlBody().isEmpty()) {
                Content htmlContent = new Content("text/html", message.getHtmlBody());
                mail.addContent(htmlContent);
            }
            
            // Aggiungi destinatario
            Personalization personalization = new Personalization();
            personalization.addTo(to);
            mail.addPersonalization(personalization);

            // Genera un ID univoco per questo messaggio per il tracking
            String trackingId = java.util.UUID.randomUUID().toString();

            // Configura il tracking (open tracking, click tracking)
            TrackingSettings trackingSettings = configureTracking(trackingId);
            mail.setTrackingSettings(trackingSettings);

            // Configura webhook per eventi
            configureWebhook(mail);

            // Aggiungi argomenti personalizzati per identificare l'email nei callback
            addCustomArguments(mail, trackingId, "email");

            // TODO: Gestire allegati se presenti
            // if (message.getAttachmentIds() != null && !message.getAttachmentIds().isEmpty()) {
            //     addAttachments(mail, message.getAttachmentIds());
            // }

            Request request = new Request();
            request.setMethod(Method.POST);
            request.setEndpoint("mail/send");
            request.setBody(mail.build());
            
            Response response = sendGridClient.api(request);
            
            if (response.getStatusCode() >= 200 && response.getStatusCode() < 300) {
                LOGGER.info("Email inviata con successo tramite Message a: {}", message.getToAddress());
                
                // Estrai l'ID del messaggio dalla risposta se disponibile
                return extractMessageId(response);
            } else {
                LOGGER.error("Errore nell'invio email tramite Message. Status: {}, Body: {}", 
                    response.getStatusCode(), response.getBody());
                return null;
            }
            
        } catch (IOException e) {
            LOGGER.error("Errore nell'invio email tramite Message a {}: {}", 
                message.getToAddress(), e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Configura le impostazioni di tracking per l'email
     */
    private static TrackingSettings configureTracking(String messageId) {
        TrackingSettings trackingSettings = new TrackingSettings();

        // Open Tracking - traccia quando l'email viene aperta
        OpenTrackingSetting openTracking = new OpenTrackingSetting();
        openTracking.setEnable(true);
        /*if (messageId != null) {
            openTracking.setSubstitutionTag("%message_id%");
        }*/
        trackingSettings.setOpenTrackingSetting(openTracking);

        // Click Tracking - traccia quando i link vengono cliccati
        ClickTrackingSetting clickTracking = new ClickTrackingSetting();
        clickTracking.setEnable(true);
        clickTracking.setEnableText(true); // Traccia anche i link in plain text
        trackingSettings.setClickTrackingSetting(clickTracking);

        return trackingSettings;
    }

    /**
     * Configura il webhook per gli eventi email
     */
    private static void configureWebhook(Mail mail) {
        if (SENDGRID_WEBHOOK_URL != null && !SENDGRID_WEBHOOK_URL.isEmpty()) {
            // Configura l'URL del webhook per ricevere eventi
            // Nota: questo deve essere configurato anche nel dashboard SendGrid
            LOGGER.debug("Webhook URL configurato: {}", SENDGRID_WEBHOOK_URL);
        }
    }

    /**
     * Aggiunge argomenti personalizzati per identificare l'email nei callback
     */
    private static void addCustomArguments(Mail mail, String messageId, String messageType) {
        if (messageId != null) {
            // Aggiungi custom arguments che verranno inclusi nei webhook
            mail.addCustomArg("message_id", messageId);
            mail.addCustomArg("message_type", messageType != null ? messageType : "email");
            mail.addCustomArg("source", "tigani_webapp");
        }
    }

    /**
     * Estrae l'ID del messaggio dalla risposta SendGrid
     */
    private static String extractMessageId(Response response) {
        // SendGrid può restituire l'ID del messaggio in diversi header
        if (response.getHeaders() != null) {
            // Prova diversi possibili header
            String[] possibleHeaders = {"X-Message-Id", "x-message-id", "Message-Id", "message-id"};

            for (String header : possibleHeaders) {
                if (response.getHeaders().containsKey(header)) {
                    String messageId = response.getHeaders().get(header);
                    if (messageId != null && !messageId.isEmpty()) {
                        return messageId;
                    }
                }
            }
        }

        // Se non troviamo l'ID negli header, generiamo un UUID come fallback
        String fallbackId = java.util.UUID.randomUUID().toString();
        LOGGER.warn("Message ID non trovato nella risposta SendGrid, uso fallback: {}", fallbackId);
        return fallbackId;
    }
    
    /**
     * Verifica se EmailUtils è configurato correttamente
     */
    public static boolean isConfigured() {
        return sendGridClient != null && SENDGRID_API_KEY != null && !SENDGRID_API_KEY.isEmpty();
    }
    
    /**
     * Test di connessione a SendGrid
     */
    public static boolean testConnection() {
        if (!isConfigured()) {
            return false;
        }

        try {
            // Tenta di ottenere le statistiche (endpoint che non invia email)
            Request request = new Request();
            request.setMethod(Method.GET);
            request.setEndpoint("stats");

            Response response = sendGridClient.api(request);
            return response.getStatusCode() >= 200 && response.getStatusCode() < 300;

        } catch (IOException e) {
            LOGGER.error("Errore nel test di connessione SendGrid: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Ottiene informazioni sulla configurazione del tracking
     */
    public static Map<String, Object> getTrackingInfo() {
        Map<String, Object> trackingInfo = new HashMap<>();

        trackingInfo.put("openTrackingEnabled", true);
        trackingInfo.put("clickTrackingEnabled", true);
        trackingInfo.put("webhookConfigured", SENDGRID_WEBHOOK_URL != null && !SENDGRID_WEBHOOK_URL.isEmpty());
        trackingInfo.put("webhookUrl", SENDGRID_WEBHOOK_URL);

        return trackingInfo;
    }

    /**
     * Verifica se il tracking è completamente configurato
     */
    public static boolean isTrackingConfigured() {
        return isConfigured() && SENDGRID_WEBHOOK_URL != null && !SENDGRID_WEBHOOK_URL.isEmpty();
    }
}
