package utils;

import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.bson.conversions.Bson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Country;
import pojo.City;
import pojo.QueryOptions;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TinUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(TinUtils.class.getName());

    // ITALIAN TIN FORMAT
    // 0123456789012345
    // CCCNNNAAMGGCCCCD

	private final static String MONTH_CODES        = "A" + "B" + "C" + "D" + "E" + "H" + "L" + "M" + "P" + "R" + "S" + "T";
	private final static int[]  ODD_PARITY_VALUES  = { 1, 0, 5, 7, 9,13,15,17,19,21, 1, 0, 5, 7, 9,13,15,17,19,21, 2, 4,18,20,11, 3, 6, 8,12,14,16,10,22,25,24,23};
	private final static int[]  EVEN_PARITY_VALUES = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25};
    
    public static Date birthDate(String tin) {
        if (StringUtils.isBlank(tin)) {
            return null;
        }
        if (StringUtils.length(tin) < 16) {
            return null;
        }
        
        // year
        int year = NumberUtils.toInt(StringUtils.mid(tin, 6, 2), 0);
        if (year < 0) {
            return null;
        }
        if (year <= 20) {
            year += 2000;
        } else {
            year += 1900;
        }
        
        // day
        int day = NumberUtils.toInt(StringUtils.mid(tin, 9, 2), 0);
        if (day <= 0) {
            return null;
        }
        if (day > 40) {
            day -= 40;
        }
        
        // month
        int month = MONTH_CODES.indexOf(StringUtils.mid(tin, 8, 1));
        if (month < 0) {
            return null;
        }
        
        String dt = StringUtils.leftPad("" + day, 2, "0") + "/" + StringUtils.leftPad("" + (month + 1), 2, "0") + "/" + year;
        return TimeUtils.toDate(dt);
    }
    
    /*public static String birthCountryCode(String tin) {
        if (StringUtils.isBlank(tin)) {
            return null;
        }
        if (StringUtils.length(tin) < 16) {
            return null;
        }
        
        String code = StringUtils.mid(tin, 11, 4);
        if (StringUtils.isBlank(code)) {
            return null;
        }
        Country country = null;
        if (!StringUtils.startsWith(code, "Z")) {
            return "IT";
        } else {
            try {
                List<Bson> filters = new ArrayList<>();
                filters.add(DaoFilters.getFilter("code", DaoFiltersOperation.EQ, code));
                QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 1, null, null);
                country = BaseDao.getDocumentByFilters(Country.class, queryOptions);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
                return null;
            }
            if (country == null) {
                return null;
            }
           
        }
        
        return country.getCode();
    }
    
    public static String birthCity(String tin) {
        if (StringUtils.isBlank(tin)) {
            return null;
        }
        if (StringUtils.length(tin) < 16) {
            return null;
        }
        
        String code = StringUtils.mid(tin, 11, 4);
        if (StringUtils.isBlank(code)) {
            return null;
        }
        
        Tin t = null;
        try {
            t = TinDao.loadTinByTin(code);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
            return null;
        }
        if (t == null) {
            return null;
        }
        
        return t.getCity();
    }
    
    public static String birthProvinceCode(String tin) {
        if (StringUtils.isBlank(tin)) {
            return null;
        }
        if (StringUtils.length(tin) < 16) {
            return null;
        }
        
        String code = StringUtils.mid(tin, 11, 4);
        if (StringUtils.isBlank(code)) {
            return null;
        }
        
        Tin t = null;
        try {
            t = TinDao.loadTinByTin(code);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
            return null;
        }
        if (t == null) {
            return null;
        }
        if (StringUtils.isBlank(t.getCity())) {
            return null;
        }
        
        List<City> l = null;
        try {
            l = CityDao.loadCityList(t.getCity());
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
            return null;
        }
        if (l == null) {
            return null;
        }
        if (l.isEmpty()) {
            return null;
        }
        City c = l.get(0);
        if (c == null) {
            return null;
        }
        
        return c.getProvinceCode();
    }
    
    public static String birthTin(String city) {
        if (StringUtils.isBlank(city)) {
            return null;
        }
        
        Tin t = null;
        try {
            t = TinDao.loadTinByCity(city);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
            return null;
        }
        if (t == null) {
            return null;
        }
        
        return t.getTin();
    }
    
    public static String birthTinByCountry(String countryCode) {
        if (StringUtils.isBlank(countryCode)) {
            return null;
        }
        
        Country country = null;
        try {
            country = CountryDao.loadCountry(countryCode);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
            return null;
        }
        if (country == null) {
            return null;
        }

        return country.getCodeAt();
    }*/
    
    public static String tin(String lastname, String name, String gender, Date birthDate, String birthTin) {
        if (StringUtils.isBlank(lastname)) {
            return null;
        }
        if (StringUtils.isBlank(name)) {
            return null;
        }
        if (StringUtils.isBlank(gender)) {
            return null;
        }
        if (!StringUtils.containsIgnoreCase("MF", gender)) {
            return null;
        }
        if (birthDate == null) {
            return null;
        }
        // birthTin = "codiceBelfiore" di City.java
        if (StringUtils.isBlank(birthTin)) {
            return null;
        }
        
        String partial = (encodeTinLastname(lastname)
                        + encodeTinName(name)
                        + encodeTinBirthDate(gender, birthDate)
                        + birthTin)
                        .toUpperCase();

        String tin = partial + encodeTinControlCode(partial);

        return tin;
    }

    private static String encodeTinLastname(String lastname) {
        String tinSurname = "";
        if (lastname != null) {
            if (lastname.length() > 0) {
                lastname = removeAccents(lastname);
                int i = 0;
                while (i < lastname.length()) {
                    char c = lastname.charAt(i);
                    if (isAlphabetical(c)) {
                        if (!isVocal(c)) {
                            tinSurname += c;
                        }
                    }

                    if (tinSurname.length() == 3) {
                        break;
                    } else {
                        i++;
                    }
                }

                if (tinSurname.length() < 3) {
                    i = 0;
                    while (i < lastname.length()) {
                        char c = lastname.charAt(i);
                        if (isAlphabetical(c)) {
                            if (isVocal(c)) {
                                tinSurname += c;
                            }
                        }

                        if (tinSurname.length() == 3) {
                            break;
                        } else {
                            i++;
                        }
                    }
                }

				while (tinSurname.length() < 3) {
					tinSurname += 'x';
				}
            }
        }
        return tinSurname.toUpperCase();
    }

    private static String encodeTinName(String name) {
        String tinName = "";
        if (name != null) {
            if (name.length() > 0) {
                name = removeAccents(name);
                int i = 0;
                int consonants = 0;
                while (i < name.length()) {
                    char c = name.charAt(i);
                    if (isAlphabetical(c)) {
                        if (!isVocal(c)) {
                            consonants++;
                            if (consonants != 2) {
                                tinName += c;
                            }
                            if (tinName.length() == 3) {
                                break;
                            }
                        }
                    }
                    i++;
                }

                if (consonants < 4) {
                    i = 0;
                    tinName = "";
                    while (i < name.length()) {
                        char c = name.charAt(i);
                        if (isAlphabetical(c)) {
                            if (!isVocal(c)) {
                                tinName += c;
                            }
                        }

                        if (tinName.length() == 3) {
                            break;
                        } else {
                            i++;
                        }
                    }

                    if (tinName.length() < 3) {
                        i = 0;
                        while (i < name.length()) {
                            char c = name.charAt(i);
                            if (isAlphabetical(c)) {
                                if (isVocal(c)) {
                                    tinName += c;
                                }
                            }

                            if (tinName.length() == 3) {
                                break;
                            } else {
                                i++;
                            }
                        }
                    }

					while (tinName.length() < 3) {
						tinName += 'x';
					}
                }
            }
        }
        return tinName.toUpperCase();
    }

    private static String encodeTinBirthDate(String gender, Date birthDate) {
        String date = "";
        if (birthDate != null) {
            int year = TimeUtils.year(birthDate);
            String yearString = String.valueOf(year);
            if (yearString.length() >= 2) {
                date += yearString.charAt(yearString.length() - 2);
                date += yearString.charAt(yearString.length() - 1);
            }
            int month = TimeUtils.month(birthDate);
			date += MONTH_CODES.substring(month - 1, month);
            int day = TimeUtils.day(birthDate);
            if (gender.equals("F")) {
                day += 40;
            }
            String dayString = String.valueOf(day);
            if (dayString.length() == 1) {
                dayString = "0" + dayString;
            }
            date += dayString;
        }
        return date;
    }

    public static String encodeTinControlCode(String partialTin) {
        String controlCode = "";
        if (partialTin != null) {
            if (partialTin.length() == 15) {
                partialTin = partialTin.toUpperCase();
				int i = 0;
				int sum = 0;
				boolean invalidCharPresent = false;
				while (i < partialTin.length() && !invalidCharPresent) {
					if (i % 2 != 0) {
						if (isAlphabetical(partialTin.charAt(i))) {
							sum += EVEN_PARITY_VALUES[partialTin.charAt(i) - 65 + 10];
						} else if (isDigit(partialTin.charAt(i))) {
							sum += EVEN_PARITY_VALUES[partialTin.charAt(i) - 48];
						} else {
							invalidCharPresent = true;
						}
					} else {
						if (isAlphabetical(partialTin.charAt(i))) {
							sum += ODD_PARITY_VALUES[partialTin.charAt(i) - 65 + 10];
						} else if (isDigit(partialTin.charAt(i))) {
							sum += ODD_PARITY_VALUES[partialTin.charAt(i) - 48];
						} else {
							invalidCharPresent = true;
						}
					}
					i++;
				}
				controlCode += invalidCharPresent ? "" : (char) ((sum % 26) + 65);
            }
        }
        return controlCode;
    }
    
    private static Boolean isVocal(char c) {
        return ("AEIOUaeiou".indexOf(c) >= 0);
    }

    private static Boolean isAlphabetical(char c) {
		return (c >= 65) && (c <= 90) || (c >= 97) && (c <= 122);
    }

    private static Boolean isDigit(char c) {
		return (c >= 48) && (c <= 57);
    }
    
    private static String removeAccents(String s) {
        if (s != null) {
            if (s.length() > 0) {
	            int i = 0;
                char[] chars = s.toCharArray();
                while (i < s.length()) {
					switch (chars[i]) {
						case 'à': chars[i] = 'a'; break;
						case 'è': chars[i] = 'e'; break;
						case 'é': chars[i] = 'e'; break;
						case 'ì': chars[i] = 'i'; break;
						case 'ò': chars[i] = 'o'; break;
						case 'ù': chars[i] = 'u'; break;
						default: break;
					}
                    i++;
                }
                s = new String(chars);
            }
        }
        return s;
    }
    
}
