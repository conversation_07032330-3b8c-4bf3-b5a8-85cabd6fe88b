package utils;

import dao.BaseDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.InsuranceCompany;
import pojo.Warranty;
import pojo.WarrantyEntry;
import pojo.WarrantyType;

import java.util.ArrayList;
import java.util.List;

/**
 * Utility class for common warranty operations, particularly for converting
 * Warranty objects to WarrantyEntry objects with complete related data.
 * 
 * <AUTHOR>
 */
public class WarrantyCommons {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(WarrantyCommons.class.getName());
    
    /**
     * Convert a single Warranty to a WarrantyEntry with complete related data
     * 
     * @param warranty The warranty to convert
     * @return WarrantyEntry with warranty, warrantyType, and insuranceCompany populated
     */
    public static WarrantyEntry toEntry(Warranty warranty) {
        if (warranty == null) {
            LOGGER.warn("Cannot convert null warranty to WarrantyEntry");
            return null;
        }
        
        try {
            WarrantyEntry entry = new WarrantyEntry();
            entry.setWarranty(warranty);
            
            // Load WarrantyType if warrantyTypeId is present
            if (warranty.getWarrantyTypeId() != null) {
                try {
                    WarrantyType warrantyType = BaseDao.getDocumentById(warranty.getWarrantyTypeId(), WarrantyType.class);
                    entry.setWarrantyType(warrantyType);
                    if (warrantyType == null) {
                        LOGGER.warn("WarrantyType not found for ID: {}", warranty.getWarrantyTypeId());
                    }
                } catch (Exception e) {
                    LOGGER.error("Error loading WarrantyType for warranty {}: {}", warranty.getId(), e.getMessage());
                }
            }
            
            // Load InsuranceCompany if insuranceCompanyId is present
            if (warranty.getInsuranceCompanyId() != null) {
                try {
                    InsuranceCompany insuranceCompany = BaseDao.getDocumentById(warranty.getInsuranceCompanyId(), InsuranceCompany.class);
                    entry.setInsuranceCompany(insuranceCompany);
                    if (insuranceCompany == null) {
                        LOGGER.warn("InsuranceCompany not found for ID: {}", warranty.getInsuranceCompanyId());
                    }
                } catch (Exception e) {
                    LOGGER.error("Error loading InsuranceCompany for warranty {}: {}", warranty.getId(), e.getMessage());
                }
            }
            
            return entry;
            
        } catch (Exception e) {
            LOGGER.error("Error converting warranty {} to WarrantyEntry: {}", warranty.getId(), e.getMessage());
            return null;
        }
    }
    
    /**
     * Convert a list of Warranties to a list of WarrantyEntries with complete related data
     * 
     * @param warranties The list of warranties to convert
     * @return List of WarrantyEntry objects with complete related data
     */
    public static List<WarrantyEntry> toEntries(List<Warranty> warranties) {
        List<WarrantyEntry> entries = new ArrayList<>();
        
        if (warranties == null || warranties.isEmpty()) {
            LOGGER.debug("No warranties to convert to entries");
            return entries;
        }
        
        LOGGER.debug("Converting {} warranties to WarrantyEntry objects", warranties.size());
        
        for (Warranty warranty : warranties) {
            WarrantyEntry entry = toEntry(warranty);
            if (entry != null) {
                entries.add(entry);
            }
        }
        
        LOGGER.debug("Successfully converted {} out of {} warranties to WarrantyEntry objects", 
                    entries.size(), warranties.size());
        
        return entries;
    }
    
    /**
     * Convert a list of Warranties to a list of WarrantyEntries, filtering out incomplete entries
     * 
     * @param warranties The list of warranties to convert
     * @param requireComplete If true, only returns entries that have all related data loaded
     * @return List of WarrantyEntry objects, optionally filtered for completeness
     */
    public static List<WarrantyEntry> toEntries(List<Warranty> warranties, boolean requireComplete) {
        List<WarrantyEntry> entries = toEntries(warranties);
        
        if (!requireComplete) {
            return entries;
        }
        
        // Filter for complete entries only
        List<WarrantyEntry> completeEntries = new ArrayList<>();
        for (WarrantyEntry entry : entries) {
            if (entry.isComplete()) {
                completeEntries.add(entry);
            }
        }
        
        LOGGER.debug("Filtered {} complete entries out of {} total entries", 
                    completeEntries.size(), entries.size());
        
        return completeEntries;
    }
}
