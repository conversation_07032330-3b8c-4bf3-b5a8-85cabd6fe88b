package utils;

import com.twilio.Twilio;
import com.twilio.rest.api.v2010.account.Message;
import com.twilio.type.PhoneNumber;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Utility class per l'invio di SMS tramite Twilio
 */
public class SmsUtils {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(SmsUtils.class.getName());
    
    // Configurazione Twilio
    private static final String TWILIO_ACCOUNT_SID = System.getenv("TWILIO_ACCOUNT_SID") != null ?
        System.getenv("TWILIO_ACCOUNT_SID") : "**********************************";
    private static final String TWILIO_AUTH_TOKEN = System.getenv("TWILIO_AUTH_TOKEN") != null ?
        System.getenv("TWILIO_AUTH_TOKEN") : "1f1ed126740872021c213143faac19e1";
    private static final String TWILIO_PHONE_NUMBER = System.getenv("TWILIO_PHONE_NUMBER") != null ?
        System.getenv("TWILIO_PHONE_NUMBER") : "deview25";
    
    private static boolean initialized = false;
    
    static {
        initializeTwilio();
    }
    
    /**
     * Inizializza il client Twilio
     */
    private static void initializeTwilio() {
        if (TWILIO_ACCOUNT_SID != null && !TWILIO_ACCOUNT_SID.isEmpty() &&
            TWILIO_AUTH_TOKEN != null && !TWILIO_AUTH_TOKEN.isEmpty()) {
            
            try {
                Twilio.init(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN);
                initialized = true;
                LOGGER.info("Twilio inizializzato correttamente");
            } catch (Exception e) {
                LOGGER.error("Errore nell'inizializzazione di Twilio: {}", e.getMessage(), e);
                initialized = false;
            }
        } else {
            LOGGER.warn("Credenziali Twilio non configurate. SmsUtils non funzionerà correttamente.");
            initialized = false;
        }
    }
    
    /**
     * Invia un SMS semplice
     */
    public static boolean sendSimpleSms(String to, String body) {
        return sendSimpleSms(to, TWILIO_PHONE_NUMBER, body);
    }
    
    /**
     * Invia un SMS specificando il numero mittente
     */
    public static boolean sendSimpleSms(String to, String from, String body) {
        if (!initialized) {
            LOGGER.error("Twilio non inizializzato. Verificare le credenziali.");
            return false;
        }
        
        try {
            Message message = Message.creator(
                new PhoneNumber(to),
                new PhoneNumber(from),
                body
            ).create();
            
            LOGGER.info("SMS inviato con successo. SID: {}, Status: {}", 
                message.getSid(), message.getStatus());
            return true;
            
        } catch (Exception e) {
            LOGGER.error("Errore nell'invio SMS a {}: {}", to, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Invia SMS con callback URL per tracking dello stato
     */
    public static String sendSmsWithCallback(String to, String from, String body, String callbackUrl) {
        if (!initialized) {
            LOGGER.error("Twilio non inizializzato. Verificare le credenziali.");
            return null;
        }
        
        try {
            Message message = Message.creator(
                new PhoneNumber(to),
                new PhoneNumber(from),
                body
            ).setStatusCallback(callbackUrl).create();
            
            LOGGER.info("SMS con callback inviato con successo. SID: {}, Status: {}", 
                message.getSid(), message.getStatus());
            
            return message.getSid();
            
        } catch (Exception e) {
            LOGGER.error("Errore nell'invio SMS con callback a {}: {}", to, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Invia SMS utilizzando un oggetto Message
     */
    public static String sendMessage(pojo.Message message) {
        return sendMessage(message, null);
    }
    
    /**
     * Invia SMS utilizzando un oggetto Message con callback URL
     */
    public static String sendMessage(pojo.Message message, String callbackUrl) {
        if (!initialized) {
            LOGGER.error("Twilio non inizializzato. Verificare le credenziali.");
            return null;
        }
        
        if (!message.isSms()) {
            LOGGER.error("Il messaggio non è di tipo SMS");
            return null;
        }
        
        try {
            String fromNumber = message.getFromAddress() != null ? message.getFromAddress() : TWILIO_PHONE_NUMBER;

            Message twilioMessage = Message.creator(
                    new PhoneNumber(message.getToAddress()),
                    new PhoneNumber(fromNumber),
                    message.getBody()
            ).setStatusCallback(callbackUrl).create();
            
            LOGGER.info("SMS tramite Message inviato con successo. SID: {}, Status: {}", 
                twilioMessage.getSid(), twilioMessage.getStatus());
            
            return twilioMessage.getSid();
            
        } catch (Exception e) {
            LOGGER.error("Errore nell'invio SMS tramite Message a {}: {}", 
                message.getToAddress(), e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Ottiene lo stato di un messaggio tramite SID
     */
    public static String getMessageStatus(String messageSid) {
        if (!initialized) {
            LOGGER.error("Twilio non inizializzato. Verificare le credenziali.");
            return null;
        }
        
        try {
            Message message = Message.fetcher(messageSid).fetch();
            return message.getStatus().toString();
            
        } catch (Exception e) {
            LOGGER.error("Errore nel recupero stato messaggio {}: {}", messageSid, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Verifica se SmsUtils è configurato correttamente
     */
    public static boolean isConfigured() {
        return initialized && TWILIO_PHONE_NUMBER != null && !TWILIO_PHONE_NUMBER.isEmpty();
    }
    
    /**
     * Test di connessione a Twilio
     */
    public static boolean testConnection() {
        if (!initialized) {
            return false;
        }
        
        try {
            // Tenta di ottenere informazioni sull'account
            com.twilio.rest.api.v2010.Account account = com.twilio.rest.api.v2010.Account.fetcher().fetch();
            LOGGER.info("Test connessione Twilio riuscito. Account SID: {}", account.getSid());
            return true;
            
        } catch (Exception e) {
            LOGGER.error("Errore nel test di connessione Twilio: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * Formatta un numero di telefono per Twilio (aggiunge + se mancante)
     */
    public static String formatPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return phoneNumber;
        }
        
        // Rimuovi spazi e caratteri speciali
        String cleaned = phoneNumber.replaceAll("[\\s\\-\\(\\)]", "");
        
        // Aggiungi + se mancante
        if (!cleaned.startsWith("+")) {
            cleaned = "+" + cleaned;
        }
        
        return cleaned;
    }
    
    /**
     * Valida un numero di telefono
     */
    public static boolean isValidPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return false;
        }
        
        String formatted = formatPhoneNumber(phoneNumber);
        
        // Controllo base: deve iniziare con + e avere almeno 10 cifre
        return formatted.matches("\\+\\d{10,15}");
    }
}
