package utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.BasePojo;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.*;

/**
 * Utility class for reflection operations on POJOs
 * Provides functionality to extract field names and metadata from classes
 * 
 * <AUTHOR>
 */
public class ReflectionUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReflectionUtils.class.getName());

    /**
     * Extract all field names from POJOs defined in CHANGE_STREAMS_COLLECTIONS
     * Includes fields from BasePojo inheritance and excludes technical fields
     * 
     * @return Set of unique field names across all monitored POJOs
     */
    public static Set<String> extractFieldNamesFromChangeStreamCollections() {
        Set<String> allFieldNames = new LinkedHashSet<>();
        
        if (Defaults.CHANGE_STREAMS_COLLECTIONS == null || Defaults.CHANGE_STREAMS_COLLECTIONS.isEmpty()) {
            LOGGER.warn("No collections configured in CHANGE_STREAMS_COLLECTIONS");
            return allFieldNames;
        }

        LOGGER.info("Extracting field names from {} POJO classes", Defaults.CHANGE_STREAMS_COLLECTIONS.size());

        for (String collectionName : Defaults.CHANGE_STREAMS_COLLECTIONS) {
            try {
                // Convert collection name to POJO class name (assuming they match)
                String className = "pojo." + collectionName;
                Class<?> pojoClass = Class.forName(className);
                
                Set<String> classFieldNames = extractFieldNamesFromClass(pojoClass);
                allFieldNames.addAll(classFieldNames);
                
                LOGGER.debug("Extracted {} field names from class {}: {}", 
                           classFieldNames.size(), className, classFieldNames);
                
            } catch (ClassNotFoundException e) {
                LOGGER.warn("POJO class not found for collection '{}': {}", collectionName, e.getMessage());
            } catch (Exception e) {
                LOGGER.error("Error extracting fields from collection '{}'", collectionName, e);
            }
        }

        LOGGER.info("Total unique field names extracted: {}", allFieldNames.size());
        return allFieldNames;
    }

    /**
     * Extract all field names from a specific class, including inherited fields
     * 
     * @param clazz The class to extract fields from
     * @return Set of field names from the class and its superclasses
     */
    public static Set<String> extractFieldNamesFromClass(Class<?> clazz) {
        Set<String> fieldNames = new LinkedHashSet<>();
        
        if (clazz == null) {
            return fieldNames;
        }

        // Get fields from the class and all its superclasses
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            Field[] fields = currentClass.getDeclaredFields();
            
            for (Field field : fields) {
                // Skip static, synthetic, and technical fields
                if (shouldIncludeField(field)) {
                    fieldNames.add(field.getName());
                }
            }
            
            currentClass = currentClass.getSuperclass();
        }

        return fieldNames;
    }

    /**
     * Extract detailed field information from POJOs in CHANGE_STREAMS_COLLECTIONS
     * 
     * @return Map of collection name -> field information
     */
    public static Map<String, List<FieldInfo>> extractDetailedFieldInfo() {
        Map<String, List<FieldInfo>> collectionFieldsMap = new HashMap<>();
        
        if (Defaults.CHANGE_STREAMS_COLLECTIONS == null || Defaults.CHANGE_STREAMS_COLLECTIONS.isEmpty()) {
            LOGGER.warn("No collections configured in CHANGE_STREAMS_COLLECTIONS");
            return collectionFieldsMap;
        }

        for (String collectionName : Defaults.CHANGE_STREAMS_COLLECTIONS) {
            try {
                String className = "pojo." + collectionName;
                Class<?> pojoClass = Class.forName(className);
                
                List<FieldInfo> fieldInfoList = extractFieldInfoFromClass(pojoClass);
                collectionFieldsMap.put(collectionName, fieldInfoList);
                
            } catch (ClassNotFoundException e) {
                LOGGER.warn("POJO class not found for collection '{}': {}", collectionName, e.getMessage());
                collectionFieldsMap.put(collectionName, new ArrayList<>());
            } catch (Exception e) {
                LOGGER.error("Error extracting field info from collection '{}'", collectionName, e);
                collectionFieldsMap.put(collectionName, new ArrayList<>());
            }
        }

        return collectionFieldsMap;
    }

    /**
     * Extract detailed field information from a specific class
     */
    private static List<FieldInfo> extractFieldInfoFromClass(Class<?> clazz) {
        List<FieldInfo> fieldInfoList = new ArrayList<>();
        
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            Field[] fields = currentClass.getDeclaredFields();
            
            for (Field field : fields) {
                if (shouldIncludeField(field)) {
                    FieldInfo fieldInfo = new FieldInfo();
                    fieldInfo.setName(field.getName());
                    fieldInfo.setType(field.getType().getSimpleName());
                    fieldInfo.setDeclaringClass(currentClass.getSimpleName());
                    fieldInfo.setFromBasePojo(BasePojo.class.isAssignableFrom(currentClass) && 
                                            currentClass.equals(BasePojo.class));
                    
                    fieldInfoList.add(fieldInfo);
                }
            }
            
            currentClass = currentClass.getSuperclass();
        }

        return fieldInfoList;
    }

    /**
     * Determine if a field should be included in the extraction
     * Excludes static, synthetic, and certain technical fields
     */
    private static boolean shouldIncludeField(Field field) {
        int modifiers = field.getModifiers();
        
        // Skip static fields
        if (Modifier.isStatic(modifiers)) {
            return false;
        }
        
        // Skip synthetic fields (generated by compiler)
        if (field.isSynthetic()) {
            return false;
        }
        
        String fieldName = field.getName();
        
        // Skip certain technical field names
        if (fieldName.startsWith("$") || 
            fieldName.equals("serialVersionUID") ||
            fieldName.equals("CGLIB$BOUND") ||
            fieldName.startsWith("CGLIB$")) {
            return false;
        }
        
        return true;
    }

    /**
     * Inner class to hold detailed field information
     */
    public static class FieldInfo {
        private String name;
        private String type;
        private String declaringClass;
        private boolean fromBasePojo;

        // Getters and setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        
        public String getDeclaringClass() { return declaringClass; }
        public void setDeclaringClass(String declaringClass) { this.declaringClass = declaringClass; }
        
        public boolean isFromBasePojo() { return fromBasePojo; }
        public void setFromBasePojo(boolean fromBasePojo) { this.fromBasePojo = fromBasePojo; }

        @Override
        public String toString() {
            return String.format("FieldInfo{name='%s', type='%s', declaringClass='%s', fromBasePojo=%s}", 
                               name, type, declaringClass, fromBasePojo);
        }
    }
}
