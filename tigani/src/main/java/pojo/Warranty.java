package pojo;

import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Warranty extends BasePojo {

    private String code;
    private String title;
    private String description;
    private ObjectId warrantyTypeId;
    private ObjectId insuranceCompanyId;
    private List<String> criteriaFields;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public ObjectId getWarrantyTypeId() {
        return warrantyTypeId;
    }

    public void setWarrantyTypeId(ObjectId warrantyTypeId) {
        this.warrantyTypeId = warrantyTypeId;
    }

    public ObjectId getInsuranceCompanyId() {
        return insuranceCompanyId;
    }

    public void setInsuranceCompanyId(ObjectId insuranceCompanyId) {
        this.insuranceCompanyId = insuranceCompanyId;
    }

    public List<String> getCriteriaFields() {
        return criteriaFields;
    }

    public String getCriteriaFieldsFormatted() {
        if (criteriaFields == null || criteriaFields.isEmpty()) {
            return "";
        }
        List<String> criteriaFieldsFormatted = new ArrayList<>();
        for (String criteriaField : criteriaFields) {
            if (StringUtils.equalsIgnoreCase(criteriaField, "province")) {
                criteriaFieldsFormatted.add("Provincia di residenza");
            } else if (StringUtils.equalsIgnoreCase(criteriaField, "insuranceSituation")) {
                criteriaFieldsFormatted.add("Situazione assicurativa");
            } else if (StringUtils.equalsIgnoreCase(criteriaField, "claimNumbers")) {
                criteriaFieldsFormatted.add("Numero di sinistri");
            } else if (StringUtils.equalsIgnoreCase(criteriaField, "cu")) {
                criteriaFieldsFormatted.add("Classe Universale");
            }
        }
        return StringUtils.join(criteriaFieldsFormatted, ", ");
    }

    public void setCriteriaFields(List<String> criteriaFields) {
        this.criteriaFields = criteriaFields;
    }
}
