package pojo;

import org.bson.types.ObjectId;

import java.util.List;

/**
 * POJO for Product entity
 * Contains all fields from the first step form and additional product management fields
 */
public class Product extends BasePojo {

    // Step 1 fields
    private ObjectId logoImageId;
    private String version;
    private String code;
    private String name;
    private String description;
    private String category;
    private String subcategory;
    private List<ObjectId> brandIds;

    // Step 2 fields
    private List<ObjectId> warrantyIds;

    // Internal
    private Integer step;
    private String status;
    private ObjectId createdByUserId;

    public ObjectId getLogoImageId() {
        return logoImageId;
    }

    public void setLogoImageId(ObjectId logoImageId) {
        this.logoImageId = logoImageId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getSubcategory() {
        return subcategory;
    }

    public void setSubcategory(String subcategory) {
        this.subcategory = subcategory;
    }

    public Integer getStep() {
        return step;
    }

    public void setStep(Integer step) {
        this.step = step;
    }

    public List<ObjectId> getBrandIds() {
        return brandIds;
    }

    public void setBrandIds(List<ObjectId> brandIds) {
        this.brandIds = brandIds;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public ObjectId getCreatedByUserId() {
        return createdByUserId;
    }

    public void setCreatedByUserId(ObjectId createdByUserId) {
        this.createdByUserId = createdByUserId;
    }

    public List<ObjectId> getWarrantyIds() {
        return warrantyIds;
    }

    public void setWarrantyIds(List<ObjectId> warrantyIds) {
        this.warrantyIds = warrantyIds;
    }
}
