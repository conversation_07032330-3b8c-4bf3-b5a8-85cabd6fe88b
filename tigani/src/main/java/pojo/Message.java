package pojo;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * POJO per gestire messaggi di diverso tipo (Email, SMS, WhatsApp)
 * Supporta tracking dello stato e callback da servizi esterni
 */
public class Message extends BasePojo {

    // Enum per il tipo di messaggio
    public enum MessageType {
        EMAIL, SMS, WHATSAPP
    }

    // Enum per lo stato del messaggio
    public enum MessageStatus {
        PENDING,     // In attesa di invio
        SENT,        // Inviato
        DELIVERED,   // Consegnato
        FAILED,      // Fallito
        BOUNCED,     // Rimbalzato (email)
        OPENED,      // Aperto (email)
        CLICKED      // Cliccato (email)
    }

    // Campi comuni
    private String messageType;
    private String status;
    private String externalId;           // ID del messaggio nel servizio esterno (Twilio/SendGrid)
    private String errorMessage;         // Messaggio di errore in caso di fallimento

    // Campi per il destinatario e mittente
    private String toAddress;            // Email o numero di telefono del destinatario
    private String fromAddress;          // Email o numero di telefono del mittente
    private String toName;               // Nome del destinatario (opzionale)
    private String fromName;             // Nome del mittente (opzionale)

    // Contenuto del messaggio
    private String subject;              // Oggetto (solo per email)
    private String body;                 // Corpo del messaggio
    private String htmlBody;             // Corpo HTML (solo per email)

    // Metadati e tracking
    private Date sentAt;                 // Data/ora di invio
    private Date deliveredAt;            // Data/ora di consegna
    private Date openedAt;               // Data/ora di apertura (email)
    private Date clickedAt;              // Data/ora di click (email)
    private Integer retryCount;          // Numero di tentativi di invio
    private Map<String, Object> metadata; // Metadati aggiuntivi

    // Allegati (solo per email)
    private List<String> attachmentIds;  // ID degli allegati nel sistema

    // Configurazione servizio
    private String serviceConfig;        // Configurazione specifica del servizio utilizzato

    // Costruttori
    public Message() {
        this.status = MessageStatus.PENDING.name();
        this.retryCount = 0;
    }

    public Message(String messageType, String toAddress, String fromAddress, String body) {
        this();
        this.messageType = messageType;
        this.toAddress = toAddress;
        this.fromAddress = fromAddress;
        this.body = body;
    }

    // Getter e Setter
    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getToAddress() {
        return toAddress;
    }

    public void setToAddress(String toAddress) {
        this.toAddress = toAddress;
    }

    public String getFromAddress() {
        return fromAddress;
    }

    public void setFromAddress(String fromAddress) {
        this.fromAddress = fromAddress;
    }

    public String getToName() {
        return toName;
    }

    public void setToName(String toName) {
        this.toName = toName;
    }

    public String getFromName() {
        return fromName;
    }

    public void setFromName(String fromName) {
        this.fromName = fromName;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getHtmlBody() {
        return htmlBody;
    }

    public void setHtmlBody(String htmlBody) {
        this.htmlBody = htmlBody;
    }

    public Date getSentAt() {
        return sentAt;
    }

    public void setSentAt(Date sentAt) {
        this.sentAt = sentAt;
    }

    public Date getDeliveredAt() {
        return deliveredAt;
    }

    public void setDeliveredAt(Date deliveredAt) {
        this.deliveredAt = deliveredAt;
    }

    public Date getOpenedAt() {
        return openedAt;
    }

    public void setOpenedAt(Date openedAt) {
        this.openedAt = openedAt;
    }

    public Date getClickedAt() {
        return clickedAt;
    }

    public void setClickedAt(Date clickedAt) {
        this.clickedAt = clickedAt;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    public List<String> getAttachmentIds() {
        return attachmentIds;
    }

    public void setAttachmentIds(List<String> attachmentIds) {
        this.attachmentIds = attachmentIds;
    }

    public String getServiceConfig() {
        return serviceConfig;
    }

    public void setServiceConfig(String serviceConfig) {
        this.serviceConfig = serviceConfig;
    }

    // Metodi di utilità
    public boolean isEmail() {
        return MessageType.EMAIL.name().equals(this.messageType);
    }

    public boolean isSms() {
        return MessageType.SMS.name().equals(this.messageType);
    }

    public boolean isWhatsApp() {
        return MessageType.WHATSAPP.name().equals(this.messageType);
    }

    public boolean isPending() {
        return MessageStatus.PENDING.name().equals(this.status);
    }

    public boolean isSent() {
        return MessageStatus.SENT.name().equals(this.status);
    }

    public boolean isDelivered() {
        return MessageStatus.DELIVERED.name().equals(this.status);
    }

    public boolean isFailed() {
        return MessageStatus.FAILED.name().equals(this.status);
    }

    public void markAsSent(String externalId) {
        this.status = MessageStatus.SENT.name();
        this.externalId = externalId;
        this.sentAt = new Date();
    }

    public void markAsDelivered() {
        this.status = MessageStatus.DELIVERED.name();
        this.deliveredAt = new Date();
    }

    public void markAsFailed(String errorMessage) {
        this.status = MessageStatus.FAILED.name();
        this.errorMessage = errorMessage;
    }

    public void incrementRetryCount() {
        if (this.retryCount == null) {
            this.retryCount = 0;
        }
        this.retryCount++;
    }
}
