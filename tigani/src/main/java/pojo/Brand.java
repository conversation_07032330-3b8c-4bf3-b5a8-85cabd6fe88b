package pojo;

import org.bson.types.ObjectId;

/**
 * POJO to store motorcycle brand data (fields aligned with API: codice, descrizione)
 */
public class Brand extends BasePojo {

    private ObjectId imageId;
    private String codice;
    private String descrizione;

    public ObjectId getImageId() {
        return imageId;
    }

    public void setImageId(ObjectId imageId) {
        this.imageId = imageId;
    }

    public String getCodice() {
        return codice;
    }

    public void setCodice(String codice) {
        this.codice = codice;
    }

    public String getDescrizione() {
        return descrizione;
    }

    public void setDescrizione(String descrizione) {
        this.descrizione = descrizione;
    }
}
