package pojo;

import org.bson.types.ObjectId;

/**
 * POJO representing the relationship between a user and a permission.
 * This junction entity defines what specific permission types (VIEW, CREATE, EDIT, DELETE)
 * a user has for a particular permission.
 * 
 * <AUTHOR>
 */
public class UserPermission extends BasePojo {

    private ObjectId userId;           // Reference to the User
    private String permissionCode;     // Code of the permission (e.g., "USER_MANAGEMENT")
    private String permissionType;     // Type of permission (VIEW, CREATE, EDIT, DELETE)

    public ObjectId getUserId() {
        return userId;
    }

    public void setUserId(ObjectId userId) {
        this.userId = userId;
    }

    public String getPermissionCode() {
        return permissionCode;
    }

    public void setPermissionCode(String permissionCode) {
        this.permissionCode = permissionCode;
    }

    public String getPermissionType() {
        return permissionType;
    }

    public void setPermissionType(String permissionType) {
        this.permissionType = permissionType;
    }
}
