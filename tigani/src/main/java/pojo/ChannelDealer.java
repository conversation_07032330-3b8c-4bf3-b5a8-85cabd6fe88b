package pojo;

import java.util.List;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class ChannelDealer extends BasePojo {

    private ObjectId channelId;
    private ObjectId dealerId;
    private List<ObjectId> brandModelIds;
    private List<ObjectId> warrantyIds;
    private List<String> emails;

    public ObjectId getChannelId() {
        return channelId;
    }

    public void setChannelId(ObjectId channelId) {
        this.channelId = channelId;
    }

    public ObjectId getDealerId() {
        return dealerId;
    }

    public void setDealerId(ObjectId dealerId) {
        this.dealerId = dealerId;
    }

    public List<ObjectId> getBrandModelIds() {
        return brandModelIds;
    }

    public void setBrandModelIds(List<ObjectId> brandModelIds) {
        this.brandModelIds = brandModelIds;
    }

    public List<ObjectId> getWarrantyIds() {
        return warrantyIds;
    }

    public void setWarrantyIds(List<ObjectId> warrantyIds) {
        this.warrantyIds = warrantyIds;
    }

    public List<String> getEmails() {
        return emails;
    }

    public void setEmails(List<String> emails) {
        this.emails = emails;
    }
}
