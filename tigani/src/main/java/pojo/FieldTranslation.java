package pojo;

/**
 * POJO for storing field translations generated by AI
 * Used to provide user-friendly Italian translations for POJO field names
 * 
 * <AUTHOR>
 */
public class FieldTranslation extends BasePojo {

    private String fieldName;           // Original field name (e.g., "claimNumber")
    private String translation;         // Italian translation (e.g., "Numero Sinistro")
    private String sourceClass;         // POJO class where the field is defined (e.g., "WarrantyDetails")
    private String fieldType;           // Field type (e.g., "String", "Integer", "List<String>")
    private Boolean isFromBasePojo;     // Whether the field comes from BasePojo
    private Boolean isAiGenerated;      // Whether the translation was generated by AI
    private String notes;               // Optional notes about the translation

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getTranslation() {
        return translation;
    }

    public void setTranslation(String translation) {
        this.translation = translation;
    }

    public String getSourceClass() {
        return sourceClass;
    }

    public void setSourceClass(String sourceClass) {
        this.sourceClass = sourceClass;
    }

    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    public Boolean getIsFromBasePojo() {
        return isFromBasePojo;
    }

    public void setIsFromBasePojo(Boolean isFromBasePojo) {
        this.isFromBasePojo = isFromBasePojo;
    }

    public Boolean getIsAiGenerated() {
        return isAiGenerated;
    }

    public void setIsAiGenerated(Boolean isAiGenerated) {
        this.isAiGenerated = isAiGenerated;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }
}
