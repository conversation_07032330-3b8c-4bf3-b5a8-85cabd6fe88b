package pojo;

import java.util.List;

/**
 * POJO to represent the response structure from Italiana API
 * Contains segnalazioni, errori, and payload fields
 */
public class ItalianaApiResponse {

    private List<String> segnalazioni;
    private List<String> errori;
    private List<ItalianaApiItem> payload;

    public List<String> getSegnalazioni() {
        return segnalazioni;
    }

    public void setSegnalazioni(List<String> segnalazioni) {
        this.segnalazioni = segnalazioni;
    }

    public List<String> getErrori() {
        return errori;
    }

    public void setErrori(List<String> errori) {
        this.errori = errori;
    }

    public List<ItalianaApiItem> getPayload() {
        return payload;
    }

    public void setPayload(List<ItalianaApiItem> payload) {
        this.payload = payload;
    }

    /**
     * Check if the response is valid (no errors and has payload)
     */
    public boolean isValid() {
        return (errori == null || errori.isEmpty()) && 
               (payload != null && !payload.isEmpty());
    }

    /**
     * Get error messages as a single string
     */
    public String getErrorMessage() {
        if (errori != null && !errori.isEmpty()) {
            return String.join(", ", errori);
        }
        return null;
    }

    @Override
    public String toString() {
        return "ItalianaApiResponse{" +
                "segnalazioni=" + segnalazioni +
                ", errori=" + errori +
                ", payload=" + payload +
                '}';
    }
}
