var hot_search_basic_init;
var counter = 0;

$(function () {

    // asynchronously load data
    var path_data = [];

    // Define element
    var hot_search_basic = document.getElementById('hot_search_basic');

    var colHeaders = ["Chiave"], columns = [{data: 'key'}];
      
    var colWidths = [100];
    if (pageVariables.get("languages")) {
        pageVariables.get("languages").replace("[", "").replace("]", "").split(", ").forEach(function (language) {
            colHeaders.push(language);
            columns.push({data: '' + language});
            colWidths.push(200);
        });
    }
//    console.log(colHeaders, columns);

    // Initialize with options
    var profileType = pageVariables.get("profileType");
    var contextMenu = null;
    if (profileType === 'system') {
        contextMenu = ['row_above', 'row_below', 'remove_row', 'undo'];
    }

    hot_search_basic_init = new Handsontable(hot_search_basic, {
        stretchH: 'all',
        fillHandle: false,
        colHeaders: colHeaders,
        colWidths: colWidths,
        manualColumnResize: true,
        wordWrap: false,
        autoRowSize: false,
        contextMenu: contextMenu,
        search: true,
        columns: columns,
        afterCreateRow: function (index, amount) {
            hot_search_basic_init.setDataAtCell(index, 0, 'path.url.' + counter);
            counter++;
        }
    });

    // asynchronously load all cell's data
    var url = appRoutes.get("BE_PATHS_DATA");
    $.ajax({
        url: url,
        type: 'GET',
        cache: false,
        contentType: false,
        processData: false,
        success:
                function (json) {
                    if (hot_search_basic_init) {
                        if (json && (json.length > 0)) {
                            var data = JSON.parse(json);
                            hot_search_basic_init.loadData(data);
                        }
                    }
                },
        error:
                function (response, status, errorThrown) {
                    // warn
                    $.alert({
                        theme: 'supervan',
                        escapeKey: true,
                        animation: 'top',
                        closeAnimation: 'bottom',
                        backgroundDismiss: true,
                        title: 'Oh oh! :(',
                        content: 'Unable to load data.<br/>'
                    });
                }
    });

    // Define search field
    var hot_search_basic_input = document.getElementById('hot_search_basic_input');

    // Setup matching function
    function onlyExactMatch(queryStr, value) {
        return queryStr.toString() === value.toString();
    }

    // Add event search
    Handsontable.Dom.addEvent(hot_search_basic_input, 'keyup', function (event) {
        var queryResult = hot_search_basic_init.search.query(this.value);

        hot_search_basic_init.render();
    });

    // Define save button
    var hot_save_button = document.getElementById('hot_save');

    // Add event save
    Handsontable.dom.addEvent(hot_save_button, 'click', function () {

        var url = appRoutes.get("BE_PATHS_SAVE");

        var data = new FormData();
        data.append('json', JSON.stringify({data: hot_search_basic_init.getData()}));

        // save all cell's data
        $.blockUI();
        $.ajax({
            url: url,
            type: 'POST',
            data: data,
            cache: false,
            contentType: false,
            processData: false,
            success:
                    function (returndata) {
                        $.unblockUI();
                    },
            error:
                    function (response, status, errorThrown) {
                        $.unblockUI();
                        // warn
                        $.alert({
                            theme: 'supervan',
                            escapeKey: true,
                            animation: 'top',
                            closeAnimation: 'bottom',
                            backgroundDismiss: true,
                            title: 'Oh oh! :(',
                            content: 'Impossibile salvare i dati.<br/>'
                        });
                    }
        });
    });

});


