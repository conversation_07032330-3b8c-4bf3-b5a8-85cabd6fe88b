const WarrantyCriteriaTest = function () {

    // Initialize Select2 dropdowns
    const _initializeSelect2 = function () {
        if (!$().select2) {
            console.warn('Warning - select2.min.js is not loaded.');
            return;
        }

        // Initialize all select2 dropdowns
        $('select[data-placeholder]').select2({
            placeholder: function() {
                return $(this).data('placeholder');
            },
            allowClear: true,
            width: '100%'
        });
    };

    // Initialize form handling
    const _initializeForm = function () {
        const form = $('#criteriaTestForm');
        const clearButton = $('#clearForm');
        
        // Form submission
        form.on('submit', function(e) {
            e.preventDefault();
            _performSearch();
        });
        
        // Clear form
        clearButton.on('click', function() {
            form[0].reset();
            $('select').val(null).trigger('change');
            $('#resultsContainer').hide();
        });
    };

    // Perform warranty search
    const _performSearch = function () {
        const form = $('#criteriaTestForm');

        // Validate that at least one criteria is filled
        const hasProvinceCode = form.find('select[name="provinceCode"]').val();
        const hasClaimNumber = form.find('input[name="claimNumber"]').val();
        const hasInsuranceProvenance = form.find('select[name="insuranceProvenanceTypeId"]').val();
        const hasUniversalClass = form.find('input[name="universalClass"]').val();

        if (!hasProvinceCode && !hasClaimNumber && !hasInsuranceProvenance && !hasUniversalClass) {
            new Noty({
                text: 'Inserisci almeno un criterio per effettuare la ricerca',
                type: 'warning'
            }).show();
            return;
        }

        const formData = form.serialize();

        // Show loading state
        const submitButton = form.find('button[type="submit"]');
        const originalText = submitButton.html();
        submitButton.html('<i class="ph-spinner ph-spin me-2"></i>Ricerca in corso...').prop('disabled', true);

        // Hide previous results
        $('#resultsContainer').hide();
        
        $.ajax({
            url: appRoutes.get('BE_WARRANTY_CRITERIA_SEARCH'),
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    _displayResults(response);
                } else {
                    new Noty({
                        text: 'Errore nella ricerca: ' + (response.error || 'Errore sconosciuto'),
                        type: 'error'
                    }).show();
                }
            },
            error: function(xhr, status, error) {
                console.error('Search error:', error);
                new Noty({
                    text: 'Errore nella comunicazione con il server',
                    type: 'error'
                }).show();
            },
            complete: function() {
                // Restore button state
                submitButton.html(originalText).prop('disabled', false);
            }
        });
    };

    // Display search results
    const _displayResults = function (response) {
        const resultsContainer = $('#resultsContainer');
        const resultsCount = $('#resultsCount');
        const resultsTable = $('#resultsTable');

        // Update count
        const count = response.totalResults || 0;
        resultsCount.html(`Trovate <strong>${count}</strong> garanzie valide per i criteri inseriti.`);

        if (count === 0) {
            resultsTable.html('<div class="alert alert-info"><i class="ph-info me-2"></i>Nessuna garanzia trovata per i criteri specificati.</div>');
        } else {
            // Build results table
            let tableHtml = '<div class="table-responsive"><table class="table table-striped">';
            tableHtml += '<thead class="table-light"><tr>';
            tableHtml += '<th>Garanzia</th>';
            tableHtml += '<th>Codice</th>';
            tableHtml += '<th>Province</th>';
            tableHtml += '<th>Range Sinistri</th>';
            tableHtml += '<th>Tipo Provenienza</th>';
            tableHtml += '<th>Range Classe Universale</th>';
            tableHtml += '<th class="text-end">Premio</th>';
            tableHtml += '</tr></thead><tbody>';

            response.results.forEach(function(result) {
                const warranty = result.warranty;
                const details = result.warrantyDetails;

                tableHtml += '<tr>';
                tableHtml += '<td><strong>' + _escapeHtml(warranty.title || 'N.D.') + '</strong>';
                if (warranty.description) {
                    tableHtml += '<br><small class="text-muted">' + _escapeHtml(warranty.description) + '</small>';
                }
                tableHtml += '</td>';
                tableHtml += '<td><code>' + _escapeHtml(warranty.code || 'N.D.') + '</code></td>';
                tableHtml += '<td>' + _formatProvinceList(details.provinceCode) + '</td>';
                tableHtml += '<td>' + _formatNumberRange(details.claimNumber) + '</td>';
                tableHtml += '<td>' + _formatInsuranceProvenance(result.insuranceProvenanceType) + '</td>';
                tableHtml += '<td>' + _formatNumberRange(details.universalClass) + '</td>';
                tableHtml += '<td class="text-end">' + _formatPremium(details.premiumValue) + '</td>';
                tableHtml += '</tr>';
            });

            tableHtml += '</tbody></table></div>';
            resultsTable.html(tableHtml);
        }

        // Show results
        resultsContainer.show();
    };

    // Helper functions for formatting
    const _escapeHtml = function (text) {
        if (!text) return 'N.D.';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    };

    const _formatProvinceList = function (provinces) {
        if (!provinces || provinces.length === 0) return 'N.D.';
        return provinces.join(', ');
    };

    const _formatNumberRange = function (numbers) {
        if (!numbers || numbers.length === 0) return 'N.D.';
        if (numbers.length === 1) return numbers[0].toString();
        
        const min = Math.min(...numbers);
        const max = Math.max(...numbers);
        return min === max ? min.toString() : min + ' - ' + max;
    };

    const _formatInsuranceProvenance = function (insuranceProvenanceType) {
        if (!insuranceProvenanceType) return 'N.D.';
        return _escapeHtml(insuranceProvenanceType.title || insuranceProvenanceType.code || 'N.D.');
    };

    const _formatPremium = function (value) {
        if (!value) return 'N.D.';
        return '€ ' + value.toFixed(2);
    };

    // Public methods
    return {
        init: function () {
            _initializeSelect2();
            _initializeForm();
        }
    };
}();

// Initialize module
document.addEventListener('DOMContentLoaded', function () {
    WarrantyCriteriaTest.init();
});
