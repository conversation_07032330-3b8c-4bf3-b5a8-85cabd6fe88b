var oldImageBase64;

const ProductForm = function () {
    var pond;
    var productId, step;

    // Initialization of components
    const init = function () {
        _loadProductId();
        _loadStep();
        _componentValidate();
        _componentMaxlength();
        _componentPermissionChecks();
        _componentSubmitProduct();
        _componentInputFormatting();

        // Step 1
        if (step === 1) {
            _componentFilePond();
        }

        // Step 2
        if (step === 2) {
            _componentAddWarranty();
            _componentWarrantiesManagement();
            // _componentSearchWarranty();
        }
    };

    const _loadProductId = function() {
        productId = getUrlParameter('productId');
    };

    const _loadStep = function() {
        step = $("[name='step']").val();
        if (!step) {
            // show alert
            showToast('Errore nel caricamento dello step', 'error');
        } else {
            step = parseInt(step);
        }
    };

    // FilePond using centralized factory
    const _componentFilePond = function () {
        pond = TiganiLibs.UIComponentFactory.initFilePond('input[type="file"]', {});

        // Load initial image if present
        var imageId = pageVariables.get("imageId");
        if (typeof imageId !== "undefined" && imageId && pond) {
            var image = appRoutes.get("BE_IMAGE") + "?oid=" + pageVariables.get("imageId").replace("[", "").replace("]", "");
            pond.addFile(image).then(file => {
                oldImageBase64 = file.getFileEncodeBase64String();
            }).catch(err => {
                console.error('Error loading image', err);
            });
        }
    };

    // Validation config using TiganiLibs
    const _componentValidate = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Custom validation rules for product
        $.validator.addMethod('productCode', function (value) {
            return /^[A-Z0-9_-]+$/.test(value);
        }, 'Il codice può contenere solo lettere maiuscole, numeri, trattini e underscore.');

        // Define custom rules for this form
        const customRules = {
            code: {
                required: true,
                productCode: true,
                maxlength: 50
            },
            name: {
                required: true,
                maxlength: 100
            },
            description: {
                maxlength: 500
            },
            category: {
                required: true
            },
            subcategory: {
                required: true
            }
        };

        // Custom options for this form
        const customOptions = {
            ignore: 'input[type=hidden], .select2-search__field',
            errorClass: 'text-red-600 text-sm mt-1',
            validClass: 'text-green-600',
            errorElement: 'div',
            highlight: function(element) {
                $(element).addClass('border-red-500').removeClass('border-gray-200');
            },
            unhighlight: function(element) {
                $(element).removeClass('border-red-500').addClass('border-gray-200');
            }
        };

        // Initialize validation using TiganiLibs factory
        const validator = TiganiLibs.FormValidationFactory.create('#product-form', customRules, customOptions);
    };

    // Maxlength using TiganiLibs
    const _componentMaxlength = function () {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Initialize maxlength using TiganiLibs
        TiganiLibs.ComponentUtils.initMaxlength('.maxlength');
    };

    // Permission checks
    const _componentPermissionChecks = function () {
        // Check if user has permission to create/edit products
        const isNewProduct = !productId || productId === '';

        if (isNewProduct && !hasPermission('PRODUCT_MANAGEMENT', 'create')) {
            // Disable all form inputs
            $('#product-form input, #product-form textarea, #product-form select').prop('disabled', true);
            $('#product-form button[type="submit"]').prop('disabled', true);

            // Add visual indication
            $('#product-form').addClass('opacity-50');
            showToast('Non hai i permessi per creare prodotti.', 'warning');
        } else if (!isNewProduct && !hasPermission('PRODUCT_MANAGEMENT', 'edit')) {
            // Disable all form inputs
            $('#product-form input, #product-form textarea, #product-form select').prop('disabled', true);
            $('#product-form button[type="submit"]').prop('disabled', true);

            // Add visual indication
            $('#product-form').addClass('opacity-50');
            showToast('Non hai i permessi per modificare prodotti.', 'warning');
        }
    };

    // Form submission handling
    const _componentSubmitProduct = function () {
        // Handle main form submission (Save and Continue)
        $('#product-form').submit(function (e) {
            e.preventDefault();

            if ($(this).valid()) {
                _submitForm('continue');
            }
        });
    };

    // Submit form with specified action
    const _submitForm = function(action) {
        const form = $('#product-form')[0];
        const formData = new FormData(form);
        const isNewProduct = !productId || productId === '';

        // Add action type
        formData.append('action', action);

        // Add product ID if editing
        if (!isNewProduct) {
            formData.append('productId', productId);
        }

        // Handle FilePond file if present
        try {
            if (pond.getFiles().length > 0) {
                // Ottieni la stringa base64 del file croppato
                const fileToInsert = pond.getFiles()[0];
                const base64String = fileToInsert.getFileEncodeBase64String();

                if (oldImageBase64 === base64String) {
                    // add field to specify that image is the same and should not be updated
                    formData.append('sameImage', true);
                } else {
                    const mimeType = fileToInsert.fileType;
                    const blob = base64ToBlob(base64String, mimeType);
                    const fileName = fileToInsert.filename;
                    const file = new File([blob], fileName, {type: mimeType});

                    // Aggiungi il file croppato al FormData
                    formData.append('file', file);
                }
            }
        } catch (fileError) {
            console.warn('Error processing file upload:', fileError);
            // Continue without file if there's an error
        }

        // Check permissions
        if (isNewProduct && !hasPermission('PRODUCT_MANAGEMENT', 'create')) {
            showToast('Non hai i permessi per creare prodotti.', 'error');
            return;
        } else if (!isNewProduct && !hasPermission('PRODUCT_MANAGEMENT', 'edit')) {
            showToast('Non hai i permessi per modificare prodotti.', 'error');
            return;
        }

        // Show loading state
        const submitBtn = action === 'draft' ? $('#save-draft-btn') : $('#save-continue-btn');
        const originalText = submitBtn.html();
        submitBtn.prop('disabled', true).html('<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Salvando...');

        // Block UI
        // $.blockUI();

        // Submit form
        $.ajax({
            url: appRoutes.get('BE_PRODUCT_SAVE'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                // $.unblockUI();

                if (response.success) {
                    const message = action === 'draft' ? 'Bozza salvata con successo' : 'Prodotto salvato con successo';
                    showToast(message, 'success');

                    if (action === 'continue') {
                        // Navigate to next step
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        // Just reset button for draft save
                        submitBtn.prop('disabled', false).html(originalText);
                    }
                } else {
                    const errorMessage = response.message || 'Errore durante il salvataggio del prodotto';
                    showToast(errorMessage, 'error');
                    submitBtn.prop('disabled', false).html(originalText);
                }
            },
            error: function(xhr, status, error) {
                // $.unblockUI();
                submitBtn.prop('disabled', false).html(originalText);

                let errorMessage = 'Errore durante il salvataggio del prodotto';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.status === 403) {
                    errorMessage = 'Non hai i permessi per questa operazione';
                } else if (xhr.status === 400) {
                    errorMessage = 'Dati del form non validi';
                }

                showToast(errorMessage, 'error');
                console.error('Error during product save:', {
                    status: status,
                    error: error,
                    xhr: xhr
                });
            }
        });
    };

    // Input formatting and validation
    const _componentInputFormatting = function() {
        // Code field - uppercase transformation
        $('input[name="code"]').on('input', function() {
            this.value = this.value.toUpperCase().replace(/[^A-Z0-9_-]/g, '');
        });

        // Name field - trim whitespace
        $('input[name="name"]').on('blur', function() {
            this.value = this.value.trim();
        });

        // Description field - trim whitespace
        $('textarea[name="description"]').on('blur', function() {
            this.value = this.value.trim();
        });
    };

    function _componentAddWarranty() {
        // Create Warranty Button Handler
        const createWarrantyBtn = document.getElementById('create-warranty-btn');
        if (createWarrantyBtn) {
            createWarrantyBtn.addEventListener('click', function() {
                try {
                    // Check if required functions are available
                    if (typeof createDynamicOffcanvas !== 'function') {
                        showToast('Errore: funzione offcanvas non disponibile', 'error');
                        return;
                    }

                    if (!appRoutes.has('BE_WARRANTY_FORM')) {
                        showToast('Errore: route non configurata', 'error');
                        return;
                    }

                    const offcanvas = createDynamicOffcanvas({
                        title: 'Nuova Garanzia',
                        url: appRoutes.get('BE_WARRANTY_FORM'),
                        entity: 'warranty',
                        tabs: [
                            {
                                name: 'Cerca',
                                url: appRoutes.get('BE_WARRANTY_SEARCH_FORM')
                            }
                        ],
                        onContentLoaded: function(offcanvasElement, contentContainer) {
                            try {
                                // Initialize warranty form components after content is loaded
                                if (typeof WarrantyForm !== 'undefined' && WarrantyForm.init) {
                                    var options = {
                                        callback: function (response) {
                                            // Handle warranty creation success
                                            try {
                                                // Parse response if it's a string
                                                let warrantyId = response;

                                                if (warrantyId && productId) {
                                                    // Associate warranty with product
                                                    _addWarrantyToProduct(productId, warrantyId);
                                                } else {
                                                    console.warn('Missing warrantyId or productId for association');
                                                    showToast('Garanzia creata ma non associata al prodotto', 'warning');
                                                }
                                            } catch (error) {
                                                console.error('Error in warranty creation callback:', error);
                                                showToast('Garanzia creata ma errore nell\'associazione', 'warning');
                                            }
                                        }
                                    }
                                    WarrantyForm.init(options);
                                }
                            } catch (initError) {
                                console.error('Error initializing form:', initError);
                                showToast('Errore nell\'inizializzazione del modulo', 'error');
                            }
                        }
                    });
                } catch (error) {
                    console.error('Error creating offcanvas:', error);
                    showToast('Errore nell\'apertura del modulo', 'error');
                }
            });
        } else {
            console.warn('Create warranty button not found');
        }
    }

    /*// Helper function to add warranty to product
    function _addWarrantyToProduct(productId, warrantyId, successCallback, errorCallback) {
        if (!productId || !warrantyId) {
            console.error('Missing productId or warrantyId for association');
            if (typeof errorCallback === 'function') {
                errorCallback();
            }
            return;
        }

        const formData = new FormData();
        formData.append('productId', productId);
        formData.append('warrantyId', warrantyId);

        $.ajax({
            url: appRoutes.get('BE_PRODUCT_ADD_WARRANTY'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                try {
                    let result = response;
                    if (typeof response === 'string') {
                        result = JSON.parse(response);
                    }

                    if (result.success) {
                        showToast('Garanzia associata al prodotto con successo', 'success');
                        // Refresh warranties list
                        _loadWarranties();
                        if (typeof successCallback === 'function') {
                            successCallback();
                        }
                    } else {
                        showToast(result.message || 'Errore nell\'associazione della garanzia', 'error');
                        if (typeof errorCallback === 'function') {
                            errorCallback();
                        }
                    }
                } catch (error) {
                    console.error('Error parsing warranty association response:', error);
                    showToast('Errore nell\'elaborazione della risposta', 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error associating warranty to product:', error);
                let errorMessage = 'Errore nell\'associazione della garanzia';

                if (xhr.responseText) {
                    try {
                        const errorResponse = JSON.parse(xhr.responseText);
                        errorMessage = errorResponse.message || errorMessage;
                    } catch (e) {
                        errorMessage = xhr.responseText;
                    }
                }

                showToast(errorMessage, 'error');
                if (typeof errorCallback === 'function') {
                    errorCallback();
                }
            }
        });
    }*/

    function _componentSearchWarranty() {
        let searchTimeout;
        const searchInput = $('#searchWarranty');
        const resultsContainer = $('#warranty-search-results');
        const loadingIndicator = $('#warranty-search-loading');
        const noResultsMessage = $('#warranty-search-no-results');

        // Debounced search function
        function performSearch(query) {
            if (!query || query.trim().length < 2) {
                resultsContainer.empty();
                hideLoadingAndNoResults();
                return;
            }

            showLoading();
            var data = {q: query.trim()};
            if (productId) {
                data.productId = productId;
            }

            $.ajax({
                url: appRoutes.get('BE_WARRANTY_SEARCH'),
                type: 'GET',
                data: data,
                dataType: 'json',
                success: function (response) {
                    hideLoading();
                    displayResults(response.results || []);
                },
                error: function (xhr, status, error) {
                    hideLoading();
                    console.error('Warranty search error:', error);
                    showError('Errore durante la ricerca delle garanzie');
                }
            });
        }

        // Display search results
        function displayResults(results) {
            resultsContainer.empty();

            if (results.length === 0) {
                showNoResults();
                return;
            }

            hideNoResults();

            // For each result, fetch and render the template
            results.forEach(function (warrantyEntry) {
                renderWarrantyResult(warrantyEntry);
            });
        }

        // Render a single warranty result using the template
        function renderWarrantyResult(warrantyEntry) {
            // Create a temporary container to render the template
            const tempContainer = $('<div>');

            // Build the HTML manually since we can't easily use Pebble on client-side
            const warrantyHtml = buildWarrantyHtml(warrantyEntry);
            tempContainer.html(warrantyHtml);

            // Append to results container
            resultsContainer.append(tempContainer.html());
        }

        // Build warranty HTML manually
        function buildWarrantyHtml(warrantyEntry) {
            const warranty = warrantyEntry.warranty || {};
            const warrantyType = warrantyEntry.warrantyType || {};
            const insuranceCompany = warrantyEntry.insuranceCompany || {};

            const criteriaFields = formatWarrantyCriteriaFields(warranty.criteriaFields);

            return `
        <div class="p-5 flex flex-col bg-gray-50 border border-gray-200 text-sm text-gray-600 rounded-lg p-4 dark:bg-white/10 dark:border-white/10 dark:text-neutral-400 warranty-result-item" data-warranty-id="${warranty._id.$oid || ''}">
            <dl class="grid grid-cols-2 gap-x-2">
                <dt class="py-1 text-sm text-gray-500 dark:text-neutral-500">Codice:</dt>
                <dd class="py-1 inline-flex justify-end items-center gap-x-2 text-end font-medium text-sm text-gray-800 dark:text-neutral-200">${warranty.code || 'N/A'}</dd>

                <dt class="py-1 text-sm text-gray-500 dark:text-neutral-500">Titolo:</dt>
                <dd class="py-1 inline-flex justify-end items-center gap-x-2 text-end font-medium text-sm text-gray-800 dark:text-neutral-200">${warranty.title || 'N/A'}</dd>

                <dt class="py-1 text-sm text-gray-500 dark:text-neutral-500">Tipo:</dt>
                <dd class="py-1 inline-flex justify-end items-center gap-x-2 text-end font-medium text-sm text-gray-800 dark:text-neutral-200">${warrantyType.name || 'N/A'}</dd>

                <dt class="py-1 text-sm text-gray-500 dark:text-neutral-500">Compagnia:</dt>
                <dd class="py-1 inline-flex justify-end items-center gap-x-2 text-end font-medium text-sm text-gray-800 dark:text-neutral-200">${insuranceCompany.description || 'N/A'}</dd>

                <dt class="py-1 text-sm text-gray-500 dark:text-neutral-500">Criteri applicabili:</dt>
                <dd class="py-1 inline-flex justify-end items-center gap-x-2 text-end font-medium text-sm text-gray-800 dark:text-neutral-200">${criteriaFields}</dd>

                <dt class="py-1 text-sm text-gray-500 dark:text-neutral-500 col-span-2 border-t border-gray-200 dark:border-neutral-800 pt-2 mt-1">Descrizione:</dt>
                <dd class="py-1 text-sm text-gray-600 dark:text-neutral-400 col-span-2 leading-relaxed">${warranty.description || 'Nessuna descrizione disponibile'}</dd>
            </dl>
            <div class="block text-end">
                <button type="button" class="py-2 px-3 mt-2 inline-flex justify-center items-center gap-x-2 text-start bg-gray-800 border border-gray-800 text-white text-sm font-medium rounded-lg shadow-2xs align-middle hover:bg-gray-950 focus:outline-hidden focus:bg-gray-900 dark:bg-white dark:text-neutral-800 dark:hover:bg-neutral-200 dark:focus:bg-neutral-200 warranty-add-btn"
                        data-warranty-id="${warranty._id.$oid || ''}"
                        data-warranty-code="${warranty.code || ''}"
                        data-warranty-title="${warranty.title || ''}">
                    Aggiungi
                </button>
            </div>
        </div>
    `;
        }

        // Show loading indicator
        function showLoading() {
            loadingIndicator.removeClass('hidden');
            noResultsMessage.addClass('hidden');
        }

        // Hide loading indicator
        function hideLoading() {
            loadingIndicator.addClass('hidden');
        }

        // Show no results message
        function showNoResults() {
            noResultsMessage.removeClass('hidden');
        }

        // Hide no results message
        function hideNoResults() {
            noResultsMessage.addClass('hidden');
        }

        // Hide both loading and no results
        function hideLoadingAndNoResults() {
            hideLoading();
            hideNoResults();
        }

        // Show error message
        function showError(message) {
            resultsContainer.html(`
        <div class="text-center py-4">
            <p class="text-sm text-red-500">${message}</p>
        </div>
    `);
        }

        // Search input event handler with debouncing
        searchInput.on('input', function () {
            const query = $(this).val();

            // Clear previous timeout
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            // Set new timeout for debounced search
            searchTimeout = setTimeout(function () {
                performSearch(query);
            }, 300); // 300ms delay
        });

        // Handle "Aggiungi" button clicks (event delegation for dynamic content)
        resultsContainer.on('click', '.warranty-add-btn', function() {
            const button = $(this);
            const warrantyId = button.data('warranty-id');

            if (!warrantyId) {
                showToast('Errore: ID garanzia non trovato', 'error');
                return;
            }

            if (!productId) {
                showToast('Errore: ID prodotto non trovato', 'error');
                return;
            }

            // Disable button during processing
            button.prop('disabled', true).text('Aggiungendo...');

            // Add warranty to product
            _addWarrantyToProduct(productId, warrantyId, function() {
                // Success callback - re-enable button
                button.prop('disabled', false).text('Aggiunto');
                button.removeClass('bg-gray-800 hover:bg-gray-950')
                      .addClass('bg-green-600 hover:bg-green-700');

                // Reset button after 2 seconds
                setTimeout(function() {
                    button.text('Aggiungi')
                          .removeClass('bg-green-600 hover:bg-green-700')
                          .addClass('bg-gray-800 hover:bg-gray-950');
                }, 2000);
            }, function() {
                // Error callback - re-enable button
                button.prop('disabled', false).text('Aggiungi');
            });
        });
    }

    // ========================================
    // PRODUCT WARRANTIES MANAGEMENT
    // ========================================

    // Private variables for warranties
    let warranties = [];

    // DOM elements for warranties
    const emptyStateElement = document.getElementById('warranties-empty-state');
    const listElement = document.getElementById('warranties-list');
    const listContainer = document.getElementById('warranties-list-container');

    // Initialize warranties management
    function _componentWarrantiesManagement() {
        _setupWarrantiesEventHandlers();
        _loadWarranties();
    }

    // Setup event handlers for warranties
    function _setupWarrantiesEventHandlers() {
        // Handle "Inserisci garanzia" buttons (both in empty state and list)
        $(document).on('click', '#create-warranty-btn, #add-warranty-btn', function(e) {
            e.preventDefault();
            _handleAddWarranty();
        });

        // Handle edit warranty buttons
        $(document).on('click', '.warranty-edit-btn', function(e) {
            e.preventDefault();
            const warrantyId = $(this).data('warranty-id');
            const warrantyTitle = $(this).data('warranty-title');
            _handleEditWarranty(warrantyId, warrantyTitle);
        });

        // Handle delete warranty buttons
        $(document).on('click', '.warranty-delete-btn', function(e) {
            e.preventDefault();
            const warrantyId = $(this).data('warranty-id');
            const warrantyTitle = $(this).data('warranty-title');
            _handleDeleteWarranty(warrantyId, warrantyTitle);
        });
    }

    // Load warranties for the product
    function _loadWarranties() {
        if (!productId) {
            _showWarrantiesError('ID prodotto non disponibile');
            return;
        }

        $.ajax({
            url: appRoutes.get('BE_PRODUCT_WARRANTIES'),
            type: 'GET',
            data: { productId: productId },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    warranties = JSON.parse(response.warranties) || [];
                    _displayWarranties();
                } else {
                    _showWarrantiesError(response.message || 'Errore nel caricamento delle garanzie');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading warranties:', error);
                _showWarrantiesError('Errore nel caricamento delle garanzie');
            }
        });
    }

    // Display warranties based on current state
    function _displayWarranties() {
        if (warranties.length === 0) {
            _showEmptyState();
        } else {
            _showWarrantiesList();
        }
    }

    // Show empty state
    function _showEmptyState() {
        if (emptyStateElement) emptyStateElement.classList.remove('hidden');
        if (listElement) listElement.classList.add('hidden');
    }

    // Show warranties list
    function _showWarrantiesList() {
        if (emptyStateElement) emptyStateElement.classList.add('hidden');
        if (listElement) listElement.classList.remove('hidden');

        _renderWarrantiesList();
    }

    // Render warranties list
    function _renderWarrantiesList() {
        if (!listContainer) {
            console.error('Warranties list container not found');
            return;
        }

        listContainer.innerHTML = '';

        warranties.forEach(function(warrantyEntry) {
            const listItem = _createWarrantyListItem(warrantyEntry);
            listContainer.appendChild(listItem);
        });
    }

    // Create warranty list item
    function _createWarrantyListItem(warrantyEntry) {
        const warranty = warrantyEntry.warranty || {};
        const warrantyType = warrantyEntry.warrantyType || {};
        const insuranceCompany = warrantyEntry.insuranceCompany || {};

        const li = document.createElement('li');
        li.className = 'py-3 border-b last:border-b-0 border-gray-200 dark:border-neutral-700';
        li.setAttribute('data-warranty-id', warranty._id.$oid || '');

        var iconImg = pageVariables.get("contextPath") + '/img/warranties/' + warrantyType.icon + '.svg';
        li.innerHTML = `
            <div class="flex gap-x-3">
                <img class="shrink-0 size-8 rounded-full" src="${iconImg}" alt="${warrantyType.name || 'N/A'}">
                <div class="grow">
                    <span class="font-medium text-gray-800 dark:text-neutral-200">
                        ${warranty.title || 'Titolo non disponibile'}
                    </span>
                    <p class="text-xs text-gray-500 dark:text-neutral-500">
                        ${insuranceCompany.description || warrantyType.name || 'N/A'}
                    </p>
                </div>

                <!-- Button Group -->
                <div>
                    <div class="flex border border-gray-200 divide-x divide-gray-200 rounded-lg -space-x-px dark:border-neutral-700 dark:divide-neutral-700">
                        <!-- Edit Button Tooltip -->
                        <div class="hs-tooltip inline-block">
                            <button type="button" class="warranty-edit-btn hs-tooltip-toggle size-8 inline-flex justify-center items-center gap-x-2 rounded-s-md bg-white text-gray-500 shadow-2xs hover:bg-gray-50 hover:text-gray-800 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 focus:text-gray-800 dark:bg-neutral-800 dark:text-neutral-500 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 dark:hover:text-neutral-200 dark:focus:text-neutral-200"
                                    data-warranty-id="${warranty._id.$oid || ''}"
                                    data-warranty-title="${warranty.title || ''}">
                                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/><path d="m15 5 4 4"/>
                                </svg>
                            </button>
                            <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg dark:bg-neutral-700" role="tooltip">
                                Modifica
                            </span>
                        </div>
                        <!-- End Edit Button Tooltip -->
                        <!-- Delete Button Tooltip -->
                        <div class="hs-tooltip inline-block">
                            <button type="button" class="warranty-delete-btn hs-tooltip-toggle size-8 inline-flex justify-center items-center gap-x-2 rounded-e-md bg-white text-gray-500 shadow-2xs hover:bg-gray-50 hover:text-gray-800 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 focus:text-gray-800 dark:bg-neutral-800 dark:text-neutral-500 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 dark:hover:text-neutral-200 dark:focus:text-neutral-200"
                                    data-warranty-id="${warranty._id.$oid || ''}"
                                    data-warranty-title="${warranty.title || ''}">
                                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/>
                                </svg>
                            </button>
                            <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg dark:bg-neutral-700" role="tooltip">
                                Elimina
                            </span>
                        </div>
                        <!-- End Delete Button Tooltip -->
                    </div>
                </div>
                <!-- End Button Group -->
            </div>
        `;

        return li;
    }

    // Show error message for warranties
    function _showWarrantiesError(message) {
        if (emptyStateElement) emptyStateElement.classList.add('hidden');
        if (listElement) listElement.classList.add('hidden');

        // Show error in container
        const container = document.getElementById('warranties-container');
        if (container) {
            container.innerHTML = `
                <div class="bg-red-50 border border-red-200 text-red-800 rounded-lg p-4 dark:bg-red-800/10 dark:border-red-900 dark:text-red-500">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-4 w-4 text-red-400 mt-0.5" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM5.354 4.646a.5.5 0 1 0-.708.708L7.293 8l-2.647 2.646a.5.5 0 0 0 .708.708L8 8.707l2.646 2.647a.5.5 0 0 0 .708-.708L8.707 8l2.647-2.646a.5.5 0 0 0-.708-.708L8 7.293 5.354 4.646z"/>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium">Errore</h3>
                            <div class="mt-2 text-sm">
                                <p>${message}</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    // Handle add warranty
    function _handleAddWarranty() {
        // Check if required functions are available
        if (typeof createDynamicOffcanvas !== 'function') {
            showToast('Errore: funzione offcanvas non disponibile', 'error');
            return;
        }

        if (!appRoutes.has('BE_WARRANTY_FORM')) {
            showToast('Errore: route non configurata', 'error');
            return;
        }

        try {
            const offcanvas = createDynamicOffcanvas({
                title: 'Nuova Garanzia',
                url: appRoutes.get('BE_WARRANTY_FORM'),
                entity: 'warranty',
                tabs: [
                    {
                        name: 'Cerca',
                        url: appRoutes.get('BE_WARRANTY_SEARCH_FORM')
                    }
                ],
                onContentLoaded: function(offcanvasElement, contentContainer) {
                    try {
                        // Initialize warranty form components after content is loaded
                        if (typeof WarrantyForm !== 'undefined' && WarrantyForm.init) {
                            var options = {
                                callback: function (response) {
                                    // Handle warranty creation success
                                    try {
                                        // Parse response if it's a string
                                        let warrantyId = response;

                                        if (warrantyId && productId) {
                                            // Associate warranty with product
                                            _addWarrantyToProduct(productId, warrantyId);
                                        } else {
                                            console.warn('Missing warrantyId or productId for association');
                                            showToast('Garanzia creata ma non associata al prodotto', 'warning');
                                            // Still refresh the list in case something changed
                                            _loadWarranties();
                                        }
                                    } catch (error) {
                                        console.error('Error in warranty creation callback:', error);
                                        showToast('Garanzia creata ma errore nell\'associazione', 'warning');
                                        // Still refresh the list in case something changed
                                        _loadWarranties();
                                    }
                                }
                            }
                            WarrantyForm.init(options);
                        }
                    } catch (initError) {
                        console.error('Error initializing form:', initError);
                        showToast('Errore nell\'inizializzazione del modulo', 'error');
                    }
                }
            });
        } catch (error) {
            console.error('Error creating offcanvas:', error);
            showToast('Errore nell\'apertura del modulo', 'error');
        }
    }

    // Handle edit warranty
    function _handleEditWarranty(warrantyId, warrantyTitle) {
        if (!warrantyId) {
            showToast('Errore: ID garanzia non trovato', 'error');
            return;
        }

        // Check if required functions are available
        if (typeof createDynamicOffcanvas !== 'function') {
            showToast('Errore: funzione offcanvas non disponibile', 'error');
            return;
        }

        if (!appRoutes.has('BE_WARRANTY_FORM')) {
            showToast('Errore: route non configurata', 'error');
            return;
        }

        const offcanvas = createDynamicOffcanvas({
            title: 'Modifica Garanzia: ' + (warrantyTitle || 'Sconosciuto'),
            url: appRoutes.get('BE_WARRANTY_FORM') + '?warrantyId=' + encodeURIComponent(warrantyId),
            entity: 'warranty',
            entityId: warrantyId,
            onContentLoaded: function(offcanvasElement, contentContainer) {
                try {
                    // Initialize warranty form components after content is loaded
                    if (typeof WarrantyForm !== 'undefined' && WarrantyForm.init) {
                        WarrantyForm.init();
                    }
                } catch (initError) {
                    console.error('Error initializing form:', initError);
                    showToast('Errore nell\'inizializzazione del modulo', 'error');
                }
            },
            onClose: function() {
                // Refresh warranties list when offcanvas is closed
                _loadWarranties();
            }
        });
    }

    // Handle delete warranty
    function _handleDeleteWarranty(warrantyId, warrantyTitle) {
        if (!warrantyId) {
            showToast('Errore: ID garanzia non trovato', 'error');
            return;
        }

        // Show confirmation dialog
        $.confirm({
            title: 'Conferma rimozione',
            content: `Sei sicuro di voler rimuovere la garanzia "${warrantyTitle || 'Sconosciuto'}" dal prodotto?`,
            theme: 'preline',
            type: 'red',
            typeAnimated: true,
            columnClass: 'col-lg-4 col-lg-offset-4 col-md-6 col-md-offset-3 col-sm-8 col-sm-offset-2',
            buttons: {
                elimina: {
                    text: 'Rimuovi',
                    btnClass: 'btn-red',
                    action: function () {
                        // Remove warranty from product
                        _removeWarrantyFromProduct(warrantyId);
                    }
                },
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-light'
                }
            }
        });
    }

    // Remove warranty from product
    function _removeWarrantyFromProduct(warrantyId) {
        if (!productId || !warrantyId) {
            showToast('Errore: parametri mancanti', 'error');
            return;
        }

        const formData = new FormData();
        formData.append('productId', productId);
        formData.append('warrantyId', warrantyId);

        $.ajax({
            url: appRoutes.get('BE_PRODUCT_REMOVE_WARRANTY'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                try {
                    let result = response;
                    if (typeof response === 'string') {
                        result = JSON.parse(response);
                    }

                    if (result.success) {
                        showToast('Garanzia rimossa dal prodotto con successo', 'success');
                        _loadWarranties(); // Refresh the list
                    } else {
                        showToast(result.message || 'Errore nella rimozione della garanzia', 'error');
                    }
                } catch (error) {
                    console.error('Error parsing warranty removal response:', error);
                    showToast('Errore nell\'elaborazione della risposta', 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error removing warranty from product:', error);
                let errorMessage = 'Errore nella rimozione della garanzia';

                if (xhr.responseText) {
                    try {
                        const errorResponse = JSON.parse(xhr.responseText);
                        errorMessage = errorResponse.message || errorMessage;
                    } catch (e) {
                        errorMessage = xhr.responseText;
                    }
                }

                showToast(errorMessage, 'error');
            }
        });
    }

    // Helper function to add warranty to product
    function _addWarrantyToProduct(productId, warrantyId) {
        if (!productId || !warrantyId) {
            console.error('Missing productId or warrantyId for association');
            return;
        }

        const formData = new FormData();
        formData.append('productId', productId);
        formData.append('warrantyId', warrantyId);

        $.ajax({
            url: appRoutes.get('BE_PRODUCT_ADD_WARRANTY'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                try {
                    let result = response;
                    if (typeof response === 'string') {
                        result = JSON.parse(response);
                    }

                    if (result.success) {
                        showToast('Garanzia associata al prodotto con successo', 'success');
                        _loadWarranties(); // Refresh the list
                    } else {
                        showToast(result.message || 'Errore nell\'associazione della garanzia', 'error');
                    }
                } catch (error) {
                    console.error('Error parsing warranty association response:', error);
                    showToast('Errore nell\'elaborazione della risposta', 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error associating warranty to product:', error);
                let errorMessage = 'Errore nell\'associazione della garanzia';

                if (xhr.responseText) {
                    try {
                        const errorResponse = JSON.parse(xhr.responseText);
                        errorMessage = errorResponse.message || errorMessage;
                    } catch (e) {
                        errorMessage = xhr.responseText;
                    }
                }

                showToast(errorMessage, 'error');
            }
        });
    }

    // Public method to refresh warranties (called from other modules)
    function refreshWarranties() {
        _loadWarranties();
    }

    // Return objects assigned to module
    return {
        init: init,
        componentSearchWarranty: _componentSearchWarranty,
        refreshWarranties: refreshWarranties
    };
}();

// Initialize module
// ------------------------------

document.addEventListener('DOMContentLoaded', function () {
    ProductForm.init();
});
