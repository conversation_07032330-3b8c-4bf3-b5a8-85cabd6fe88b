// Global variables for table management
const UserCollection = function () {

    // Datatable using centralized factory
    const _componentDatatable = function () {
        /*// Set starting date
        var params = [];
        var startDate = moment().subtract(29, 'days').format('DD/MM/YYYY');
        var endDate = moment().format('DD/MM/YYYY');
        if (startDate && startDate.trim() !== '') {
            params.push("startDate=" + encodeURIComponent(startDate));
        }
        if (endDate && endDate.trim() !== '') {
            params.push("endDate=" + encodeURIComponent(endDate));
        }*/

        // Custom configuration specific to user collection
        const customConfig = {
            ajax: {
                url: appRoutes.get("BE_USER_DATA")/* + (params.length > 0 ? '?' + params.join('&') : '')*/,
                type: 'GET'
            },
            columnDefs: [
                {
                    targets: 4,
                    render: function(data, type, row, meta) {
                        if (type === 'display') {
                            const statusMap = {
                                'system': '<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800/30 dark:text-gray-500">System</span>',
                                'admin': '<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-800/30 dark:text-purple-500">Admin</span>',
                                'customer': '<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800/30 dark:text-green-500">Cliente</span>',
                                'unconfirmed': '<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-800/30 dark:text-yellow-500">Non Confermato</span>'
                            };
                            return statusMap[data] || `<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800/30 dark:text-gray-500">${data}</span>`;
                        }
                        return data;
                    }
                },
                {
                    targets: -1,
                    orderable: false,
                    className: 'w-0 text-center',
                    render: function(data, type, row, meta) {
                        if (type === 'display') {
                            return _renderActionDropdown(row);
                        }
                        return data;
                    }
                }
            ]
        };

        // Create DataTable using centralized factory - base config is cloned and merged with custom options
        const hsDataTable = TiganiLibs.DataTableFactory.create('#user-datatable-container', customConfig);

        // On draw initialize HS components and setup checkbox handlers
        hsDataTable.dataTable.on('draw', function() {
            HSStaticMethods.autoInit();
            _setupCheckboxHandlers();
        });

        window.usersDataTable = hsDataTable;

        const buttons = document.querySelectorAll('#hs-dropdown-datatable-with-export .hs-dropdown-menu button');
        buttons.forEach((btn) => {
            const type = btn.getAttribute('data-hs-datatable-action-type');

            btn.addEventListener('click', () => hsDataTable.dataTable.button(`.buttons-${type}`).trigger());
        });

        // Initial setup of checkbox handlers
        _setupCheckboxHandlers();
    };

    // Action dropdown renderer
    function _renderActionDropdown(row) {
        // Extract user ID from the link in the second column (index 1)
        const linkHtml = row[1];
        const userIdMatch = linkHtml.match(/userId='([^']+)'/);
        const userId = userIdMatch ? userIdMatch[1] : '';
        const userType = row[4];

        // Build action items based on permissions
        let actionItems = '';
        
        // Confirm action - requires edit permission
        if (hasPermission('USER_MANAGEMENT', 'edit') && userType === 'unconfirmed') {
            actionItems += `
                <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700" href="#" onclick="_confirmSingleRow('${userId}'); return false;">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                    Conferma
                </a>`;
        }

        // Archive action - requires edit permission
        if (hasPermission('USER_MANAGEMENT', 'edit')) {
            actionItems += `
                <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700" href="#" onclick="_archiveSingleRow('${userId}'); return false;">
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="5" x="2" y="3" rx="1"/><path d="M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8"/><path d="M10 12h4"/></svg>
                    Archivia
                </a>`;
        }

        // Delete action - requires delete permission
        if (hasPermission('USER_MANAGEMENT', 'delete')) {
            actionItems += `
                <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-red-600 hover:bg-red-100 focus:outline-hidden focus:bg-red-100 dark:text-red-500 dark:hover:bg-red-800/30 dark:focus:bg-red-800/30" href="#" onclick="_deleteSingleRow('${userId}'); return false;">
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c-1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>
                    Elimina
                </a>`;
        }

        // If no actions are available, don't show the dropdown
        if (!actionItems.trim()) {
            return '<span class="text-sm text-gray-500 dark:text-neutral-400">Nessuna azione disponibile</span>';
        }

        return `
            <div class="hs-dropdown relative inline-flex">
                <button id="hs-table-dropdown-${userId || Math.random()}" type="button" class="hs-dropdown-toggle py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                    Azioni
                    <svg class="hs-dropdown-open:rotate-180 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                </button>
                <div class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden divide-y divide-gray-200 min-w-40 z-20 bg-white shadow-2xl rounded-lg p-2 mt-2 dark:divide-neutral-700 dark:bg-neutral-800 dark:border dark:border-neutral-700" role="menu" aria-orientation="vertical">
                    <div class="py-2 first:pt-0 last:pb-0">
                        ${actionItems}
                    </div>
                </div>
            </div>
        `;
    }

    function _componentDatepicker() {
        var start = moment().subtract(29, 'days');
        var end = moment();

        function cb(start, end) {
            $('#reportrange span').html(start.format('MMMM D, YYYY') + ' - ' + end.format('MMMM D, YYYY'));
        }

        $('#reportrange').daterangepicker({
            startDate: start,
            endDate: end,
            opens: 'left',
            ranges: {
                'Oggi': [moment(), moment()],
                'Ieri': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'Ultimi 7 Giorni': [moment().subtract(6, 'days'), moment()],
                'Ultimi 30 Giorni': [moment().subtract(29, 'days'), moment()],
                'Questo Mese': [moment().startOf('month'), moment().endOf('month')],
                'Mese Scorso': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            },
            locale: {
                format: 'DD/MM/YYYY',
                applyLabel: 'Applica',
                cancelLabel: 'Annulla',
                startLabel: 'Data inizio',
                endLabel: 'Data fine',
                customRangeLabel: 'Personalizzato',
                daysOfWeek: ['Do', 'Lu', 'Ma', 'Me', 'Gi', 'Ve', 'Sa'],
                monthNames: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
                firstDay: 1
            }
        }, cb);

        // Add apply event handler for date filtering
        $('#reportrange').on('apply.daterangepicker', function(ev, picker) {
            var startDate = picker.startDate.format('DD/MM/YYYY');
            var endDate = picker.endDate.format('DD/MM/YYYY');
            _reloadTableWithDateFilter(startDate, endDate);
        });

        cb(start, end);
    }

    function _componentAddUser() {
        // Create User Button Handler
        const createUserBtn = document.getElementById('create-user-btn');
        if (createUserBtn) {
            createUserBtn.addEventListener('click', function() {
                try {
                    // Check if required functions are available
                    if (typeof createDynamicOffcanvas !== 'function') {
                        showToast('Errore: funzione offcanvas non disponibile', 'error');
                        return;
                    }

                    if (!appRoutes.has('BE_USER_FORM')) {
                        showToast('Errore: route non configurata', 'error');
                        return;
                    }

                    const offcanvas = createDynamicOffcanvas({
                        title: 'Nuovo Utente',
                        url: appRoutes.get('BE_USER_FORM'),
                        entity: 'user',
                        onContentLoaded: function(offcanvasElement, contentContainer) {
                            try {
                                // Initialize user form components after content is loaded
                                if (typeof UserForm !== 'undefined' && UserForm.init) {
                                    UserForm.init();
                                }
                            } catch (initError) {
                                console.error('Error initializing form:', initError);
                                showToast('Errore nell\'inizializzazione del modulo', 'error');
                            }
                        },
                        onClose: function() {
                            // Clean up any global variables if needed
                            if (typeof pond !== 'undefined' && pond && pond.destroy) {
                                try {
                                    pond.destroy();
                                } catch (e) {
                                    console.warn('Error destroying FilePond:', e);
                                }
                            }
                        }
                    });
                } catch (error) {
                    console.error('Error creating offcanvas:', error);
                    showToast('Errore nell\'apertura del modulo', 'error');
                }
            });
        } else {
            console.warn('Create user button not found');
        }
    }

    function _componentEditUser() {
        // Edit User Click Handler for table rows
        $(document).on('click', 'a[userId]', function(e) {
            e.preventDefault();

            try {
                const userId = $(this).attr('userId');
                const userName = $(this).text().trim();

                if (!userId) {
                    showToast('Errore: ID utente non trovato', 'error');
                    return;
                }

                // Check if required functions are available
                if (typeof createDynamicOffcanvas !== 'function') {
                    showToast('Errore: funzione offcanvas non disponibile', 'error');
                    return;
                }

                if (!appRoutes.has('BE_USER_FORM')) {
                    showToast('Errore: route non configurata', 'error');
                    return;
                }

                const offcanvas = createDynamicOffcanvas({
                    title: 'Modifica Utente: ' + (userName || 'Sconosciuto'),
                    url: appRoutes.get('BE_USER_FORM') + '?userId=' + encodeURIComponent(userId),
                    entity: 'user',
                    entityId: userId,
                    onContentLoaded: function(offcanvasElement, contentContainer) {
                        try {
                            // Initialize user form components after content is loaded
                            if (typeof UserForm !== 'undefined' && UserForm.init) {
                                UserForm.init();
                            }
                        } catch (initError) {
                            console.error('Error initializing form:', initError);
                            showToast('Errore nell\'inizializzazione del modulo', 'error');
                        }
                    },
                    onClose: function() {
                        // Clean up any global variables if needed
                        if (typeof pond !== 'undefined' && pond && pond.destroy) {
                            try {
                                pond.destroy();
                            } catch (e) {
                                console.warn('Error destroying FilePond:', e);
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Error opening edit form:', error);
                showToast('Errore nell\'apertura del modulo di modifica', 'error');
            }
        });
    }

    // Setup manual checkbox handlers for Preline UI compatibility
    function _setupCheckboxHandlers() {
        // Handle select-all checkbox
        const selectAllCheckbox = document.getElementById('hs-table-search-checkbox-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.removeEventListener('change', _handleSelectAll); // Remove existing listener
            selectAllCheckbox.addEventListener('change', _handleSelectAll);
        }

        // Handle individual row checkboxes
        const individualCheckboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]');
        individualCheckboxes.forEach(checkbox => {
            checkbox.removeEventListener('change', _handleIndividualSelect); // Remove existing listener
            checkbox.addEventListener('change', _handleIndividualSelect);
        });

        // Update bulk action buttons state
        _updateBulkActionButtons();
    }

    function _handleSelectAll(event) {
        const isChecked = event.target.checked;
        const individualCheckboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]');

        individualCheckboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
        });

        _updateBulkActionButtons();
    }

    function _handleIndividualSelect() {
        const selectAllCheckbox = document.getElementById('hs-table-search-checkbox-all');
        const individualCheckboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]');
        const checkedBoxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]:checked');

        if (selectAllCheckbox) {
            if (checkedBoxes.length === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else if (checkedBoxes.length === individualCheckboxes.length) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            }
        }

        _updateBulkActionButtons();
    }

    function _reloadTable(archived) {
        _reloadTableWithFilters(null, null, archived, null);
    }

    function _reloadTableWithFilters(startDate, endDate, archived, userType) {
        var newLink = appRoutes.get("BE_USER_DATA");
        var params = [];

        // check archiviati
        var isArchivedChecked = archived || $("#user_archived:checked").length > 0;
        if (isArchivedChecked) {
            params.push("archived=" + isArchivedChecked);
        }

        // check tipologia utente
        var selectedUserType = userType || $("#user_type_filter").val();
        if (selectedUserType && selectedUserType.trim() !== '') {
            params.push("userType=" + encodeURIComponent(selectedUserType));
        }

        // Add date filtering parameters
        if (startDate && startDate.trim() !== '') {
            params.push("startDate=" + encodeURIComponent(startDate));
        }
        if (endDate && endDate.trim() !== '') {
            params.push("endDate=" + encodeURIComponent(endDate));
        }

        // Build final URL
        if (params.length > 0) {
            newLink += "?" + params.join("&");
        }

        if (window.usersDataTable && window.usersDataTable.dataTable) {
            window.usersDataTable.dataTable.ajax.url(newLink).load();
        }
    }

    // Manteniamo la funzione originale per retrocompatibilità
    function _reloadTableWithDateFilter(startDate, endDate, archived) {
        _reloadTableWithFilters(startDate, endDate, archived, null);
    }

    // Inizializza il filtro tipologia utente
    const _componentUserTypeFilter = function () {
        // Event handler per il cambio di tipologia utente
        $(document).on('change', '#user_type_filter', function() {
            _reloadTableWithFilters(null, null, null, $(this).val());
        });
    }

    function _archiveSelectedRows() {
        // Check permission first
        if (!hasPermission('USER_MANAGEMENT', 'edit')) {
            showToast('Non hai i permessi per eseguire questa operazione.', 'error');
            return;
        }

        const selectedRows = _getSelectedRows();
        if (selectedRows.length === 0) {
            showToast('Seleziona almeno un elemento.', 'warning');
            return;
        }

        const userIds = selectedRows.map(row => row.id).join(',');
        const formData = new FormData();
        formData.append('userIds', userIds);
        formData.append('operation', "archive");
        formData.append('fromArchived', $("#user_archived:checked").length > 0);

        $.ajax({
            url: appRoutes.get("BE_USER_OPERATE"),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                _reloadTable();
                showToast('Dati salvati correttamente.', 'success');
                _clearSelection();
            },
            error: function (error) {
                showToast(error.responseText || 'Errore durante l\'operazione', 'error');
                console.error('Error during user archive', error);
            }
        });
    }

    function _deleteSelectedRows() {
        // Check permission first
        if (!hasPermission('USER_MANAGEMENT', 'delete')) {
            showToast('Non hai i permessi per eseguire questa operazione.', 'error');
            return;
        }

        const selectedRows = _getSelectedRows();
        if (selectedRows.length === 0) {
            showToast('Seleziona almeno un elemento.', 'warning');
            return;
        }

        $.confirm({
            title: 'Conferma eliminazione',
            content: `Sei sicuro di voler eliminare ${selectedRows.length} element${selectedRows.length > 1 ? 'i' : 'o'}? Questa azione non può essere annullata.`,
            theme: 'preline',
            type: 'red',
            typeAnimated: true,
            columnClass: 'col-lg-4 col-lg-offset-4 col-md-6 col-md-offset-3 col-sm-8 col-sm-offset-2',
            buttons: {
                elimina: {
                    text: 'Elimina',
                    btnClass: 'btn-red',
                    action: function () {
                        _performDeleteSelectedRows(selectedRows);
                    }
                },
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-light'
                }
            }
        });
    }

    function _performDeleteSelectedRows(selectedRows) {

        const userIds = selectedRows.map(row => row.id).join(',');
        const formData = new FormData();
        formData.append('userIds', userIds);
        formData.append('operation', "delete");
        formData.append('fromArchived', $("#user_archived:checked").length > 0);

        $.ajax({
            url: appRoutes.get("BE_USER_OPERATE"),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                _reloadTable();
                showToast('Dati eliminati correttamente.', 'success');
                _clearSelection();
            },
            error: function (error) {
                showToast(error.responseText || 'Errore durante l\'eliminazione', 'error');
                console.error('Error during user delete', error);
            }
        });
    }

    function _confirmSelectedRows() {
        // Check permission first
        if (!hasPermission('USER_MANAGEMENT', 'edit')) {
            showToast('Non hai i permessi per eseguire questa operazione.', 'error');
            return;
        }

        const selectedRows = _getSelectedRows();
        if (selectedRows.length === 0) {
            showToast('Seleziona almeno un elemento.', 'warning');
            return;
        }

        const userIds = selectedRows.map(row => row.id).join(',');
        const formData = new FormData();
        formData.append('userIds', userIds);
        formData.append('operation', "confirm");
        formData.append('fromArchived', $("#user_archived:checked").length > 0);

        $.ajax({
            url: appRoutes.get("BE_USER_OPERATE"),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                _reloadTable();
                showToast('Utenti confermati correttamente.', 'success');
                _clearSelection();
            },
            error: function (error) {
                showToast(error.responseText || 'Errore durante la conferma', 'error');
                console.error('Error during user confirm', error);
            }
        });
    }

    // Helper functions for selection management
    function _getSelectedRows() {
        const selectedRows = [];
        const checkboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]:checked');

        checkboxes.forEach(checkbox => {
            const row = checkbox.closest('tr');
            if (row && window.usersDataTable && window.usersDataTable.dataTable) {
                const rowData = window.usersDataTable.dataTable.row(row).data();
                if (rowData && rowData.length > 1) {
                    // Extract user ID from the link in the second column (index 1)
                    const linkHtml = rowData[1];
                    const userIdMatch = linkHtml.match(/userId='([^']+)'/);
                    if (userIdMatch && userIdMatch[1]) {
                        selectedRows.push({ id: userIdMatch[1], data: rowData });
                    }
                }
            }
        });

        return selectedRows;
    }

    function _clearSelection() {
        const checkboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual], #hs-table-search-checkbox-all');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
            checkbox.indeterminate = false;
        });
        _updateBulkActionButtons();
    }

    function _updateBulkActionButtons() {
        const selectedCount = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]:checked').length;
        const bulkActionContainer = document.getElementById('bulk-actions-container');
        const selectedCountElement = document.getElementById('selected-count');

        if (bulkActionContainer && selectedCountElement) {
            if (selectedCount > 0) {
                // Show the enhanced bulk actions container
                bulkActionContainer.classList.remove('hidden');
                selectedCountElement.textContent = selectedCount;
            } else {
                // Hide the enhanced bulk actions container
                bulkActionContainer.classList.add('hidden');
            }
        }

        // Legacy support for old bulk action buttons (if any still exist)
        const bulkActionButtons = document.querySelectorAll('.bulk-action-btn');
        bulkActionButtons.forEach(button => {
            // Update button text with count
            const buttonTextSpan = button.querySelector('.bulk-action-text');
            if (buttonTextSpan) {
                if (selectedCount > 0) {
                    buttonTextSpan.textContent = `Azioni (${selectedCount})`;
                } else {
                    buttonTextSpan.textContent = 'Azioni';
                }
            }

            if (selectedCount > 0) {
                button.disabled = false;
                button.classList.remove('opacity-50', 'cursor-not-allowed');
            } else {
                button.disabled = true;
                button.classList.add('opacity-50', 'cursor-not-allowed');
            }
        });
    }

    // Individual row action functions
    function _confirmSingleRow(userId) {
        // Check permission first
        if (!hasPermission('USER_MANAGEMENT', 'edit')) {
            showToast('Non hai i permessi per eseguire questa operazione.', 'error');
            return;
        }
        _performSingleRowAction(userId, 'confirm');
    }

    function _archiveSingleRow(userId) {
        // Check permission first
        if (!hasPermission('USER_MANAGEMENT', 'edit')) {
            showToast('Non hai i permessi per eseguire questa operazione.', 'error');
            return;
        }
        _performSingleRowAction(userId, 'archive');
    }

    function _deleteSingleRow(userId) {
        // Check permission first
        if (!hasPermission('USER_MANAGEMENT', 'delete')) {
            showToast('Non hai i permessi per eseguire questa operazione.', 'error');
            return;
        }

        $.confirm({
            title: 'Conferma eliminazione',
            content: 'Sei sicuro di voler eliminare questo utente? Questa azione non può essere annullata.',
            theme: 'preline',
            type: 'red',
            typeAnimated: true,
            columnClass: 'col-lg-4 col-lg-offset-4 col-md-6 col-md-offset-3 col-sm-8 col-sm-offset-2',
            buttons: {
                elimina: {
                    text: 'Elimina',
                    btnClass: 'btn-red',
                    action: function () {
                        _performSingleRowAction(userId, 'delete');
                    }
                },
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-light'
                }
            }
        });
    }

    function _performSingleRowAction(userId, operation) {
        const formData = new FormData();
        formData.append('userIds', userId);
        formData.append('operation', operation);
        formData.append('fromArchived', $("#user_archived:checked").length > 0);

        $.ajax({
            url: appRoutes.get("BE_USER_OPERATE"),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                _reloadTable();
                showToast('Operazione completata correttamente.', 'success');
            },
            error: function (error) {
                showToast(error.responseText || 'Errore durante l\'operazione', 'error');
                console.error('Error during user operation', error);
            }
        });
    }

    // Make individual action functions globally accessible
    window._confirmSingleRow = _confirmSingleRow;
    window._archiveSingleRow = _archiveSingleRow;
    window._deleteSingleRow = _deleteSingleRow;

    //
    // Return objects assigned to module
    //

    return {
        init: function () {
            _componentDatatable();
            _componentDatepicker();
            _componentAddUser();
            _componentEditUser();
            _componentUserTypeFilter();
        },
        reloadTable: _reloadTable,
        confirmSelectedRows: _confirmSelectedRows,
        archiveSelectedRows: _archiveSelectedRows,
        deleteSelectedRows: _deleteSelectedRows,
        getSelectedRows: _getSelectedRows,
        clearSelection: _clearSelection
    };
}();

// Initialize module
// ------------------------------

document.addEventListener('DOMContentLoaded', function () {
    UserCollection.init();
});
