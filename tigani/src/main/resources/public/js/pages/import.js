$(function () {

    // Initialize form validation
    const _componentValidation = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Initialize form validation
        $('.form-validate-jquery').validate({
            ignore: 'input[type=hidden], .select2-search__field',
            errorClass: 'validation-invalid-label',
            successClass: 'validation-valid-label',
            validClass: 'validation-valid-label',
            highlight: function(element, errorClass) {
                $(element).removeClass('is-valid').addClass('is-invalid');
            },
            unhighlight: function(element, errorClass) {
                $(element).removeClass('is-invalid').addClass('is-valid');
            },
            errorPlacement: function(error, element) {
                if (element.hasClass('select2-hidden-accessible')) {
                    error.appendTo(element.parent());
                } else if (element.parents().hasClass('form-control-feedback') || element.parents().hasClass('form-check') || element.parents().hasClass('input-group')) {
                    error.appendTo(element.parent().parent());
                } else {
                    error.insertAfter(element);
                }
            }
        });
    };

    // Initialize file uploader
    const _componentFileUpload = function () {
        // File upload configuration
        $('input[type="file"]').each(function() {
            const $input = $(this);
            const maxSize = $input.data('maxfilessize') || 10485760; // 10MB default

            $input.on('change', function() {
                const file = this.files[0];
                if (file) {
                    // Validate file size
                    if (file.size > maxSize) {
                        showToast('Il file è troppo grande. Dimensione massima: ' + (maxSize / 1024 / 1024) + 'MB', 'error');
                        $(this).val('');
                        $('#file-info').addClass('hidden');
                        return;
                    }

                    // Validate file type
                    const allowedTypes = [
                        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                        'application/vnd.ms-excel'
                    ];

                    if (!allowedTypes.includes(file.type) && !file.name.toLowerCase().endsWith('.xlsx') && !file.name.toLowerCase().endsWith('.xls')) {
                        showToast('Formato file non valido. Sono accettati solo file Excel (.xlsx, .xls)', 'error');
                        $(this).val('');
                        $('#file-info').addClass('hidden');
                        return;
                    }

                    // Show file info
                    $('#file-name').text(file.name);
                    $('#file-size').text(formatFileSize(file.size));
                    $('#file-info').removeClass('hidden');
                } else {
                    $('#file-info').addClass('hidden');
                }
            });
        });
    };

    // Helper function to format file size
    const formatFileSize = function(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    // Clear file function
    window.clearFile = function() {
        $('#excelFile').val('');
        $('#file-info').addClass('hidden');
    };

    // Toast notification helper
    const showToast = function(message, type) {
        if (typeof Noty !== 'undefined') {
            new Noty({
                text: message,
                type: type
            }).show();
        } else {
            // Fallback to alert if Noty is not available
            alert(message);
        }
    };

    // Handle import type selection
    const _handleImportTypeChange = function () {
        $('select[name="importType"]').on('change', function() {
            const selectedType = $(this).val();
            const $fileInput = $('input[name="excelFile"]');
            const $fileContainer = $fileInput.closest('.grid');

            // Hide all info sections
            $('#comuni-info, #stati-info, #province-info, #marche-info, #modelli-info, #allestimenti-info').hide();

            // Show relevant info section and handle file requirement
            if (selectedType === 'comuni') {
                $('#comuni-info').show();
                $fileContainer.show();
                $fileInput.prop('required', true);
            } else if (selectedType === 'stati') {
                $('#stati-info').show();
                $fileContainer.show();
                $fileInput.prop('required', true);
            } else if (selectedType === 'province') {
                $('#province-info').show();
                $fileContainer.show();
                $fileInput.prop('required', true);
            } else if (selectedType === 'marche') {
                $('#marche-info').show();
                $fileContainer.hide();
                $fileInput.prop('required', false);
            } else if (selectedType === 'modelli') {
                $('#modelli-info').show();
                $fileContainer.hide();
                $fileInput.prop('required', false);
            } else if (selectedType === 'allestimenti') {
                $('#allestimenti-info').show();
                $fileContainer.hide();
                $fileInput.prop('required', false);
            } else {
                // Default case - show file input
                $fileContainer.show();
                $fileInput.prop('required', true);
            }
        });
    };

    // Handle form submission
    const _handleFormSubmission = function () {
        $('#import-form').on('submit', function(e) {
            e.preventDefault();

            if (!$(this).valid()) {
                return;
            }

            const formData = new FormData(this);
            const $submitBtn = $('#import-btn');
            const $resetBtn = $('#reset-btn');
            const $progressContainer = $('#import-progress');
            const $progressBar = $progressContainer.find('[role="progressbar"]');
            const $progressText = $('#progress-text');
            const $progressPercent = $progressContainer.find('.text-gray-500');
            const $resultsContainer = $('#import-results');
            const $resultsContent = $('#results-content');

            // Disable form and show progress
            $submitBtn.prop('disabled', true).html('<svg class="shrink-0 size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/><path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16"/></svg>Importazione in corso...');
            $progressContainer.show();
            $resultsContainer.hide();
            $progressBar.css('width', '10%');
            $progressPercent.text('10%');

            // Simulate progress updates
            let progress = 10;
            const progressInterval = setInterval(function() {
                if (progress < 90) {
                    progress += Math.random() * 20;
                    if (progress > 90) progress = 90;
                    $progressBar.css('width', progress + '%');
                    $progressPercent.text(Math.round(progress) + '%');
                }
            }, 500);
            
            // Submit form
            $.ajax({
                url: appRoutes.get('BE_IMPORT_PROCESS'),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                timeout: 600000, // 10 minutes timeout
                success: function(response) {
                    clearInterval(progressInterval);
                    $progressBar.css('width', '100%');
                    $progressPercent.text('100%');
                    $progressText.text('Importazione completata!');

                    setTimeout(function() {
                        $progressContainer.hide();

                        let importedCount = response.importedCount || response.imported || 0;

                        $resultsContent.html(
                            '<div class="bg-green-50 border border-green-200 rounded-lg p-4 dark:bg-green-800/10 dark:border-green-900">' +
                            '<div class="flex">' +
                            '<div class="shrink-0">' +
                            '<svg class="shrink-0 size-4 text-green-600 mt-0.5 dark:text-green-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">' +
                            '<path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"/>' +
                            '<path d="m9 12 2 2 4-4"/>' +
                            '</svg>' +
                            '</div>' +
                            '<div class="ms-3">' +
                            '<h3 class="text-sm font-medium text-green-800 dark:text-green-200">Successo!</h3>' +
                            '<p class="text-sm text-green-700 dark:text-green-300 mt-2">Importazione completata con successo. Sono stati importati <strong>' + importedCount + '</strong> record.</p>' +
                            '</div>' +
                            '</div>' +
                            '</div>'
                        );

                        $resultsContainer.show();
                        $resetBtn.show();

                        showToast('Importazione completata con successo! Importati ' + importedCount + ' record.', 'success');

                    }, 1000);
                },
                error: function(xhr) {
                    clearInterval(progressInterval);
                    $progressContainer.hide();

                    let errorMessage = 'Errore durante l\'importazione';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {
                        // Use default error message
                    }

                    $resultsContent.html(
                        '<div class="bg-red-50 border border-red-200 rounded-lg p-4 dark:bg-red-800/10 dark:border-red-900">' +
                        '<div class="flex">' +
                        '<div class="shrink-0">' +
                        '<svg class="shrink-0 size-4 text-red-600 mt-0.5 dark:text-red-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">' +
                        '<path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"/>' +
                        '<path d="m15 9-6 6"/>' +
                        '<path d="m9 9 6 6"/>' +
                        '</svg>' +
                        '</div>' +
                        '<div class="ms-3">' +
                        '<h3 class="text-sm font-medium text-red-800 dark:text-red-200">Errore!</h3>' +
                        '<p class="text-sm text-red-700 dark:text-red-300 mt-2">' + errorMessage + '</p>' +
                        '</div>' +
                        '</div>' +
                        '</div>'
                    );

                    $resultsContainer.show();
                    $resetBtn.show();

                    showToast(errorMessage, 'error');
                },
                complete: function() {
                    $submitBtn.prop('disabled', false).html('<svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="7 10 12 15 17 10"/><line x1="12" x2="12" y1="15" y2="3"/></svg>Avvia Importazione');
                }
            });
        });
    };

    // Handle reset button
    const _handleReset = function () {
        $('#reset-btn').on('click', function() {
            // Reset form
            $('#import-form')[0].reset();

            // Hide all dynamic sections
            $('#comuni-info, #stati-info, #province-info, #marche-info, #modelli-info, #allestimenti-info, #import-progress, #import-results').hide();

            // Clear file info
            $('#file-info').addClass('hidden');

            // Show file input container and make it required by default
            $('input[name="excelFile"]').closest('.grid').show();
            $('input[name="excelFile"]').prop('required', true);

            // Reset buttons
            $('#import-btn').show();
            $(this).hide();

            // Clear validation
            $('#import-form').validate().resetForm();
            $('#import-form .is-valid, #import-form .is-invalid').removeClass('is-valid is-invalid');
        });
    };

    // Initialize all components
    _componentValidation();
    _componentFileUpload();
    _handleImportTypeChange();
    _handleFormSubmission();
    _handleReset();

});
