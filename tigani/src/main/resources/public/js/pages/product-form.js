var oldImageBase64;

const ProductForm = function () {
    // Initialization of components
    const init = function () {
        _componentFilePond();
        _componentValidate();        
    };

    // FilePond using centralized factory
    const _componentFilePond = function () {
        TiganiLibs.UIComponentFactory.initFilePond('input[type="file"]', {
            onaddfile: function(error, file) {
                if (!error) {
                    oldImageBase64 = file.getFileEncodeBase64String();
                }
            }
        });
    };

    // Validation using centralized factory
    const _componentValidate = function () {
        // Custom validation options for dealer form
        const customOptions = {};

        // Use centralized form validation factory
        const validator = TiganiLibs.FormValidationFactory.create('.form-validate-jquery', null, customOptions);

        // Custom validation methods for dealer form
        $.validator.addMethod('vatNumber', function (value, element) {
            return this.optional(element) || /^[0-9]{11}$/.test(value);
        }, 'Partita IVA deve contenere esattamente 11 cifre.');

        $.validator.addMethod('fiscalCode', function (value, element) {
            return this.optional(element) || /^[A-Z0-9]{16}$/.test(value.toUpperCase());
        }, 'Codice fiscale deve contenere esattamente 16 caratteri alfanumerici.');

    };           

    // Return objects assigned to module
    return {
        init: init
    };
}();