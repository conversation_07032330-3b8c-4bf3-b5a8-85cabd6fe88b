<!doctype html>
<html lang="it">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>Preline UI + DataTable (zero-build)</title>

    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Preline CSS (stili componenti) -->
    <link rel="stylesheet" href="https://unpkg.com/preline/dist/preline.css">

    <!-- DataTables CSS (tema base; Preline "skinna" la UI esterna) -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.8/css/jquery.dataTables.min.css">

    <!-- Nasconde i controlli nativi DataTables (usiamo quelli "Preline style") -->
    <style>
        .dt-layout-row:has(.dt-search),
        .dt-layout-row:has(.dt-length),
        .dt-layout-row:has(.dt-paging) {
            display: none !important;
        }

        /* Additional DataTables styling fixes */
        #usersTable_wrapper {
            width: 100%;
        }

        table.dataTable {
            width: 100% !important;
        }

        /* Apply Preline styling to DataTables */
        table.dataTable thead th {
            background-color: #f9fafb;
            border: none;
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.75rem;
            color: #6b7280;
            padding: 0.75rem 1rem;
        }

        table.dataTable tbody td {
            border: none;
            border-bottom: 1px solid #e5e7eb;
            padding: 0.75rem 1rem;
        }

        table.dataTable tbody tr:hover {
            background-color: #f9fafb;
        }

        /* Remove DataTables default sorting icons and add custom ones */
        table.dataTable thead .sorting:before,
        table.dataTable thead .sorting_asc:before,
        table.dataTable thead .sorting_desc:before,
        table.dataTable thead .sorting:after,
        table.dataTable thead .sorting_asc:after,
        table.dataTable thead .sorting_desc:after {
            display: none;
        }

        table.dataTable thead .sorting,
        table.dataTable thead .sorting_asc,
        table.dataTable thead .sorting_desc {
            position: relative;
            cursor: pointer;
        }

        table.dataTable thead .sorting:after {
            content: "↕";
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            opacity: 0.5;
            display: inline-block;
        }

        table.dataTable thead .sorting_asc:after {
            content: "↑";
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            opacity: 0.8;
            display: inline-block;
        }

        table.dataTable thead .sorting_desc:after {
            content: "↓";
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            opacity: 0.8;
            display: inline-block;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen p-6">
<div class="max-w-5xl mx-auto">
    <h1 class="text-2xl font-semibold text-gray-800 mb-6">Utenti</h1>

    <!-- Barra controlli in stile Preline -->
    <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between mb-4">
        <div class="w-full sm:w-80">
            <label for="tableSearch" class="sr-only">Cerca</label>
            <div class="relative">
                <input id="tableSearch" type="text" placeholder="Cerca..."
                       class="py-2 px-3 ps-9 block w-full border border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500" />
                <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none ps-3">
                    <svg class="h-4 w-4 text-gray-400" viewBox="0 0 24 24" fill="none">
                        <path d="M21 21l-4.3-4.3M10.5 18a7.5 7.5 0 1 1 0-15 7.5 7.5 0 0 1 0 15Z"
                              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </div>
        </div>

        <div class="flex items-center gap-3">
            <!--<label for="rowsPerPage" class="text-sm text-gray-600">Righe</label>-->
            <select class="hidden" data-hs-select='{
        "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span data-title></span></button>",
        "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 before:absolute before:inset-0 before:z-1 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-200 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800",
        "dropdownClasses": "mt-2 z-50 w-20 max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-md overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
        "optionClasses": "py-2 px-3 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-md focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
        "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-blue-600 dark:text-blue-500\" xmlns=\"http:.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>",
        "extraMarkup": "<div class=\"absolute top-1/2 end-3 -translate-y-1/2\"><svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m7 15 5 5 5-5\"/><path d=\"m7 9 5-5 5 5\"/></svg></div>"
      }' data-hs-datatable-page-entities="">
                <option value="10">10</option>
                <option value="15">15</option>
                <option value="20">20</option>
                <option value="25">25</option>
                <option value="50">50</option>
            </select>
        </div>
    </div>

    <!-- Tabella -->
    <div class="overflow-x-auto bg-white border border-gray-200 rounded-xl shadow-sm">
        <table id="usersTable" class="min-w-full" data-hs-datatable='{
            "paging": true,
            "pageLength": 10,
            "searching": true,
            "ordering": true,
            "info": false,
            "lengthChange": false,
            "dom": "rt",
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.13.8/i18n/it-IT.json"
            }
        }'>
            <thead class="bg-gray-50">
            <tr>
                <th class="px-4 py-3 text-start text-xs font-medium text-gray-500 uppercase">Nome</th>
                <th class="px-4 py-3 text-start text-xs font-medium text-gray-500 uppercase">Età</th>
                <th class="px-4 py-3 text-start text-xs font-medium text-gray-500 uppercase">Indirizzo</th>
                <th class="px-4 py-3 text-end  text-xs font-medium text-gray-500 uppercase">Azione</th>
            </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
            <tr>
                <td class="px-4 py-3 text-sm text-gray-800">Christina Bersh</td>
                <td class="px-4 py-3 text-sm text-gray-800">45</td>
                <td class="px-4 py-3 text-sm text-gray-600">4222 River Rd, Columbus</td>
                <td class="px-4 py-3 text-sm text-end">
                    <button class="inline-flex items-center gap-x-1.5 rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50" onclick="deleteRow(this)">
                        Elimina
                    </button>
                </td>
            </tr>
            <tr>
                <td class="px-4 py-3 text-sm text-gray-800">David Harrison</td>
                <td class="px-4 py-3 text-sm text-gray-800">27</td>
                <td class="px-4 py-3 text-sm text-gray-600">2952 S Peoria Ave, Tulsa</td>
                <td class="px-4 py-3 text-sm text-end">
                    <button class="inline-flex items-center gap-x-1.5 rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50" onclick="deleteRow(this)">
                        Elimina
                    </button>
                </td>
            </tr>
            <tr>
                <td class="px-4 py-3 text-sm text-gray-800">Maria Rossi</td>
                <td class="px-4 py-3 text-sm text-gray-800">32</td>
                <td class="px-4 py-3 text-sm text-gray-600">Via Roma 123, Milano</td>
                <td class="px-4 py-3 text-sm text-end">
                    <button class="inline-flex items-center gap-x-1.5 rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50" onclick="deleteRow(this)">
                        Elimina
                    </button>
                </td>
            </tr>
            <tr>
                <td class="px-4 py-3 text-sm text-gray-800">Giuseppe Verdi</td>
                <td class="px-4 py-3 text-sm text-gray-800">55</td>
                <td class="px-4 py-3 text-sm text-gray-600">Corso Venezia 45, Milano</td>
                <td class="px-4 py-3 text-sm text-end">
                    <button class="inline-flex items-center gap-x-1.5 rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50" onclick="deleteRow(this)">
                        Elimina
                    </button>
                </td>
            </tr>
            <tr>
                <td class="px-4 py-3 text-sm text-gray-800">Anna Bianchi</td>
                <td class="px-4 py-3 text-sm text-gray-800">28</td>
                <td class="px-4 py-3 text-sm text-gray-600">Piazza Duomo 10, Milano</td>
                <td class="px-4 py-3 text-sm text-end">
                    <button class="inline-flex items-center gap-x-1.5 rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50" onclick="deleteRow(this)">
                        Elimina
                    </button>
                </td>
            </tr>
            <tr>
                <td class="px-4 py-3 text-sm text-gray-800">Luca Ferrari</td>
                <td class="px-4 py-3 text-sm text-gray-800">41</td>
                <td class="px-4 py-3 text-sm text-gray-600">Via Garibaldi 67, Torino</td>
                <td class="px-4 py-3 text-sm text-end">
                    <button class="inline-flex items-center gap-x-1.5 rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50" onclick="deleteRow(this)">
                        Elimina
                    </button>
                </td>
            </tr>
            <tr>
                <td class="px-4 py-3 text-sm text-gray-800">Sofia Greco</td>
                <td class="px-4 py-3 text-sm text-gray-800">35</td>
                <td class="px-4 py-3 text-sm text-gray-600">Via Dante 89, Roma</td>
                <td class="px-4 py-3 text-sm text-end">
                    <button class="inline-flex items-center gap-x-1.5 rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50" onclick="deleteRow(this)">
                        Elimina
                    </button>
                </td>
            </tr>
            <tr>
                <td class="px-4 py-3 text-sm text-gray-800">Marco Conte</td>
                <td class="px-4 py-3 text-sm text-gray-800">39</td>
                <td class="px-4 py-3 text-sm text-gray-600">Via Mazzini 23, Firenze</td>
                <td class="px-4 py-3 text-sm text-end">
                    <button class="inline-flex items-center gap-x-1.5 rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50" onclick="deleteRow(this)">
                        Elimina
                    </button>
                </td>
            </tr>
            <tr>
                <td class="px-4 py-3 text-sm text-gray-800">Francesca Ricci</td>
                <td class="px-4 py-3 text-sm text-gray-800">24</td>
                <td class="px-4 py-3 text-sm text-gray-600">Via Nazionale 156, Napoli</td>
                <td class="px-4 py-3 text-sm text-end">
                    <button class="inline-flex items-center gap-x-1.5 rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50" onclick="deleteRow(this)">
                        Elimina
                    </button>
                </td>
            </tr>
            <tr>
                <td class="px-4 py-3 text-sm text-gray-800">Roberto Esposito</td>
                <td class="px-4 py-3 text-sm text-gray-800">47</td>
                <td class="px-4 py-3 text-sm text-gray-600">Corso Italia 78, Palermo</td>
                <td class="px-4 py-3 text-sm text-end">
                    <button class="inline-flex items-center gap-x-1.5 rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50" onclick="deleteRow(this)">
                        Elimina
                    </button>
                </td>
            </tr>
            <tr>
                <td class="px-4 py-3 text-sm text-gray-800">Elena Romano</td>
                <td class="px-4 py-3 text-sm text-gray-800">31</td>
                <td class="px-4 py-3 text-sm text-gray-600">Via Colombo 234, Genova</td>
                <td class="px-4 py-3 text-sm text-end">
                    <button class="inline-flex items-center gap-x-1.5 rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50" onclick="deleteRow(this)">
                        Elimina
                    </button>
                </td>
            </tr>
            <tr>
                <td class="px-4 py-3 text-sm text-gray-800">Alessandro Martini</td>
                <td class="px-4 py-3 text-sm text-gray-800">52</td>
                <td class="px-4 py-3 text-sm text-gray-600">Viale Europa 45, Bologna</td>
                <td class="px-4 py-3 text-sm text-end">
                    <button class="inline-flex items-center gap-x-1.5 rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50" onclick="deleteRow(this)">
                        Elimina
                    </button>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <!-- Paginazione "Preline style" -->
    <div class="flex items-center justify-between mt-4">
        <p id="tableInfo" class="text-sm text-gray-600"></p>
        <nav class="flex items-center gap-2" aria-label="Pagination">
            <button id="prevPage"
                    class="px-3 py-2 text-sm rounded-lg border border-gray-200 hover:bg-gray-50 disabled:opacity-50">« Precedente</button>
            <button id="nextPage"
                    class="px-3 py-2 text-sm rounded-lg border border-gray-200 hover:bg-gray-50 disabled:opacity-50">Successiva »</button>
        </nav>
    </div>
</div>

<!-- jQuery + DataTables -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/1.13.8/js/jquery.dataTables.min.js"></script>

<!-- Preline JS -->
<script src="https://unpkg.com/preline/dist/preline.js"></script>

<script>
    let dataTable;

    // Function to delete a row
    function deleteRow(button) {
        if (confirm('Sei sicuro di voler eliminare questo utente?')) {
            const row = button.closest('tr');
            dataTable.row(row).remove().draw();
            syncPagingUI();
        }
    }

    // Function to sync pagination UI
    function syncPagingUI() {
        const info = dataTable.page.info();
        const infoEl = document.getElementById('tableInfo');
        const prevBtn = document.getElementById('prevPage');
        const nextBtn = document.getElementById('nextPage');

        infoEl.textContent = `Mostro ${info.start + 1}-${info.end} di ${info.recordsDisplay}`;
        prevBtn.disabled = info.page <= 0;
        nextBtn.disabled = info.page >= info.pages - 1;
    }

    // Initialize DataTable when page loads
    window.addEventListener('DOMContentLoaded', function() {
        // Initialize Preline components first
        if (window.HSStaticMethods) {
            window.HSStaticMethods.autoInit();
        }

        // Wait a bit for Preline to initialize, then get the DataTable instance
        setTimeout(() => {
            // Try to get HSDataTable instance
            const tableElement = document.getElementById('usersTable');
            let hsDataTableInstance = null;

            // Check if HSDataTable was created
            if (window.HSDataTable && window.HSDataTable.getInstance) {
                hsDataTableInstance = window.HSDataTable.getInstance(tableElement);
            }

            // If HSDataTable exists, use it; otherwise fallback to jQuery DataTables
            if (hsDataTableInstance && hsDataTableInstance.dataTable) {
                dataTable = hsDataTableInstance.dataTable;
            } else {
                // Fallback to jQuery DataTables initialization
                dataTable = $('#usersTable').DataTable({
                    paging: true,
                    pageLength: 10,
                    searching: true,
                    ordering: true,
                    info: false,
                    lengthChange: false,
                    pagingType: 'simple',
                    dom: 'rt',
                    language: {
                        "sEmptyTable": "Nessun dato presente nella tabella",
                        "sInfo": "Vista da _START_ a _END_ di _TOTAL_ elementi",
                        "sInfoEmpty": "Vista da 0 a 0 di 0 elementi",
                        "sInfoFiltered": "(filtrati da _MAX_ elementi totali)",
                        "sInfoPostFix": "",
                        "sInfoThousands": ".",
                        "sLengthMenu": "Visualizza _MENU_ elementi",
                        "sLoadingRecords": "Caricamento...",
                        "sProcessing": "Elaborazione...",
                        "sSearch": "Cerca:",
                        "sZeroRecords": "La ricerca non ha portato alcun risultato.",
                        "oPaginate": {
                            "sFirst": "Inizio",
                            "sPrevious": "Precedente",
                            "sNext": "Successivo",
                            "sLast": "Fine"
                        },
                        "oAria": {
                            "sSortAscending": ": attiva per ordinare la colonna in ordine crescente",
                            "sSortDescending": ": attiva per ordinare la colonna in ordine decrescente"
                        }
                    }
                });
            }

            // Custom search functionality
            const searchEl = document.getElementById('tableSearch');
            searchEl.addEventListener('input', function() {
                dataTable.search(this.value).draw();
                syncPagingUI();
            });

            // Custom page length change
            const lenEl = document.getElementById('rowsPerPage');
            lenEl.addEventListener('change', function() {
                dataTable.page.len(parseInt(this.value, 10)).draw();
                syncPagingUI();
            });

            // Custom pagination buttons
            const prevBtn = document.getElementById('prevPage');
            const nextBtn = document.getElementById('nextPage');

            prevBtn.addEventListener('click', function() {
                dataTable.page('previous').draw('page');
                syncPagingUI();
            });

            nextBtn.addEventListener('click', function() {
                dataTable.page('next').draw('page');
                syncPagingUI();
            });

            // Listen for DataTable draw events
            $(dataTable.table().node()).on('draw.dt', function() {
                syncPagingUI();
            });

            // Initial sync
            syncPagingUI();

        }, 100); // Small delay to ensure Preline is initialized
    });
</script>
</body>
</html>