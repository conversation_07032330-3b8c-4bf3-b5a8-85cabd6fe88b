{% extends "be/include/preline-base.html" %}

{% set page = 'IMPORT' %}
{% set title = 'Importazione Dati' %}

{% block extrahead %}

<title>{{ title }}</title>

<!-- Specific script -->
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}
<!-- specific script-->

<link href="https://anubi.us/static/lib/dropuploader/1.8.1/css/drop_uploader.min.css" rel="stylesheet">

<!-- Page script -->
<script src="https://anubi.us/static/lib/dropuploader/1.8.1/js/drop_uploader.min.js"></script>
<script src="{{ contextPath }}/js/pages/import.js?{{ buildNumber }}"></script>
<!-- /page script -->

{% endblock %}

{% block content %}
<script class="reload-script-on-load">
    addRoute('BE_IMPORT', '{{ routes("BE_IMPORT") }}');
    addRoute('BE_IMPORT_PROCESS', '{{ routes("BE_IMPORT_PROCESS") }}');
</script>

<!-- Content area -->
<div class="w-full lg:ps-64">
    <div class="p-4 sm:p-6 space-y-4 sm:space-y-6">

        <!-- Page header -->
        <div class="mb-5">
            <h1 class="text-2xl font-semibold text-gray-800 dark:text-neutral-200">Importazione Dati</h1>
            <p class="text-sm text-gray-600 dark:text-neutral-400">Gestione importazione dati da file Excel</p>
        </div>
        <!-- /page header -->

        <!-- Form -->
        <div class="bg-white border border-gray-200 rounded-xl shadow-sm dark:bg-neutral-800 dark:border-neutral-700">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-neutral-700">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-neutral-200">
                    Importazione Dati
                </h2>
            </div>
            <div class="p-6">
                <form id="import-form" class="form-validate-jquery" method="POST" action="{{ routes('BE_IMPORT_PROCESS') }}" enctype="multipart/form-data">

                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                    <div class="sm:col-span-3">
                        <label for="importType" class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            Tipo di Importazione <span class="text-red-500">*</span>
                        </label>
                    </div>
                    <!-- End Col -->

                    <div class="sm:col-span-9">
                        <div class="relative">
                            <select id="importType" name="importType" data-hs-select='{
                                    "placeholder": "Seleziona il tipo di dati da importare",
                                    "toggleTag": "<button type=\"button\" aria-expanded=\"false\"></button>",
                                    "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:border-blue-500 focus:ring-blue-500 before:absolute before:inset-0 before:z-1 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600",
                                    "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                                    "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                                    "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-blue-600 dark:text-blue-500 \" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>"
                                    }' required>
                                <option value="">Seleziona il tipo di dati da importare</option>
                                <option value="comuni">Comuni (Cities)</option>
                                <option value="stati">Stati (Countries)</option>
                                <option value="province">Province (Provinces)</option>
                                <option value="marche">Marche Veicoli (Vehicle Brands)</option>
                                <option value="modelli">Modelli Veicoli (Vehicle Models)</option>
                                <option value="allestimenti">Allestimenti Veicoli (Vehicle Model Setups)</option>
                            </select>
                            <div class="absolute top-1/2 end-2.5 -translate-y-1/2">
                                <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m7 15 5 5 5-5" />
                                    <path d="m7 9 5-5 5 5" />
                                </svg>
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 dark:text-neutral-400 mt-2">Seleziona il tipo di dati che vuoi importare dal file Excel.</p>
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->


                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5 mt-6">
                    <div class="sm:col-span-3">
                        <label for="excelFile" class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            File Excel <span class="text-red-500">*</span>
                        </label>
                    </div>
                    <!-- End Col -->

                    <div class="sm:col-span-9">
                        <!-- File Upload -->
                        <div class="flex items-center justify-center w-full">
                            <label for="excelFile" class="flex flex-col items-center justify-center w-full h-64 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-neutral-800 dark:bg-neutral-900 hover:bg-gray-100 dark:border-neutral-600 dark:hover:border-neutral-500">
                                <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                    <svg class="w-8 h-8 mb-4 text-gray-500 dark:text-neutral-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                                    </svg>
                                    <p class="mb-2 text-sm text-gray-500 dark:text-neutral-400"><span class="font-semibold">Clicca per caricare</span> o trascina qui</p>
                                    <p class="text-xs text-gray-500 dark:text-neutral-400">Excel (.xlsx, .xls) - Max 10MB</p>
                                </div>
                                <input id="excelFile" name="excelFile" type="file" accept=".xlsx,.xls" required data-maxfilessize="10485760" class="hidden" />
                            </label>
                        </div>

                        <!-- File Info Display -->
                        <div id="file-info" class="mt-3 hidden">
                            <div class="flex items-center p-3 bg-blue-50 border border-blue-200 rounded-lg dark:bg-blue-800/10 dark:border-blue-900">
                                <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
                                    <polyline points="14,2 14,8 20,8"/>
                                </svg>
                                <div class="ms-3 flex-1">
                                    <p class="text-sm font-medium text-blue-800 dark:text-blue-200" id="file-name"></p>
                                    <p class="text-xs text-blue-700 dark:text-blue-300" id="file-size"></p>
                                </div>
                                <button type="button" class="ms-3 text-blue-600 hover:text-blue-800 dark:text-blue-500 dark:hover:text-blue-300" onclick="clearFile()">
                                    <svg class="size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M18 6 6 18"/>
                                        <path d="m6 6 12 12"/>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <p class="text-xs text-gray-500 dark:text-neutral-400 mt-2">
                            Carica il file Excel (.xlsx) contenente i dati da importare. Dimensione massima: 10MB.
                        </p>
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->

                <!-- Import type specific information -->
                <div id="comuni-info" class="mb-6" style="display: none;">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 dark:bg-blue-800/10 dark:border-blue-900">
                        <div class="flex">
                            <div class="shrink-0">
                                <svg class="shrink-0 size-4 text-blue-600 mt-0.5 dark:text-blue-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="10"/>
                                    <path d="m9 12 2 2 4-4"/>
                                </svg>
                            </div>
                            <div class="ms-3">
                                <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                                    Formato per Comuni:
                                </h3>
                                <p class="text-sm text-blue-700 dark:text-blue-300 mt-2">Il file Excel deve contenere le seguenti colonne nell'ordine specificato:</p>
                                <ol class="text-sm text-blue-700 dark:text-blue-300 mt-2 list-decimal list-inside space-y-1">
                                    <li><strong>codice_istat</strong> - Codice ISTAT</li>
                                    <li><strong>denominazione_ita</strong> - Nome del comune</li>
                                    <li><strong>cap</strong> - Codice Avviamento Postale</li>
                                    <li><strong>sigla_provincia</strong> - Sigla della provincia</li>
                                    <li><strong>denominazione_provincia</strong> - Nome della provincia</li>
                                    <li><strong>denominazione_regione</strong> - Nome della regione</li>
                                    <li><strong>codice_belfiore</strong> - Codice Belfiore</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="stati-info" class="mb-6" style="display: none;">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 dark:bg-blue-800/10 dark:border-blue-900">
                        <div class="flex">
                            <div class="shrink-0">
                                <svg class="shrink-0 size-4 text-blue-600 mt-0.5 dark:text-blue-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="10"/>
                                    <path d="m9 12 2 2 4-4"/>
                                </svg>
                            </div>
                            <div class="ms-3">
                                <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                                    Formato per Stati:
                                </h3>
                                <p class="text-sm text-blue-700 dark:text-blue-300 mt-2">Il file Excel deve contenere le seguenti colonne nell'ordine specificato:</p>
                                <ol class="text-sm text-blue-700 dark:text-blue-300 mt-2 list-decimal list-inside space-y-1">
                                    <li><strong>sigla_nazione</strong> - Codice del paese</li>
                                    <li><strong>codice_belfiore</strong> - Codice Belfiore</li>
                                    <li><strong>denominazione_nazione</strong> - Nome del paese</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="province-info" class="mb-6" style="display: none;">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 dark:bg-blue-800/10 dark:border-blue-900">
                        <div class="flex">
                            <div class="shrink-0">
                                <svg class="shrink-0 size-4 text-blue-600 mt-0.5 dark:text-blue-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="10"/>
                                    <path d="m9 12 2 2 4-4"/>
                                </svg>
                            </div>
                            <div class="ms-3">
                                <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                                    Formato per Province:
                                </h3>
                                <p class="text-sm text-blue-700 dark:text-blue-300 mt-2">Il file Excel deve contenere le seguenti colonne nell'ordine specificato:</p>
                                <ol class="text-sm text-blue-700 dark:text-blue-300 mt-2 list-decimal list-inside space-y-1">
                                    <li><strong>codice_regione</strong> - Codice della regione (non utilizzato)</li>
                                    <li><strong>sigla_provincia</strong> - Sigla della provincia (utilizzato come codice)</li>
                                    <li><strong>denominazione_provincia</strong> - Nome della provincia (utilizzato come descrizione)</li>
                                    <li><strong>tipologia_provincia</strong> - Tipologia (non utilizzato)</li>
                                    <li><strong>numero_comuni</strong> - Numero comuni (non utilizzato)</li>
                                    <li><strong>superficie_kmq</strong> - Superficie (non utilizzato)</li>
                                    <li><strong>codice_sovracomunale</strong> - Codice sovracomunale (non utilizzato)</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="marche-info" class="mb-6" style="display: none;">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 dark:bg-green-800/10 dark:border-green-900">
                        <div class="flex">
                            <div class="shrink-0">
                                <svg class="shrink-0 size-4 text-green-600 mt-0.5 dark:text-green-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"/>
                                    <path d="m9 12 2 2 4-4"/>
                                </svg>
                            </div>
                            <div class="ms-3">
                                <h3 class="text-sm font-medium text-green-800 dark:text-green-200">
                                    Importazione Marche Veicoli:
                                </h3>
                                <p class="text-sm text-green-700 dark:text-green-300 mt-2">Questa importazione recupera automaticamente le marche dei veicoli dall'API Italiana Assicurazioni.</p>
                                <ul class="text-sm text-green-700 dark:text-green-300 mt-2 list-disc list-inside space-y-1">
                                    <li><strong>Nessun file richiesto</strong> - I dati vengono scaricati direttamente dall'API</li>
                                    <li><strong>Parametri utilizzati:</strong> Canale, Codice Compagnia, Codice Agenzia, Codice Agente, Classe Veicolo</li>
                                    <li><strong>Duplicati:</strong> Le marche esistenti vengono aggiornate automaticamente</li>
                                    <li><strong>Chiave identificativa:</strong> Codice marca</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="modelli-info" class="mb-6" style="display: none;">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 dark:bg-green-800/10 dark:border-green-900">
                        <div class="flex">
                            <div class="shrink-0">
                                <svg class="shrink-0 size-4 text-green-600 mt-0.5 dark:text-green-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"/>
                                    <path d="m9 12 2 2 4-4"/>
                                </svg>
                            </div>
                            <div class="ms-3">
                                <h3 class="text-sm font-medium text-green-800 dark:text-green-200">
                                    Importazione Modelli Veicoli:
                                </h3>
                                <p class="text-sm text-green-700 dark:text-green-300 mt-2">Questa importazione recupera automaticamente i modelli dei veicoli dall'API Italiana Assicurazioni per tutte le marche presenti nel database.</p>
                                <ul class="text-sm text-green-700 dark:text-green-300 mt-2 list-disc list-inside space-y-1">
                                    <li><strong>Nessun file richiesto</strong> - I dati vengono scaricati direttamente dall'API</li>
                                    <li><strong>Prerequisito:</strong> Le marche devono essere importate prima dei modelli</li>
                                    <li><strong>Relazione:</strong> Ogni modello viene collegato alla sua marca di appartenenza</li>
                                    <li><strong>Data immatricolazione:</strong> Viene utilizzata la data odierna</li>
                                    <li><strong>Duplicati:</strong> I modelli esistenti vengono aggiornati automaticamente</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="allestimenti-info" class="mb-6" style="display: none;">
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 dark:bg-yellow-800/10 dark:border-yellow-900">
                        <div class="flex">
                            <div class="shrink-0">
                                <svg class="shrink-0 size-4 text-yellow-600 mt-0.5 dark:text-yellow-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/>
                                    <path d="M12 9v4"/>
                                    <path d="m12 17 .01 0"/>
                                </svg>
                            </div>
                            <div class="ms-3">
                                <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                                    Importazione Allestimenti Veicoli:
                                </h3>
                                <p class="text-sm text-yellow-700 dark:text-yellow-300 mt-2">Questa importazione recupera automaticamente gli allestimenti/configurazioni dei veicoli dall'API Italiana Assicurazioni per tutte le combinazioni marca-modello presenti nel database.</p>
                                <ul class="text-sm text-yellow-700 dark:text-yellow-300 mt-2 list-disc list-inside space-y-1">
                                    <li><strong>Nessun file richiesto</strong> - I dati vengono scaricati direttamente dall'API</li>
                                    <li><strong>Prerequisiti:</strong> Marche e modelli devono essere importati prima degli allestimenti</li>
                                    <li><strong>Relazione:</strong> Ogni allestimento viene collegato alla sua marca e modello di appartenenza</li>
                                    <li><strong>Parametri API:</strong> Utilizza codice marca e codice modello per ogni combinazione</li>
                                    <li><strong>Data immatricolazione:</strong> Viene utilizzata la data odierna</li>
                                    <li><strong>Duplicati:</strong> Gli allestimenti esistenti vengono aggiornati automaticamente</li>
                                    <li><strong>Processo:</strong> Può richiedere più tempo in quanto elabora tutte le combinazioni marca-modello</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Progress bar -->
                <div id="import-progress" class="mb-6" style="display: none;">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium text-gray-700 dark:text-neutral-300" id="progress-text">Importazione in corso...</span>
                        <span class="text-sm text-gray-500 dark:text-neutral-500">0%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2 dark:bg-neutral-700">
                        <div class="bg-blue-600 h-2 rounded-full transition-all duration-300 dark:bg-blue-500" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>

                <!-- Results -->
                <div id="import-results" class="mb-6" style="display: none;">
                    <div id="results-content"></div>
                </div>

                <div class="flex justify-end gap-x-2">
                    <button type="submit" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none dark:bg-blue-500 dark:hover:bg-blue-600 dark:focus:bg-blue-600" id="import-btn">
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                            <polyline points="7 10 12 15 17 10"/>
                            <line x1="12" x2="12" y1="15" y2="3"/>
                        </svg>
                        Avvia Importazione
                    </button>
                    <button type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" id="reset-btn" style="display: none;">
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                            <path d="M21 3v5h-5"/>
                            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                            <path d="M3 21v-5h5"/>
                        </svg>
                        Nuova Importazione
                    </button>
                </div>
                </form>
            </div>
        </div>
        <!-- /form -->

        <!-- Instructions -->
        <div class="bg-white border border-gray-200 rounded-xl shadow-sm dark:bg-neutral-800 dark:border-neutral-700">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-neutral-700">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-neutral-200">
                    Istruzioni per l'Importazione
                </h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-800 dark:text-neutral-200 mb-3">Comuni (Cities)</h3>
                        <p class="text-sm text-gray-600 dark:text-neutral-400 mb-3">Per importare i dati dei comuni italiani:</p>
                        <ul class="text-sm text-gray-600 dark:text-neutral-400 space-y-2 list-disc list-inside">
                            <li>Utilizza il file <code class="text-xs bg-gray-100 px-1 py-0.5 rounded dark:bg-neutral-700">gi_comuni_cap.xlsx</code> come riferimento</li>
                            <li>Il sistema aggiornerà i comuni esistenti o ne creerà di nuovi</li>
                            <li>La chiave di identificazione è il <strong>codice_istat</strong></li>
                            <li>Il codice paese sarà automaticamente impostato su "IT"</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-800 dark:text-neutral-200 mb-3">Stati (Countries)</h3>
                        <p class="text-sm text-gray-600 dark:text-neutral-400 mb-3">Per importare i dati degli stati:</p>
                        <ul class="text-sm text-gray-600 dark:text-neutral-400 space-y-2 list-disc list-inside">
                            <li>Utilizza il file <code class="text-xs bg-gray-100 px-1 py-0.5 rounded dark:bg-neutral-700">gi_nazioni.xlsx</code> come riferimento</li>
                            <li>Il sistema aggiornerà gli stati esistenti o ne creerà di nuovi</li>
                            <li>La chiave di identificazione è la <strong>sigla_nazione</strong></li>
                            <li>Assicurati che i codici paese siano univoci</li>
                        </ul>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-800 dark:text-neutral-200 mb-3">Province (Provinces)</h3>
                        <p class="text-sm text-gray-600 dark:text-neutral-400 mb-3">Per importare i dati delle province italiane:</p>
                        <ul class="text-sm text-gray-600 dark:text-neutral-400 space-y-2 list-disc list-inside">
                            <li>Utilizza il file <code class="text-xs bg-gray-100 px-1 py-0.5 rounded dark:bg-neutral-700">gi_province.xlsx</code> come riferimento</li>
                            <li>Il sistema aggiornerà le province esistenti o ne creerà di nuove</li>
                            <li>La chiave di identificazione è la <strong>sigla_provincia</strong></li>
                            <li>Vengono importati solo i campi <strong>sigla_provincia</strong> e <strong>denominazione_provincia</strong></li>
                        </ul>
                    </div>
                    <div>
                        <!-- Empty space for layout balance -->
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-800 dark:text-neutral-200 mb-3">Marche Veicoli (Vehicle Brands)</h3>
                        <p class="text-sm text-gray-600 dark:text-neutral-400 mb-3">Per importare le marche dei veicoli dall'API Italiana:</p>
                        <ul class="text-sm text-gray-600 dark:text-neutral-400 space-y-2 list-disc list-inside">
                            <li><strong>Nessun file Excel richiesto</strong></li>
                            <li>I dati vengono scaricati automaticamente dall'API</li>
                            <li>Utilizza i parametri configurati in <code class="text-xs bg-gray-100 px-1 py-0.5 rounded dark:bg-neutral-700">Defaults.java</code></li>
                            <li>La chiave di identificazione è il <strong>codice marca</strong></li>
                            <li>Prerequisito per l'importazione dei modelli</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-800 dark:text-neutral-200 mb-3">Modelli Veicoli (Vehicle Models)</h3>
                        <p class="text-sm text-gray-600 dark:text-neutral-400 mb-3">Per importare i modelli dei veicoli dall'API Italiana:</p>
                        <ul class="text-sm text-gray-600 dark:text-neutral-400 space-y-2 list-disc list-inside">
                            <li><strong>Nessun file Excel richiesto</strong></li>
                            <li>Richiede che le marche siano già state importate</li>
                            <li>Ogni modello viene collegato alla sua marca</li>
                            <li>Utilizza la data odierna come data di immatricolazione</li>
                            <li>La chiave di identificazione è <strong>codice modello + marca</strong></li>
                        </ul>
                    </div>
                </div>

                <div class="grid grid-cols-1 gap-6 mt-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-800 dark:text-neutral-200 mb-3">Allestimenti Veicoli (Vehicle Model Setups)</h3>
                        <p class="text-sm text-gray-600 dark:text-neutral-400 mb-3">Per importare gli allestimenti/configurazioni dei veicoli dall'API Italiana:</p>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <ul class="text-sm text-gray-600 dark:text-neutral-400 space-y-2 list-disc list-inside">
                                <li><strong>Nessun file Excel richiesto</strong></li>
                                <li>Richiede che marche e modelli siano già stati importati</li>
                                <li>Ogni allestimento viene collegato alla sua marca e modello</li>
                                <li>Utilizza codice marca e codice modello per le chiamate API</li>
                            </ul>
                            <ul class="text-sm text-gray-600 dark:text-neutral-400 space-y-2 list-disc list-inside">
                                <li>Utilizza la data odierna come data di immatricolazione</li>
                                <li>La chiave di identificazione è <strong>codice allestimento + marca + modello</strong></li>
                                <li><strong>Attenzione:</strong> Processo più lungo in quanto elabora tutte le combinazioni</li>
                                <li>Ordine consigliato: Marche → Modelli → Allestimenti</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6 dark:bg-yellow-800/10 dark:border-yellow-900">
                    <div class="flex">
                        <div class="shrink-0">
                            <svg class="shrink-0 size-4 text-yellow-600 mt-0.5 dark:text-yellow-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/>
                                <path d="M12 9v4"/>
                                <path d="m12 17 .01 0"/>
                            </svg>
                        </div>
                        <div class="ms-3">
                            <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Attenzione:</h3>
                            <ul class="text-sm text-yellow-700 dark:text-yellow-300 mt-2 list-disc list-inside space-y-1">
                                <li>L'importazione può richiedere alcuni minuti per file di grandi dimensioni</li>
                                <li>I dati esistenti verranno aggiornati se trovata una corrispondenza</li>
                                <li>Assicurati che il formato del file corrisponda esattamente a quello richiesto</li>
                                <li>La prima riga del file Excel deve contenere le intestazioni delle colonne</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /instructions -->

    </div>
</div>
<!-- /content area -->

{% endblock %}
