{% extends "be/include/base.html" %}

{% set page = 'INSURANCEPROVENANCETYPE' %}
{% set title = curInsuranceProvenanceType is empty ? 'Nuovo Tipo di Provenienza Assicurativa' : 'Modifica Tipo di Provenienza Assicurativa' %}

{% block extrahead %}

<title>{{ title }} </title>

<!-- Specific script -->
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}
<!-- specific script-->

<!-- Page script -->
<script src="{{ contextPath }}/be/js/pages/insuranceprovenancetype.js?{{ buildNumber }}"></script>
<!-- /page script -->

{% endblock %}

{% block content %}
<script class="reload-script-on-load">
    addRoute('BE_INSURANCEPROVENANCETYPE', '{{ routes("BE_INSURANCEPROVENANCETYPE") }}');
</script>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Form -->
    <div class="card">
        <div class="card-header">
            {% if curInsuranceProvenanceType is empty %}
            <h5 class="mb-0">Inserisci Tipo di Provenienza Assicurativa</h5>
            {% else %}
            <h5 class="mb-0">Modifica Tipo di Provenienza Assicurativa</h5>
            {% endif %}
        </div>

        <div class="card-body">
            {% set postUrl = routes('BE_INSURANCEPROVENANCETYPE_SAVE') %}
            {% if curInsuranceProvenanceType.id is not empty %}                
            {% set postUrl = routes('BE_INSURANCEPROVENANCETYPE_SAVE') + '?insuranceProvenanceTypeId=' + curInsuranceProvenanceType.id %}
            {% endif %}

            <form id="insuranceprovenancetype-edit" class="form-validate" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">
                
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Codice: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="code" type="text" class="form-control form-control-maxlength" placeholder="Codice identificativo" value="{{ curInsuranceProvenanceType.code }}" required maxlength="50">
                        <div class="form-text text-muted">Codice univoco per identificare il tipo di provenienza.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Titolo: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="title" type="text" class="form-control form-control-maxlength" placeholder="Titolo del tipo di provenienza" value="{{ curInsuranceProvenanceType.title }}" required maxlength="100">
                        <div class="form-text text-muted">Titolo descrittivo del tipo di provenienza assicurativa.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Descrizione:</label>
                    <div class="col-lg-9">
                        <textarea name="description" class="form-control form-control-maxlength" rows="4" placeholder="Descrizione dettagliata" maxlength="500">{{ curInsuranceProvenanceType.description }}</textarea>
                        <div class="form-text text-muted">Descrizione dettagliata del tipo di provenienza assicurativa.</div>
                    </div>
                </div>

                <div class="text-end">
                    <a href="{{ routes('BE_INSURANCEPROVENANCETYPE_COLLECTION') }}" class="btn btn-light">
                        <i class="ph-arrow-left me-2"></i>
                        Torna alla lista
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="ph-check me-2"></i>
                        {% if curInsuranceProvenanceType is empty %}
                        Crea Tipo Provenienza
                        {% else %}
                        Aggiorna Tipo Provenienza
                        {% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
    <!-- /form -->
</div>
<!-- /content area -->

{% endblock %}
