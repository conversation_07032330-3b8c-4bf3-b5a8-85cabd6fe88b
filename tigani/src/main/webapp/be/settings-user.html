{% extends "be/include/base.html" %}

{% set area = 'SETTINGS' %}
{% set page = 'SETTINGS_USER' %}
{% set title = curUser is empty ? 'Nuovo utente' : 'Modifica utente' %}

{% block extrahead %}

<title>{{ title }} </title>

<!-- Specific script -->
{% include "be/include/snippets/plugins/filepond.html" %}
{% include "be/include/snippets/plugins/select2.html" %}
{% include "be/include/snippets/plugins/daterangepicker.html" %}
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}
<!-- specific script-->

<!-- Page script -->
<script src="{{ contextPath }}/be/js/pages/settings-user-form.js?{{ buildNumber }}"></script>
<!-- /page script -->

{% endblock %}

{% block content %}
<script class="reload-script-on-load">
    addRoute('BE_IMAGE', '{{ routes("BE_IMAGE") }}');
    addRoute('BE_SETTINGS_USER', '{{ routes("BE_SETTINGS_USER") }}');
    addVariables('imageId', '{{ curUser.imageId }}');
</script>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Checkbox selection -->
    <div class="card">
        <div class="card-header">
            {% if curUser is empty %}
            <h5 class="mb-0">Inserisci utente</h5>
            {% else %}
            <h5 class="mb-0">Modifica utente</h5>
            {% endif %}
        </div>

        <div class="card-body">
            {% set postUrl = routes('BE_SETTINGS_USER_SAVE') %}
            {% if curUser.id is not empty %}                
            {% set postUrl = routes('BE_SETTINGS_USER_SAVE') + '?userId=' + curUser.id %}
            {% endif %}

            <form id="user-edit" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Immagine profilo:</label>
                    <div class="col-lg-9">                              
                        <input id="logo" name="logo" type="file" class="filepond filepond-avatar">                        
                        <div class="form-text text-muted">Formato immagine .jpg, .png o .svg.</div>                                                        
                    </div>

                </div>    
                <!--<div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Email: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="email" type="text" class="form-control" placeholder="Email" value="{{ curUser.email }}" required {{ disabled }}>
                    </div>
                </div>-->

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Nome: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="name" type="text" class="form-control" placeholder="Nome" value="{{ curUser.name }}" required {{ disabled }}>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Email di accesso: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="username" type="text" class="form-control" placeholder="Email di accesso" value="{{ curUser.username }}" required {{ disabled }}>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Password: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="password" type="password" class="form-control" placeholder="Password" value="{{ curUser.password }}" required {{ disabled }}>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Tipologia Profilo: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <select name="profileType" class="form-control" data-minimum-results-for-search="Infinity">
                            <option value="system" {{ (curUser is not empty and curUser.profileType is not empty and curUser.profileType equals 'system') ? 'selected' : '' }}>System</option>
                            <option value="admin" {{ (curUser is not empty and curUser.profileType is not empty and curUser.profileType equals 'admin') ? 'selected' : '' }}>Admin</option>
                            <option value="operator" {{ (curUser is not empty and curUser.profileType is not empty and curUser.profileType equals 'operator') ? 'selected' : '' }}>Operatore</option>
                        </select>
                    </div>
                </div>
                
                {#
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Attivo:</label>
                    <div class="col-lg-9 align-self-center">
                        <div class="form-check form-switch">
                            <input type="checkbox" class="form-check-input" name="active" {{ curUser.active ? 'checked' : '' }} {{ disabled }}>                            
                        </div>                        
                    </div>
                </div>
                #}

                <div class="text-end">                    
                    <button type="submit" class="btn btn-primary">
                        <i class="ph-check me-2"></i>
                        Salva
                    </button>                    
                </div>
            </form>
        </div>
    </div>
    <!-- /checkbox selection -->
</div>
<!-- /content area -->

{% endblock %}