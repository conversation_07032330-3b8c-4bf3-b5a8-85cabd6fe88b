<!-- Product Card -->
<div class="p-5 md:p-8 bg-white border border-gray-200 shadow-2xs rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
    <!-- Title -->
    <div class="mb-4 xl:mb-8">
        <h1 class="text-lg font-semibold text-gray-800 dark:text-neutral-200">
            Regole premi
        </h1>
        <p class="text-sm text-gray-500 dark:text-neutral-500">
            Sistema intelligente con condizioni tipizzate per tutti i tipi di regole di pricing tariffario
        </p>
    </div>
    <!-- End Title -->
    <!-- Form -->
    <form>

        <!-- Grid -->
        <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
            <div class="sm:col-span-4 xl:col-span-3 2xl:col-span-2">
                <label class="sm:mt-2.5 inline-block text-sm text-gray-500 dark:text-neutral-500">
                    Regole
                </label>
            </div>
            <!-- End Col -->
            <div class="sm:col-span-8 xl:col-span-10">

                <!-- Users Table Card -->
                <div class="flex flex-col bg-white border border-gray-200 shadow-2xs rounded-xl dark:bg-neutral-800 dark:border-neutral-700">

                    <!-- Header -->
                    <div class="py-3 px-5 flex flex-wrap justify-between items-center gap-2 border-b border-gray-200 dark:border-neutral-700">
                        <!-- Title -->
                        <div class="flex flex-wrap items-center gap-2">
                            <h2 class="font-medium text-gray-800 dark:text-neutral-200">
                                Regole
                            </h2>
                        </div>
                        <!-- End Title -->
                        <!-- Actions -->
                        <div class="flex flex-wrap items-center gap-2">
                            {% if user.hasPermission('RULES_MANAGEMENT', 'create') %}
                            <button type="button" id="new-product-btn" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-gray-800 border border-gray-800 text-white text-sm font-medium rounded-lg shadow-2xs align-middle hover:bg-gray-950 focus:outline-hidden focus:bg-gray-900 dark:bg-white dark:text-neutral-800 dark:hover:bg-neutral-200 dark:focus:bg-neutral-200 disabled:opacity-50 disabled:pointer-events-none" aria-haspopup="dialog" aria-expanded="false" aria-controls="modal-pricing-rules" data-hs-overlay="#modal-pricing-rules">
                                <svg class="block shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
                                Nuova regola
                            </button>
                            {% endif %}                           
                        </div>
                        <!-- End Actions -->
                    </div>
                    <!-- End Header -->
                    
                    <div>
                        <!-- Tab Content -->
                        <div id="hs-pro-tabs-dut-all" role="tabpanel" aria-labelledby="hs-pro-tabs-dut-item-all">
                            
                            <!-- DataTable Header -->
                            <div class="flex flex-wrap items-center gap-2 p-5 border-b border-gray-200 dark:bg-neutral-800 dark:border-neutral-700">
                                <!-- Search -->
                                {% include "be/include/snippets/tables/search.html" %}
                                <!-- Search -->
                                <div class="flex-1 flex items-center justify-start space-x-2 xl:justify-end">
                                    <div class="flex flex-wrap md:flex-nowrap items-center gap-2">
                                        <!-- Filters -->
                                        {% if user.hasPermission('DEALER_MANAGEMENT', 'view') %}
                                        {% include "be/include/snippets/tables/filters.html" %}
                                        {% endif %}
                                        <!-- End Filters -->

                                        <!-- Export -->
                                        {% if user.hasPermission('DEALER_MANAGEMENT', 'view') %}
                                        {% include "be/include/snippets/tables/export.html" %}
                                        {% endif %}
                                        <!-- End Export -->
                                    </div>
                                </div>
                            </div>
                            <!-- End DataTable Header -->
                            <!-- Table Content -->
                            <div class="overflow-auto border-b border-gray-200 dark:bg-neutral-800 dark:border-neutral-700">
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-neutral-700">
                                    <thead class="bg-gray-50 dark:bg-neutral-700">
                                        <tr>

                                            <th scope="col" class="px-6 py-3 text-start text-xs font-medium text-gray-500 uppercase dark:text-neutral-500">Regola</th>                                            
                                            <th scope="col" class="px-6 py-3 text-start text-xs font-medium text-gray-500 uppercase dark:text-neutral-500">Canali</th>
                                            <th scope="col" class="px-6 py-3 text-start text-xs font-medium text-gray-500 uppercase dark:text-neutral-500">Garanzie</th>
                                            <th scope="col" class="px-6 py-3 text-start text-xs font-medium text-gray-500 uppercase dark:text-neutral-500">Condizioni</th>
                                            <th scope="col" class="px-6 py-3 text-start text-xs font-medium text-gray-500 uppercase dark:text-neutral-500">Effetto</th>
                                            <th scope="col" class="px-6 py-3 text-start text-xs font-medium text-gray-500 uppercase dark:text-neutral-500">Priorità</th>
                                            <th scope="col" class="px-6 py-3 text-start text-xs font-medium text-gray-500 uppercase dark:text-neutral-500">Stato</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200 dark:divide-neutral-700">
                                        <tr>

                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-neutral-200">Sconto Provincia Milano</td>                                            
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">

                                                <div class="space-y-0">
                                                    <!-- Sito Ufficiale -->
                                                    <div class="flex items-center gap-1">
                                                        <svg class="shrink-0 size-3 text-green-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-icon lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                                                        <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                            Sito ufficiale
                                                        </span>
                                                    </div>
                                                    <!-- Siti Partner -->
                                                    <div class="flex items-center gap-1">
                                                        <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x-icon lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                                                        <div class="relative group">
                                                            <span class="text-sm text-gray-800 dark:text-neutral-200 cursor-pointer flex items-center gap-1">
                                                                Siti partner
                                                            </span>

                                                        </div>
                                                    </div>
                                                    <!-- Dealer -->
                                                    <div class="flex items-center gap-1">
                                                        <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x-icon lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                                                        <div class="relative group">
                                                            <span class="text-sm text-gray-800 dark:text-neutral-200 cursor-pointer flex items-center gap-1">
                                                                Rivenditori
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-gray-800 dark:text-neutral-200">

                                                <div class="flex flex-wrap gap-1">
                                                    <span class="py-0.5 px-1.5 text-xs bg-gray-100 text-gray-800 rounded-sm dark:bg-neutral-700 dark:text-neutral-400">Responsabilità civile</span>
                                                    <span class="py-0.5 px-1.5 text-xs bg-gray-100 text-gray-800 rounded-sm dark:bg-neutral-700 dark:text-neutral-400">Incendio Furto</span>
                                                    <span class="py-0.5 px-1.5 text-xs bg-gray-100 text-gray-800 rounded-sm dark:bg-neutral-700 dark:text-neutral-400">Assistenza</span>
                                                </div>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-gray-800 dark:text-neutral-200">
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    Provincia: Milano
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-gray-800 dark:text-neutral-200">
                                                <span class="inline-flex items-center gap-x-1 text-xs font-medium rounded-full text-teal-500">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <polyline points="22 17 13.5 8.5 8.5 13.5 2 7"></polyline>
                                                        <polyline points="16 17 22 17 22 11"></polyline>
                                                    </svg>
                                                    Premio: -5%
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>

                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-neutral-200">Sconto Over 65</td>                                           
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                                <div class="space-y-0">
                                                    <!-- Sito Ufficiale -->
                                                    <div class="flex items-center gap-1">
                                                        <svg class="shrink-0 size-3 text-green-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-icon lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                                                        <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                            Sito ufficiale
                                                        </span>
                                                    </div>
                                                    <!-- Siti Partner -->
                                                    <div class="flex items-center gap-1">
                                                        <svg class="shrink-0 size-3 text-green-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-icon lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                                                        <div class="relative group">
                                                            <span class="text-sm text-gray-800 dark:text-neutral-200 cursor-pointer flex items-center gap-1">
                                                                Siti partner
                                                                <span class="inline-flex items-center text-xs text-gray-500 dark:text-neutral-500">(4/5 attivi)</span>
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="size-3 text-gray-400">
                                                                    <circle cx="12" cy="12" r="10"/>
                                                                    <path d="M12 16v-4"/>
                                                                    <path d="M12 8h.01"/>
                                                                </svg>
                                                            </span>

                                                            <!-- Popover Partner -->
                                                            <div class="absolute bottom-full left-0 mb-2 invisible group-hover:visible opacity-0 group-hover:opacity-100 transition-all duration-200 z-[100]">
                                                                <div class="bg-white dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700 rounded-lg shadow-xl p-3 w-48">
                                                                    <div class="text-xs font-medium text-gray-700 dark:text-neutral-300 mb-2">Siti partner</div>
                                                                    <ul class="space-y-1 text-xs text-gray-600 dark:text-neutral-400">
                                                                        <li class="flex items-center gap-2">
                                                                            <span class="size-1.5 bg-green-500 rounded-full"></span>
                                                                            Assicuriamo la tua Passione
                                                                        </li>
                                                                        <li class="flex items-center gap-2">
                                                                            <span class="size-1.5 bg-green-500 rounded-full"></span>
                                                                            Triumph Easy
                                                                        </li>
                                                                        <li class="flex items-center gap-2">
                                                                            <span class="size-1.5 bg-green-500 rounded-full"></span>
                                                                            Ducati Motoprotection
                                                                        </li>
                                                                        <li class="flex items-center gap-2">
                                                                            <span class="size-1.5 bg-green-500 rounded-full"></span>
                                                                            Motorrad Assicura
                                                                        </li>
                                                                        <li class="flex items-center gap-2">
                                                                            <span class="size-1.5 bg-red-500 rounded-full"></span>
                                                                            Motoprotection
                                                                        </li>
                                                                    </ul>
                                                                </div>
                                                                <!-- Freccia del popover -->
                                                                <div class="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-white dark:border-t-neutral-800"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <!-- Dealer -->
                                                    <div class="flex items-center gap-1">
                                                        <svg class="shrink-0 size-3 text-green-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-icon lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                                                        <div class="relative group">
                                                            <span class="text-sm text-gray-800 dark:text-neutral-200 cursor-pointer flex items-center gap-1">
                                                                Rivenditori
                                                                <span class="inline-flex items-center text-xs text-gray-500 dark:text-neutral-500">(124/156 attivi)</span>
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="size-3 text-gray-400">
                                                                    <circle cx="12" cy="12" r="10"/>
                                                                    <path d="M12 16v-4"/>
                                                                    <path d="M12 8h.01"/>
                                                                </svg>
                                                            </span>

                                                            <!-- Popover Dealer -->
                                                            <div class="absolute bottom-full left-0 mb-2 invisible group-hover:visible opacity-0 group-hover:opacity-100 transition-all duration-200 z-[100]">
                                                                <div class="bg-white dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700 rounded-lg shadow-xl p-3 w-40">
                                                                    <div class="text-xs font-medium text-gray-700 dark:text-neutral-300 mb-2">Rivenditori</div>
                                                                    <div class="space-y-2 text-xs text-gray-600 dark:text-neutral-400">
                                                                        <div class="flex justify-between">
                                                                            <span>Attivi:</span>
                                                                            <span class="font-medium text-green-600 dark:text-green-500">124</span>
                                                                        </div>
                                                                        <div class="flex justify-between">
                                                                            <span>Totali:</span>
                                                                            <span class="font-medium text-gray-800 dark:text-neutral-200">156</span>
                                                                        </div>
                                                                        <div class="border-t border-gray-200 dark:border-neutral-600 pt-2 mt-2">
                                                                            <div class="flex justify-between">
                                                                                <span>Tasso:</span>
                                                                                <span class="font-medium text-blue-600 dark:text-blue-500">79%</span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <!-- Freccia del popover -->
                                                                <div class="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-white dark:border-t-neutral-800"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-gray-800 dark:text-neutral-200">

                                                <div class="flex flex-wrap gap-1">
                                                    <span class="py-0.5 px-1.5 text-xs bg-gray-100 text-gray-800 rounded-sm dark:bg-neutral-700 dark:text-neutral-400">Tutte</span>
                                                </div>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-gray-800 dark:text-neutral-200">
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    Età: > 65 anni
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-gray-800 dark:text-neutral-200">
                                                <span class="inline-flex items-center gap-x-1 text-xs font-medium rounded-full text-teal-500">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <polyline points="22 17 13.5 8.5 8.5 13.5 2 7"></polyline>
                                                        <polyline points="16 17 22 17 22 11"></polyline>
                                                    </svg>
                                                    Premio: -5%
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-gray-800 dark:text-neutral-200">
                                                1
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-gray-800 dark:text-neutral-200">
                                                <div class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-green-100 text-green-800 rounded-full cursor-pointer" tabindex="0">
                                                    <!-- Badge -->
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="m9 12 2 2 4-4"></path><circle cx="12" cy="12" r="10"></circle>
                                                    </svg>
                                                    Attiva
                                                </div>
                                            </td>

                                        </tr>
                                        <tr>

                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-neutral-200">Combo RC+IF</td>
                                            
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">

                                                <div class="space-y-0">
                                                    <!-- Sito Ufficiale -->
                                                    <div class="flex items-center gap-1">
                                                        <svg class="shrink-0 size-3 text-green-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-icon lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                                                        <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                            Sito ufficiale
                                                        </span>
                                                    </div>
                                                    <!-- Siti Partner -->
                                                    <div class="flex items-center gap-1">
                                                        <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x-icon lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                                                        <div class="relative group">
                                                            <span class="text-sm text-gray-800 dark:text-neutral-200 cursor-pointer flex items-center gap-1">
                                                                Siti partner
                                                            </span>

                                                        </div>
                                                    </div>
                                                    <!-- Dealer -->
                                                    <div class="flex items-center gap-1">
                                                        <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x-icon lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                                                        <div class="relative group">
                                                            <span class="text-sm text-gray-800 dark:text-neutral-200 cursor-pointer flex items-center gap-1">
                                                                Rivenditori
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-gray-800 dark:text-neutral-200">

                                                <div class="flex flex-wrap gap-1">
                                                    <span class="py-0.5 px-1.5 text-xs bg-gray-100 text-gray-800 rounded-sm dark:bg-neutral-700 dark:text-neutral-400">Tutte</span>
                                                </div>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-gray-800 dark:text-neutral-200">
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    Garanzie: Responsabilità Civile + Furto e Incendio
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-gray-800 dark:text-neutral-200">
                                                <span class="inline-flex items-center gap-x-1 text-xs font-medium rounded-full text-red-500">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline>
                                                        <polyline points="16 7 22 7 22 13"></polyline>
                                                    </svg>
                                                    Premio: +5%
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>

                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-neutral-200">Regola</td>                                            
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">

                                                <div class="space-y-0">
                                                    <!-- Sito Ufficiale -->
                                                    <div class="flex items-center gap-1">
                                                        <svg class="shrink-0 size-3 text-green-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-icon lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                                                        <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                            Sito ufficiale
                                                        </span>
                                                    </div>
                                                    <!-- Siti Partner -->
                                                    <div class="flex items-center gap-1">
                                                        <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x-icon lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                                                        <div class="relative group">
                                                            <span class="text-sm text-gray-800 dark:text-neutral-200 cursor-pointer flex items-center gap-1">
                                                                Siti partner
                                                            </span>

                                                        </div>
                                                    </div>
                                                    <!-- Dealer -->
                                                    <div class="flex items-center gap-1">
                                                        <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x-icon lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                                                        <div class="relative group">
                                                            <span class="text-sm text-gray-800 dark:text-neutral-200 cursor-pointer flex items-center gap-1">
                                                                Rivenditori
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-gray-800 dark:text-neutral-200">

                                                <div class="flex flex-wrap gap-1">
                                                    <span class="py-0.5 px-1.5 text-xs bg-gray-100 text-gray-800 rounded-sm dark:bg-neutral-700 dark:text-neutral-400">Responsabilità civile</span>
                                                </div>

                                            </td>

                                            <td class="px-6 py-4 whitespace-nowrap text-gray-800 dark:text-neutral-200">
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    Condizione: Valore
                                                </span>
                                            </td>

                                            <td class="px-6 py-4 whitespace-nowrap text-gray-800 dark:text-neutral-200">
                                                <span class="inline-flex items-center gap-x-1 text-xs font-medium rounded-full text-teal-500">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <polyline points="22 17 13.5 8.5 8.5 13.5 2 7"></polyline>
                                                        <polyline points="16 17 22 17 22 11"></polyline>
                                                    </svg>
                                                    Premio: -5%
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>

                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-neutral-200">Regola</td>
                                            
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">

                                                <div class="space-y-0">
                                                    <!-- Sito Ufficiale -->
                                                    <div class="flex items-center gap-1">
                                                        <svg class="shrink-0 size-3 text-green-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-icon lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                                                        <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                            Sito ufficiale
                                                        </span>
                                                    </div>
                                                    <!-- Siti Partner -->
                                                    <div class="flex items-center gap-1">
                                                        <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x-icon lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                                                        <div class="relative group">
                                                            <span class="text-sm text-gray-800 dark:text-neutral-200 cursor-pointer flex items-center gap-1">
                                                                Siti partner
                                                            </span>

                                                        </div>
                                                    </div>
                                                    <!-- Dealer -->
                                                    <div class="flex items-center gap-1">
                                                        <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x-icon lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                                                        <div class="relative group">
                                                            <span class="text-sm text-gray-800 dark:text-neutral-200 cursor-pointer flex items-center gap-1">
                                                                Rivenditori
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-gray-800 dark:text-neutral-200">

                                                <div class="flex flex-wrap gap-1">
                                                    <span class="py-0.5 px-1.5 text-xs bg-gray-100 text-gray-800 rounded-sm dark:bg-neutral-700 dark:text-neutral-400">Responsabilità civile</span>
                                                    <span class="py-0.5 px-1.5 text-xs bg-gray-100 text-gray-800 rounded-sm dark:bg-neutral-700 dark:text-neutral-400">Incendio Furto</span>
                                                    <span class="py-0.5 px-1.5 text-xs bg-gray-100 text-gray-800 rounded-sm dark:bg-neutral-700 dark:text-neutral-400">Assistenza</span>

                                                    <!-- Badge +2 con hover -->
                                                    <div class="relative group inline-flex">
                                                        <span class="py-0.5 px-1.5 text-xs bg-gray-100 text-gray-800 rounded-sm dark:bg-neutral-700 dark:text-neutral-400 cursor-pointer">+2</span>

                                                        <!-- Popover con garanzie aggiuntive -->
                                                        <div class="absolute bottom-full left-0 mb-2 invisible group-hover:visible opacity-0 group-hover:opacity-100 transition-all duration-200 z-[9999]">
                                                            <div class="max-w-64 p-2 bg-white border border-gray-200 rounded-lg shadow-xl dark:bg-neutral-800 dark:border-neutral-700">
                                                                <div class="flex flex-wrap gap-1">
                                                                    <span class="py-0.5 px-1.5 text-xs bg-gray-100 text-gray-800 rounded-sm dark:bg-neutral-700 dark:text-neutral-400">
                                                                        Kasko
                                                                    </span>
                                                                    <span class="py-0.5 px-1.5 text-xs bg-gray-100 text-gray-800 rounded-sm dark:bg-neutral-700 dark:text-neutral-400">
                                                                        Cristalli
                                                                    </span>
                                                                </div>
                                                            </div>
                                                            <!-- Freccia del popover -->
                                                            <div class="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-white dark:border-t-neutral-800"></div>
                                                        </div>
                                                    </div>
                                                </div>

                                            </td>

                                            <td class="px-6 py-4 whitespace-nowrap text-gray-800 dark:text-neutral-200">
                                                <span class="text-sm text-gray-800 dark:text-neutral-200">
                                                    Condizione: Valore
                                                </span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div><!-- End Table Content -->

                            <!-- Footer -->
                            <div class="py-1 px-4">
                                <nav class="flex items-center space-x-1" aria-label="Pagination">
                                    <button type="button" class="p-2.5 min-w-10 inline-flex justify-center items-center gap-x-2 text-sm rounded-full text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" aria-label="Previous">
                                        <span aria-hidden="true">«</span>
                                        <span class="sr-only">Previous</span>
                                    </button>
                                    <button type="button" class="min-w-10 flex justify-center items-center text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 py-2.5 text-sm rounded-full disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:focus:bg-neutral-700 dark:hover:bg-neutral-700" aria-current="page">1</button>
                                    <button type="button" class="min-w-10 flex justify-center items-center text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 py-2.5 text-sm rounded-full disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:focus:bg-neutral-700 dark:hover:bg-neutral-700">2</button>
                                    <button type="button" class="min-w-10 flex justify-center items-center text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 py-2.5 text-sm rounded-full disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:focus:bg-neutral-700 dark:hover:bg-neutral-700">3</button>
                                    <button type="button" class="p-2.5 min-w-10 inline-flex justify-center items-center gap-x-2 text-sm rounded-full text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" aria-label="Next">
                                        <span class="sr-only">Next</span>
                                        <span aria-hidden="true">»</span>
                                    </button>
                                </nav>
                            </div>
                            <!-- End Footer -->
                        </div>
                        <!-- End Tab Content -->

                        <!-- Tab Content -->
                        <div id="hs-pro-tabs-dut-archived" class="hidden" role="tabpanel" aria-labelledby="hs-pro-tabs-dut-item-archived">
                            <!-- Empty State -->
                            <div class="p-5  flex flex-col justify-center items-center text-center">
                                <svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800" />
                                    <rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-50 dark:stroke-neutral-700/10" />
                                    <rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                                    <rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                                    <rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                                    <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800" />
                                    <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/30" />
                                    <rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                                    <rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                                    <rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                                    <g filter="url(#filter4)">
                                        <rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor" class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges" />
                                        <rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges" />
                                        <rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700 " />
                                        <rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                                        <rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                                    </g>
                                    <defs>
                                        <filter id="filter4" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                            <feFlood flood-opacity="0" result="BackgroundImageFix" />
                                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                                            <feOffset dy="6" />
                                            <feGaussianBlur stdDeviation="6" />
                                            <feComposite in2="hardAlpha" operator="out" />
                                            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0" />
                                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1187_14810" />
                                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810" result="shape" />
                                        </filter>
                                    </defs>
                                </svg>

                                <div class="max-w-sm mx-auto">
                                    <p class="mt-2 font-medium text-gray-800 dark:text-neutral-200">
                                        Non sono ancora state create regole di questo tipo
                                    </p>
                                    <p class="mb-5 text-sm text-gray-500 dark:text-neutral-500">
                                        Le regole di questo tipo verranno visualizzate qui.
                                    </p>
                                </div>

                                <button type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-gray-800 border border-gray-800 text-white text-sm font-medium rounded-lg shadow-2xs align-middle hover:bg-gray-950 focus:outline-hidden focus:bg-gray-900 dark:bg-white dark:text-neutral-800 dark:hover:bg-neutral-200 dark:focus:bg-neutral-200 disabled:opacity-50 disabled:pointer-events-none">
                                    <svg class="block shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
                                    Nuova regola
                                </button>

                            </div>
                            <!-- End Empty State -->
                        </div>
                        <!-- End Tab Content -->

                        <!-- Tab Content -->
                        <div id="hs-pro-tabs-dut-publish" class="hidden" role="tabpanel" aria-labelledby="hs-pro-tabs-dut-item-publish">
                            <!-- Empty State -->
                            <div class="p-5  flex flex-col justify-center items-center text-center">
                                <svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800" />
                                    <rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-50 dark:stroke-neutral-700/10" />
                                    <rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                                    <rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                                    <rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                                    <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800" />
                                    <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/30" />
                                    <rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                                    <rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                                    <rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                                    <g filter="url(#filter5)">
                                        <rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor" class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges" />
                                        <rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges" />
                                        <rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700 " />
                                        <rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                                        <rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                                    </g>
                                    <defs>
                                        <filter id="filter5" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                            <feFlood flood-opacity="0" result="BackgroundImageFix" />
                                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                                            <feOffset dy="6" />
                                            <feGaussianBlur stdDeviation="6" />
                                            <feComposite in2="hardAlpha" operator="out" />
                                            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0" />
                                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1187_14810" />
                                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810" result="shape" />
                                        </filter>
                                    </defs>
                                </svg>

                                <div class="max-w-sm mx-auto">
                                    <p class="mt-2 font-medium text-gray-800 dark:text-neutral-200">
                                        Your data will appear here soon.
                                    </p>
                                    <p class="mb-5 text-sm text-gray-500 dark:text-neutral-500">
                                        In the meantime, you can create new custom insights to monitor your most important metrics.
                                    </p>
                                </div>

                                <a class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="#">
                                    Learn more
                                </a>
                            </div>
                            <!-- End Empty State -->
                        </div>
                        <!-- End Tab Content -->

                        <!-- Tab Content -->
                        <div id="hs-pro-tabs-dut-unpublish" class="hidden" role="tabpanel" aria-labelledby="hs-pro-tabs-dut-item-unpublish">
                            <!-- Empty State -->
                            <div class="p-5  flex flex-col justify-center items-center text-center">
                                <svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800" />
                                    <rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-50 dark:stroke-neutral-700/10" />
                                    <rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                                    <rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                                    <rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                                    <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800" />
                                    <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/30" />
                                    <rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                                    <rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                                    <rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                                    <g filter="url(#filter6)">
                                        <rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor" class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges" />
                                        <rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges" />
                                        <rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700 " />
                                        <rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                                        <rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                                    </g>
                                    <defs>
                                        <filter id="filter6" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                            <feFlood flood-opacity="0" result="BackgroundImageFix" />
                                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                                            <feOffset dy="6" />
                                            <feGaussianBlur stdDeviation="6" />
                                            <feComposite in2="hardAlpha" operator="out" />
                                            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0" />
                                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1187_14810" />
                                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810" result="shape" />
                                        </filter>
                                    </defs>
                                </svg>

                                <div class="max-w-sm mx-auto">
                                    <p class="mt-2 font-medium text-gray-800 dark:text-neutral-200">
                                        Your data will appear here soon.
                                    </p>
                                    <p class="mb-5 text-sm text-gray-500 dark:text-neutral-500">
                                        In the meantime, you can create new custom insights to monitor your most important metrics.
                                    </p>
                                </div>

                                <a class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="#">
                                    Learn more
                                </a>
                            </div>
                            <!-- End Empty State -->
                        </div>
                        <!-- End Tab Content -->
                    </div>
                </div>
                <!-- End Users Table Card -->


            </div>
            <!-- End Col -->
        </div>
        <!-- End Grid -->

    </form>
    <!-- End Form -->
</div>
<!-- End Product Card -->

<div id="modal-pricing-rules" class="hs-overlay [--overlay-backdrop:static] hidden size-full fixed top-0 start-0 z-80 overflow-x-hidden overflow-y-auto pointer-events-none" role="dialog" tabindex="-1" aria-labelledby="modal-pricing-rules-label" data-hs-overlay-keyboard="false">
    <div class="hs-overlay-open:mt-7 hs-overlay-open:opacity-100 hs-overlay-open:duration-500 mt-0 opacity-0 ease-out transition-all lg:max-w-5xl lg:w-full m-3 lg:mx-auto">
        <div class="flex flex-col bg-white border border-gray-200 shadow-2xl rounded-xl pointer-events-auto dark:bg-neutral-800 dark:border-neutral-700 dark:shadow-neutral-700/70">
            <div class="flex justify-between items-center py-3 px-4 border-b border-gray-200 dark:border-neutral-700">
                <h3 id="modal-pricing-rules-label" class="font-bold text-gray-800 dark:text-white">
                    Nuova regola
                </h3>
                <button type="button" class="size-8 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent bg-gray-100 text-gray-800 hover:bg-gray-200 focus:outline-hidden focus:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-700 dark:hover:bg-neutral-600 dark:text-neutral-400 dark:focus:bg-neutral-600" aria-label="Close" data-hs-overlay="#modal-pricing-rules">
                    <span class="sr-only">Close</span>
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 6 6 18"></path>
                        <path d="m6 6 12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="overflow-y-auto">
                <div class="grid grid-cols-1 md:grid-cols-12" data-hs-stepper="">
                    <!-- Sidebar Sinistra -->
                    <div class="md:col-span-4 p-6">
                        <div class="space-y-6">
                            <!-- Header Sidebar -->
                            <div>                            
                                <p class="text-sm text-gray-600 dark:text-neutral-400">
                                    Completa i passaggi per configurare la nuova regola
                                </p>
                            </div>
                            <!-- Stepper Verticale -->
                            <div data-hs-stepper='{"mode": "non-linear"}'>
                                <!-- Item 1: Informazioni -->
                                <div class="group relative flex gap-x-3.5" data-hs-stepper-nav-item='{"index": 1}'>
                                    <!-- Icon -->
                                    <div class="relative group-last:after:hidden after:absolute after:top-6 after:bottom-1 after:start-2.5 after:w-px after:-translate-x-[0.5px] after:bg-gray-200 dark:after:bg-neutral-700 hs-stepper-success:after:bg-green-600 hs-stepper-completed:after:bg-green-600">
                                        <span class="size-5 flex justify-center items-center bg-gray-800 text-white rounded-full hs-stepper-active:bg-gray-800 hs-stepper-success:bg-green-600 hs-stepper-completed:bg-green-600 dark:bg-blue-500 dark:hs-stepper-success:bg-green-500 dark:hs-stepper-completed:bg-green-500">
                                            <span class="hs-stepper-success:hidden hs-stepper-completed:hidden text-xs">1</span>
                                            <svg class="hidden shrink-0 size-3.5 hs-stepper-success:block hs-stepper-completed:block" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M20 6 9 17l-5-5"></path>
                                            </svg>
                                        </span>
                                    </div>
                                    <!-- End Icon -->
                                    <!-- Right Content -->
                                    <div class="-mt-1 grow pb-5 group-last:pb-0">
                                        <span class="text-sm font-medium text-gray-800 dark:text-neutral-200">
                                            Informazioni
                                        </span>
                                        <p class="text-xs text-gray-500 dark:text-neutral-400 mt-1">
                                            Dati generali della regola
                                        </p>
                                    </div>
                                    <!-- End Right Content -->
                                </div>
                                <!-- End Item -->
                                
                                <!-- Item 2: Condizioni -->
                                <div class="group relative flex gap-x-3.5" data-hs-stepper-nav-item='{"index": 2}'>
                                    <!-- Icon -->
                                    <div class="relative group-last:after:hidden after:absolute after:top-6 after:bottom-1 after:start-2.5 after:w-px after:-translate-x-[0.5px] after:bg-gray-200 dark:after:bg-neutral-700 hs-stepper-success:after:bg-green-600 hs-stepper-completed:after:bg-green-600">
                                        <span class="size-5 flex justify-center items-center bg-gray-100 text-gray-800 rounded-full hs-stepper-active:bg-gray-800 hs-stepper-active:text-white hs-stepper-success:bg-green-600 hs-stepper-success:text-white hs-stepper-completed:bg-green-600 hs-stepper-completed:text-white dark:bg-neutral-700 dark:text-neutral-200 dark:hs-stepper-active:bg-blue-500 dark:hs-stepper-success:bg-green-500 dark:hs-stepper-completed:bg-green-500">
                                            <span class="hs-stepper-success:hidden hs-stepper-completed:hidden text-xs">2</span>
                                            <svg class="hidden shrink-0 size-3.5 hs-stepper-success:block hs-stepper-completed:block" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M20 6 9 17l-5-5"></path>
                                            </svg>
                                        </span>
                                    </div>
                                    <!-- End Icon -->
                                    <!-- Right Content -->
                                    <div class="-mt-1 grow pb-5 group-last:pb-0">
                                        <span class="text-sm font-medium text-gray-800 dark:text-neutral-200">
                                            Condizioni
                                        </span>
                                        <p class="text-xs text-gray-500 dark:text-neutral-400 mt-1">
                                            Criteri di applicazione
                                        </p>
                                    </div>
                                    <!-- End Right Content -->
                                </div>
                                <!-- End Item -->
                                
                                <!-- Item 3: Canali di vendita -->
                                <div class="group relative flex gap-x-3.5" data-hs-stepper-nav-item='{"index": 3}'>
                                    <!-- Icon -->
                                    <div class="relative group-last:after:hidden after:absolute after:top-6 after:bottom-1 after:start-2.5 after:w-px after:-translate-x-[0.5px] after:bg-gray-200 dark:after:bg-neutral-700 hs-stepper-success:after:bg-green-600 hs-stepper-completed:after:bg-green-600">
                                        <span class="size-5 flex justify-center items-center bg-gray-100 text-gray-800 rounded-full hs-stepper-active:bg-gray-800 hs-stepper-active:text-white hs-stepper-success:bg-green-600 hs-stepper-success:text-white hs-stepper-completed:bg-green-600 hs-stepper-completed:text-white dark:bg-neutral-700 dark:text-neutral-200 dark:hs-stepper-active:bg-blue-500 dark:hs-stepper-success:bg-green-500 dark:hs-stepper-completed:bg-green-500">
                                            <span class="hs-stepper-success:hidden hs-stepper-completed:hidden text-xs">3</span>
                                            <svg class="hidden shrink-0 size-3.5 hs-stepper-success:block hs-stepper-completed:block" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M20 6 9 17l-5-5"></path>
                                            </svg>
                                        </span>
                                    </div>
                                    <!-- End Icon -->
                                    <!-- Right Content -->
                                    <div class="-mt-1 grow pb-5 group-last:pb-0">
                                        <span class="text-sm font-medium text-gray-800 dark:text-neutral-200">
                                            Canali di vendita
                                        </span>
                                        <p class="text-xs text-gray-500 dark:text-neutral-400 mt-1">
                                            Selezione canali
                                        </p>
                                    </div>
                                    <!-- End Right Content -->
                                </div>
                                <!-- End Item -->
                                
                                <!-- Item 4: Applicazione garanzie -->
                                <div class="group relative flex gap-x-3.5" data-hs-stepper-nav-item='{"index": 4}'>
                                    <!-- Icon -->
                                    <div class="relative group-last:after:hidden after:absolute after:top-6 after:bottom-1 after:start-2.5 after:w-px after:-translate-x-[0.5px] after:bg-gray-200 dark:after:bg-neutral-700 hs-stepper-success:after:bg-green-600 hs-stepper-completed:after:bg-green-600">
                                        <span class="size-5 flex justify-center items-center bg-gray-100 text-gray-800 rounded-full hs-stepper-active:bg-gray-800 hs-stepper-active:text-white hs-stepper-success:bg-green-600 hs-stepper-success:text-white hs-stepper-completed:bg-green-600 hs-stepper-completed:text-white dark:bg-neutral-700 dark:text-neutral-200 dark:hs-stepper-active:bg-blue-500 dark:hs-stepper-success:bg-green-500 dark:hs-stepper-completed:bg-green-500">
                                            <span class="hs-stepper-success:hidden hs-stepper-completed:hidden text-xs">4</span>
                                            <svg class="hidden shrink-0 size-3.5 hs-stepper-success:block hs-stepper-completed:block" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M20 6 9 17l-5-5"></path>
                                            </svg>
                                        </span>
                                    </div>
                                    <!-- End Icon -->
                                    <!-- Right Content -->
                                    <div class="-mt-1 grow pb-5 group-last:pb-0">
                                        <span class="text-sm font-medium text-gray-800 dark:text-neutral-200">
                                            Applicazione garanzie
                                        </span>
                                        <p class="text-xs text-gray-500 dark:text-neutral-400 mt-1">
                                            Gestione garanzie
                                        </p>
                                    </div>
                                    <!-- End Right Content -->
                                </div>
                                <!-- End Item -->
                                
                                <!-- Item 5: Riepilogo -->
                                <div class="group relative flex gap-x-3.5" data-hs-stepper-nav-item='{"index": 5}'>
                                    <!-- Icon -->
                                    <div class="relative group-last:after:hidden after:absolute after:top-6 after:bottom-1 after:start-2.5 after:w-px after:-translate-x-[0.5px] after:bg-gray-200 dark:after:bg-neutral-700 hs-stepper-success:after:bg-green-600 hs-stepper-completed:after:bg-green-600">
                                        <span class="size-5 flex justify-center items-center border border-dashed border-gray-400 text-gray-800 rounded-full hs-stepper-active:bg-gray-800 hs-stepper-active:text-white hs-stepper-active:border-transparent hs-stepper-success:bg-green-600 hs-stepper-success:text-white hs-stepper-success:border-transparent hs-stepper-completed:bg-green-600 hs-stepper-completed:text-white hs-stepper-completed:border-transparent dark:text-neutral-200 dark:hs-stepper-active:bg-blue-500 dark:hs-stepper-success:bg-green-500 dark:hs-stepper-completed:bg-green-500">
                                            <span class="hs-stepper-success:hidden hs-stepper-completed:hidden text-xs">5</span>
                                            <svg class="hidden shrink-0 size-3.5 hs-stepper-success:block hs-stepper-completed:block" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M20 6 9 17l-5-5"></path>
                                            </svg>
                                        </span>
                                    </div>
                                    <!-- End Icon -->
                                    <!-- Right Content -->
                                    <div class="-mt-1 grow pb-5 group-last:pb-0">
                                        <span class="text-sm font-medium text-gray-800 dark:text-neutral-200">
                                            Riepilogo
                                        </span>
                                        <p class="text-xs text-gray-500 dark:text-neutral-400 mt-1">
                                            Conferma e finalizzazione
                                        </p>
                                    </div>
                                    <!-- End Right Content -->
                                </div>
                                <!-- End Item -->
                            </div>
                            <!-- Info Footer -->
                            <div class="mt-auto pt-6 border-t border-gray-200 dark:border-neutral-700">
                                <div class="flex items-start gap-3">
                                    <span class="flex-shrink-0 size-6 flex justify-center items-center rounded-full bg-blue-100 text-blue-600 dark:bg-blue-800/30 dark:text-blue-400">
                                        <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <circle cx="12" cy="12" r="10"/>
                                            <path d="M12 16v-4"/>
                                            <path d="M12 8h.01"/>
                                        </svg>
                                    </span>
                                    <p class="text-xs text-gray-500 dark:text-neutral-400">
                                        Puoi modificare queste impostazioni in qualsiasi momento durante la configurazione.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="md:col-span-8 p-6 md:border-s border-gray-200 dark:border-neutral-700">
                        <!-- Stepper Content -->
                        <div>
                            <!-- Content Step 1: Informazioni -->
                            <div data-hs-stepper-content-item='{"index": 1}'>                                
                                <div class="space-y-5">                                    
                                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Informazioni</h3>
                                    <!-- Titolo regola -->
                                    <div>
                                        <label for="ruleTitle" class="block text-sm font-medium mb-2 text-gray-700 dark:text-neutral-200">
                                            Titolo regola <span class="text-red-500">*</span>
                                        </label>
                                        <input type="text" id="ruleTitle" name="ruleTitle" required class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-stone-800 focus:ring-stone-800 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Inserisci titolo">
                                    </div>
                                    
                                   
                                    
                                </div>
                            </div>
                            <!-- End Content Step 1 -->

                            <!-- Content Step 2: Condizioni -->
                            <div data-hs-stepper-content-item='{"index": 2}' style="display: none;">
                                <div class="space-y-5">                                    
                                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Informazioni</h3>
                                     <!-- Content Step 2: Condizioni -->
<div data-hs-stepper-content-item='{"index": 2}' style="display: none;">
    <div class="p-6 bg-white border border-gray-200 rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-6">
            Condizioni di Applicazione
        </h3>
        
        <!-- Condizioni Gruppo 1 -->
        <div class="space-y-4 mb-6">
            <h4 class="text-sm font-medium text-gray-700 dark:text-neutral-300">Gruppo condizioni</h4>
            
            <!-- Input Group per le condizioni -->
            <div id="hs-conditions-wrapper" class="space-y-3">
                <div id="hs-condition-template" class="grid grid-cols-12 gap-3 items-end">
                    <!-- Campo -->
                    <div class="col-span-3">
                        <label class="block text-xs font-medium text-gray-600 dark:text-neutral-400 mb-1">Campo</label>
                        <select class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-gray-800 focus:ring-gray-800 dark:bg-neutral-700 dark:border-neutral-600 dark:text-white">
                            <option>Prezzo prodotto</option>
                            <option>Categoria</option>
                            <option>Quantità</option>
                        </select>
                    </div>
                    
                    <!-- Operatore -->
                    <div class="col-span-2">
                        <label class="block text-xs font-medium text-gray-600 dark:text-neutral-400 mb-1">Operatore</label>
                        <select class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-gray-800 focus:ring-gray-800 dark:bg-neutral-700 dark:border-neutral-600 dark:text-white">
                            <option>></option>
                            <option><</option>
                            <option>=</option>
                            <option>in</option>
                            <option>!=</option>
                        </select>
                    </div>
                    
                    <!-- Valore -->
                    <div class="col-span-4">
                        <label class="block text-xs font-medium text-gray-600 dark:text-neutral-400 mb-1">Valore</label>
                        <input type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-gray-800 focus:ring-gray-800 dark:bg-neutral-700 dark:border-neutral-600 dark:text-white" placeholder="Inserisci valore">
                    </div>
                    
                    <!-- Operatore logico -->
                    <div class="col-span-2">
                        <label class="block text-xs font-medium text-gray-600 dark:text-neutral-400 mb-1">Logica</label>
                        <select class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-gray-800 focus:ring-gray-800 dark:bg-neutral-700 dark:border-neutral-600 dark:text-white">
                            <option>AND</option>
                            <option>OR</option>
                        </select>
                    </div>
                    
                    <!-- Pulsante rimuovi (nascosto per il primo) -->
                    <div class="col-span-1 hidden remove-condition">
                        <button type="button" class="py-2 px-2 inline-flex justify-center items-center text-sm font-medium rounded-lg border border-red-200 bg-white text-red-600 hover:bg-red-50 focus:outline-none focus:bg-red-50 dark:bg-neutral-800 dark:border-red-600 dark:hover:bg-red-900/20" onclick="removeCondition(this)">
                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Pulsante aggiungi condizione -->
            <p class="text-end">
                <button type="button" 
                    data-hs-copy-markup='{
                        "targetSelector": "#hs-condition-template",
                        "wrapperSelector": "#hs-conditions-wrapper",
                        "limit": 5
                    }' 
                    class="py-1.5 px-3 inline-flex items-center gap-x-2 text-xs font-medium rounded-full border border-dashed border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 dark:bg-neutral-800 dark:border-neutral-600 dark:text-neutral-300 dark:hover:bg-neutral-700">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M5 12h14"></path>
                        <path d="M12 5v14"></path>
                    </svg>
                    Aggiungi condizione
                </button>
            </p>
        </div>
        
        <!-- Separatore OR -->
        <div class="relative flex py-5 items-center">
            <div class="flex-grow border-t border-gray-300 dark:border-neutral-600"></div>
            <span class="flex-shrink mx-4 px-3 py-1 text-xs font-medium bg-gray-100 text-gray-600 rounded-full dark:bg-neutral-700 dark:text-neutral-400">OR</span>
            <div class="flex-grow border-t border-gray-300 dark:border-neutral-600"></div>
        </div>
        
        <!-- Condizioni Gruppo 2 -->
        <div class="space-y-4">
            <h4 class="text-sm font-medium text-gray-700 dark:text-neutral-300">Gruppo condizioni alternativo</h4>
            
            <!-- Input Group per il secondo gruppo -->
            <div id="hs-conditions-wrapper-2" class="space-y-3">
                <div id="hs-condition-template-2" class="grid grid-cols-12 gap-3 items-end">
                    <!-- Campo -->
                    <div class="col-span-3">
                        <label class="block text-xs font-medium text-gray-600 dark:text-neutral-400 mb-1">Campo</label>
                        <select class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-gray-800 focus:ring-gray-800 dark:bg-neutral-700 dark:border-neutral-600 dark:text-white">
                            <option>Prezzo prodotto</option>
                            <option>Categoria</option>
                            <option>Quantità</option>
                        </select>
                    </div>
                    
                    <!-- Operatore -->
                    <div class="col-span-2">
                        <label class="block text-xs font-medium text-gray-600 dark:text-neutral-400 mb-1">Operatore</label>
                        <select class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-gray-800 focus:ring-gray-800 dark:bg-neutral-700 dark:border-neutral-600 dark:text-white">
                            <option>></option>
                            <option><</option>
                            <option>=</option>
                            <option>in</option>
                            <option>!=</option>
                        </select>
                    </div>
                    
                    <!-- Valore -->
                    <div class="col-span-4">
                        <label class="block text-xs font-medium text-gray-600 dark:text-neutral-400 mb-1">Valore</label>
                        <input type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-gray-800 focus:ring-gray-800 dark:bg-neutral-700 dark:border-neutral-600 dark:text-white" placeholder="Inserisci valore">
                    </div>
                    
                    <!-- Operatore logico -->
                    <div class="col-span-2">
                        <label class="block text-xs font-medium text-gray-600 dark:text-neutral-400 mb-1">Logica</label>
                        <select class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-gray-800 focus:ring-gray-800 dark:bg-neutral-700 dark:border-neutral-600 dark:text-white">
                            <option>AND</option>
                            <option>OR</option>
                        </select>
                    </div>
                    
                    <!-- Pulsante rimuovi -->
                    <div class="col-span-1 hidden remove-condition">
                        <button type="button" class="py-2 px-2 inline-flex justify-center items-center text-sm font-medium rounded-lg border border-red-200 bg-white text-red-600 hover:bg-red-50 focus:outline-none focus:bg-red-50 dark:bg-neutral-800 dark:border-red-600 dark:hover:bg-red-900/20" onclick="removeCondition(this)">
                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Pulsante aggiungi condizione gruppo 2 -->
            <p class="text-end">
                <button type="button" 
                    data-hs-copy-markup='{
                        "targetSelector": "#hs-condition-template-2",
                        "wrapperSelector": "#hs-conditions-wrapper-2",
                        "limit": 5
                    }' 
                    class="py-1.5 px-3 inline-flex items-center gap-x-2 text-xs font-medium rounded-full border border-dashed border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 dark:bg-neutral-800 dark:border-neutral-600 dark:text-neutral-300 dark:hover:bg-neutral-700">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M5 12h14"></path>
                        <path d="M12 5v14"></path>
                    </svg>
                    Aggiungi condizione
                </button>
            </p>
        </div>
    </div>
</div>

<script>
function removeCondition(button) {
    const conditionRow = button.closest('.grid');
    if (conditionRow) {
        conditionRow.remove();
    }
}

// Mostra il pulsante rimuovi quando si aggiungono nuove condizioni
document.addEventListener('DOMContentLoaded', function() {
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === Node.ELEMENT_NODE && node.classList.contains('grid')) {
                        const removeBtn = node.querySelector('.remove-condition');
                        if (removeBtn) {
                            removeBtn.classList.remove('hidden');
                        }
                    }
                });
            }
        });
    });
    
    observer.observe(document.getElementById('hs-conditions-wrapper'), { childList: true });
    observer.observe(document.getElementById('hs-conditions-wrapper-2'), { childList: true });
});
</script>
                            <!-- End Content Step 2 -->
                                    
                                </div>
                            </div>
                            <!-- End Content Step 2 -->

                            <!-- Content Step 3: Canali di vendita -->
                            <div data-hs-stepper-content-item='{"index": 3}' style="display: none;">
                                <div class="space-y-5">                                    
                                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Informazioni</h3>
                                    <!-- Titolo regola -->
                                    <div>
                                        <label for="ruleTitle" class="block text-sm font-medium mb-2 text-gray-700 dark:text-neutral-200">
                                            Titolo regola <span class="text-red-500">*</span>
                                        </label>
                                        <input type="text" id="ruleTitle" name="ruleTitle" required class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-stone-800 focus:ring-stone-800 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Inserisci titolo">
                                    </div>
                                    
                                </div>
                            </div>
                            <!-- End Content Step 3 -->

                            <!-- Content Step 4: Applicazione garanzie -->
                            <div data-hs-stepper-content-item='{"index": 4}' style="display: none;">
                                <div class="space-y-5">                                    
                                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Informazioni</h3>
                                    <!-- Titolo regola -->
                                    <div>
                                        <label for="ruleTitle" class="block text-sm font-medium mb-2 text-gray-700 dark:text-neutral-200">
                                            Titolo regola <span class="text-red-500">*</span>
                                        </label>
                                        <input type="text" id="ruleTitle" name="ruleTitle" required class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-stone-800 focus:ring-stone-800 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Inserisci titolo">
                                    </div>
                                    
                                </div>
                            </div>
                            <!-- End Content Step 4 -->

                            <!-- Content Step 5: Riepilogo -->
                            <div data-hs-stepper-content-item='{"index": 5}' style="display: none;">
                                <div class="p-6 bg-white border border-gray-200 rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
                                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                                        Riepilogo Configurazione
                                    </h3>
                                    <div class="space-y-4">
                                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 dark:bg-blue-900/20 dark:border-blue-800">
                                            <h4 class="font-medium text-blue-800 dark:text-blue-200 mb-2">
                                                Regola configurata con successo
                                            </h4>
                                            <p class="text-sm text-blue-600 dark:text-blue-300">
                                                Tutti i passaggi sono stati completati. Verifica i dettagli prima di salvare.
                                            </p>
                                        </div>
                                        <div class="space-y-3">
                                            <div class="flex justify-between py-2 border-b border-gray-200 dark:border-neutral-700">
                                                <span class="text-sm font-medium text-gray-600 dark:text-neutral-400">Nome regola:</span>
                                                <span class="text-sm text-gray-900 dark:text-white">Regola Esempio</span>
                                            </div>
                                            <div class="flex justify-between py-2 border-b border-gray-200 dark:border-neutral-700">
                                                <span class="text-sm font-medium text-gray-600 dark:text-neutral-400">Priorità:</span>
                                                <span class="text-sm text-gray-900 dark:text-white">Alta</span>
                                            </div>
                                            <div class="flex justify-between py-2 border-b border-gray-200 dark:border-neutral-700">
                                                <span class="text-sm font-medium text-gray-600 dark:text-neutral-400">Canali selezionati:</span>
                                                <span class="text-sm text-gray-900 dark:text-white">E-commerce, App Mobile</span>
                                            </div>
                                            <div class="flex justify-between py-2 border-b border-gray-200 dark:border-neutral-700">
                                                <span class="text-sm font-medium text-gray-600 dark:text-neutral-400">Tipo garanzia:</span>
                                                <span class="text-sm text-gray-900 dark:text-white">Garanzia estesa (36 mesi)</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- End Content Step 5 -->

                            <!-- Final Content -->
                            <div data-hs-stepper-content-item='{"isFinal": true}' style="display: none;">
                                <div class="p-6 bg-green-50 border border-green-200 rounded-xl dark:bg-green-900/20 dark:border-green-800">
                                    <div class="flex items-center justify-center mb-4">
                                        <div class="size-12 bg-green-100 rounded-full flex items-center justify-center dark:bg-green-800/30">
                                            <svg class="size-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <h3 class="text-lg font-semibold text-green-800 dark:text-green-200 text-center mb-2">
                                        Regola creata con successo!
                                    </h3>
                                    <p class="text-sm text-green-600 dark:text-green-300 text-center">
                                        La tua nuova regola di pricing è stata configurata e salvata correttamente.
                                    </p>
                                </div>
                            </div>
                            <!-- End Final Content -->

                            <!-- Button Group -->
                            <div class="mt-5 flex justify-between items-center gap-x-2">
                                <button type="button" class="py-2 px-3 inline-flex items-center gap-x-1 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700" data-hs-stepper-back-btn="">
                                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="m15 18-6-6 6-6"></path>
                                    </svg>
                                    Indietro
                                </button>
                                <button type="button" class="py-2 px-3 inline-flex items-center gap-x-1 text-sm font-medium rounded-lg border border-transparent bg-gray-800 text-white hover:bg-gray-950 focus:outline-none focus:bg-gray-950 disabled:opacity-50 disabled:pointer-events-none" data-hs-stepper-next-btn="">
                                    Avanti
                                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="m9 18 6-6-6-6"></path>
                                    </svg>
                                </button>
                                <button type="button" class="py-2 px-3 inline-flex items-center gap-x-1 text-sm font-medium rounded-lg border border-transparent bg-green-600 text-white hover:bg-green-700 focus:outline-none focus:bg-green-700 disabled:opacity-50 disabled:pointer-events-none" data-hs-stepper-finish-btn="" style="display: none;">
                                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M20 6 9 17l-5-5"></path>
                                    </svg>
                                    Salva Regola
                                </button>
                                <button type="reset" class="py-2 px-3 inline-flex items-center gap-x-1 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700" data-hs-stepper-reset-btn="" style="display: none;">
                                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
                                        <path d="M3 3v5h5"></path>
                                    </svg>
                                    Ricomincia
                                </button>
                            </div>
                            <!-- End Button Group -->
                        </div>
                        <!-- End Stepper Content -->
                    </div>                  
                </div>        
            </div>
        </div>
    </div>
</div>