<!-- City Form Content for Offcanvas -->
<script class="reload-script-on-load">
    addRoute('BE_CITY', '{{ routes("BE_CITY") }}');
    addRoute('BE_CITY_SAVE', '{{ routes("BE_CITY_SAVE") }}');
    addRoute('BE_CITY_OPERATE', '{{ routes("BE_CITY_OPERATE") }}');
</script>

<!-- Form Content with Sticky Footer -->
<div class="flex flex-col h-full">
    <!-- Scrollable Content Area -->
    <div class="flex-1 overflow-y-auto">
        <div class="mt-3">
            {% set postUrl = routes('BE_CITY_SAVE') %}
            {% if curCity.id is not empty %}
            {% set postUrl = routes('BE_CITY_SAVE') + '?cityId=' + curCity.id %}
            {% endif %}

            <form id="city-edit-offcanvas" method="POST" action="{{ postUrl }}" enctype="multipart/form-data" class="form-validate-jquery mt-5">
                <!-- Hidden ID field -->
                <input type="hidden" name="id" value="{{ curCity.id }}">

                <!-- Name -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Nome: <span class="text-red-500">*</span>
                    </label>
                    <input name="name" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600 maxlength" placeholder="Nome della città" value="{{ curCity.name }}" required maxlength="100" {% if not user.hasPermission('CITY_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Codice ISTAT -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Codice ISTAT:
                    </label>
                    <input name="codiceIstat" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600 maxlength" placeholder="Codice ISTAT" value="{{ curCity.codiceIstat }}" maxlength="20" {% if not user.hasPermission('CITY_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Codice Belfiore -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Codice Belfiore:
                    </label>
                    <input name="codiceBelfiore" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600 maxlength" placeholder="Codice Belfiore" value="{{ curCity.codiceBelfiore }}" maxlength="10" {% if not user.hasPermission('CITY_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- CAP -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        CAP:
                    </label>
                    <input name="cap" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600 maxlength" placeholder="Codice Avviamento Postale" value="{{ curCity.cap }}" maxlength="10" {% if not user.hasPermission('CITY_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Province Code -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Codice Provincia:
                    </label>
                    <input name="provinceCode" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600 maxlength" placeholder="Sigla provincia (es. MI, RM, TO)" value="{{ curCity.provinceCode }}" maxlength="5" {% if not user.hasPermission('CITY_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Province -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Provincia:
                    </label>
                    <input name="province" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600 maxlength" placeholder="Nome della provincia" value="{{ curCity.province }}" maxlength="100" {% if not user.hasPermission('CITY_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Region -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Regione:
                    </label>
                    <input name="region" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600 maxlength" placeholder="Nome della regione" value="{{ curCity.region }}" maxlength="100" {% if not user.hasPermission('CITY_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Country Code -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Codice Paese:
                    </label>
                    <input name="countryCode" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600 maxlength" placeholder="Codice paese (es. IT)" value="{{ curCity.countryCode }}" maxlength="5" {% if not user.hasPermission('CITY_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

            </form>
        </div>
    </div>

    <!-- Sticky Footer -->
    <div class="flex-shrink-0 border-t border-gray-200 bg-white px-4 pt-4 dark:border-neutral-700 dark:bg-neutral-900">
        <div class="flex justify-end gap-x-2">
            {% if curCity is empty %}
                <!-- New City - Show Save Button -->
                {% if user.hasPermission('CITY_MANAGEMENT', 'create') %}
                <button type="submit" form="city-edit-offcanvas" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                    Salva Città
                </button>
                {% else %}
                <div class="text-sm text-gray-500 dark:text-neutral-400 py-2 px-3">
                    Non hai i permessi per creare città
                </div>
                {% endif %}
            {% else %}
                <!-- Edit City - Show Update and Delete Buttons -->
                {% if user.hasPermission('CITY_MANAGEMENT', 'delete') %}
                <button type="button" data-cityid="{{ curCity.id }}" id="delete-city-btn" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-red-600 hover:bg-red-50 focus:outline-none focus:bg-red-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-red-500 dark:hover:bg-red-800/30 dark:focus:bg-red-800/30">
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M3 6h18"/>
                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
                        <line x1="10" x2="10" y1="11" y2="17"/>
                        <line x1="14" x2="14" y1="11" y2="17"/>
                    </svg>
                    Elimina
                </button>
                {% endif %}
                {% if user.hasPermission('CITY_MANAGEMENT', 'edit') %}
                <button type="submit" form="city-edit-offcanvas" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                    Modifica Città
                </button>
                {% else %}
                <div class="text-sm text-gray-500 dark:text-neutral-400 py-2 px-3">
                    Non hai i permessi per modificare città
                </div>
                {% endif %}
            {% endif %}
        </div>
    </div>
</div>
