<!-- Product Card -->
<div class="p-5 md:p-8 bg-white border border-gray-200 shadow-2xs rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
    <!-- Title -->
    <div class="mb-4 xl:mb-8">
        <h1 class="text-lg font-semibold text-gray-800 dark:text-neutral-200">
            Canali di Vendita
        </h1>
        <p class="text-sm text-gray-500 dark:text-neutral-500">
            Configura i canali dove questo prodotto sarà disponibile per la vendita
        </p>
    </div>
    <!-- End Title -->
    <!-- Form -->
    <form>
        <!-- Logo -->
        <div class="py-6 sm:py-8 space-y-5 border-t border-gray-200 first:border-t-0 dark:border-neutral-700">
            <!-- Grid -->
            <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                <div class="sm:col-span-4 xl:col-span-3 2xl:col-span-2">
                    <label class="sm:mt-2.5 inline-block text-sm text-gray-500 dark:text-neutral-500">
                        Sito ufficiale
                    </label>
                </div>
                <!-- End Col -->
                <div class="sm:col-span-8 xl:col-span-8">

                    <!-- Grid -->
                    <div class="flex flex-col divide-y divide-gray-200 dark:divide-neutral-700">
                        <div class="divide-y divide-gray-200 dark:divide-neutral-700">
                            <!-- Card -->
                            <div class="py-3 flex flex-col">
                                <div class="flex justify-between items-center gap-3">
                                    
                                    <span class="mt-1 flex shrink-0 justify-center items-center size-9.5 bg-white border border-gray-200 rounded-lg dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">                                                                                                                    
                                        <svg class="shrink-0 size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-earth-icon lucide-earth"><path d="M21.54 15H17a2 2 0 0 0-2 2v4.54"/><path d="M7 3.34V5a3 3 0 0 0 3 3a2 2 0 0 1 2 2c0 1.1.9 2 2 2a2 2 0 0 0 2-2c0-1.1.9-2 2-2h3.17"/><path d="M11 21.95V18a2 2 0 0 0-2-2a2 2 0 0 1-2-2v-1a2 2 0 0 0-2-2H2.05"/><circle cx="12" cy="12" r="10"/></svg>
                                    </span>

                                    <div class="grow">
                                        <span class="font-medium text-gray-800 dark:text-neutral-200">
                                            Sito ufficiale
                                        </span>
                                        <p class="text-xs text-gray-500 dark:text-neutral-500">
                                            www.tigani.it
                                        </p>
                                    </div>

                                    <div>
                                        <label for="website-official" class="relative cursor-pointer font-medium text-xs text-gray-800 dark:text-neutral-200">

                                            <input type="checkbox" id="website-official" class="peer hidden" checked>

                                                <!-- Connect Icon (shown when not connected) -->
                                                <span class="relative z-10 size-7 flex shrink-0 justify-center items-center bg-gray-100 text-gray-800 rounded-full peer-checked:hidden peer-checked:bg-gray-800 peer-checked:text-white dark:bg-neutral-700 dark:text-neutral-200 dark:peer-checked:bg-neutral-200 dark:peer-checked:text-neutral-800">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
                                                    <span class="sr-only">Connect</span>
                                                </span>

                                                <!-- Connected Icon (shown when checked) -->
                                                <span class="relative z-10 size-7 hidden shrink-0 justify-center items-center bg-gray-100 text-gray-800 rounded-full peer-checked:flex peer-checked:bg-gray-800 peer-checked:text-white dark:bg-neutral-700 dark:text-neutral-200 dark:peer-checked:bg-neutral-200 dark:peer-checked:text-neutral-800">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 6 9 17l-5-5"/></svg>
                                                    <span class="sr-only">Connected</span>
                                                </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <!-- End Card -->

                            
                        </div>
                        <!-- End Col -->
                    </div>
                    <!-- End Grid -->

                </div>
                <!-- End Col -->
            </div>
            <!-- End Grid -->
        </div>
        <div class="py-6 sm:py-8 space-y-5 border-t border-gray-200 first:border-t-0 dark:border-neutral-700">
            <!-- Grid -->
            <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                <div class="sm:col-span-4 xl:col-span-3 2xl:col-span-2">
                    <label class="sm:mt-2.5 inline-block text-sm text-gray-500 dark:text-neutral-500">
                        Siti partner
                    </label>
                </div>
                <!-- End Col -->
                <div class="sm:col-span-8 xl:col-span-8">

                    <!-- Grid -->
                    <div class="flex flex-col divide-y divide-gray-200 dark:divide-neutral-700">
                        <div class="divide-y divide-gray-200 dark:divide-neutral-700">
                            <!-- Card -->
                            <div class="py-3 flex flex-col">
                                <div class="flex justify-between items-center gap-3">
                                    <span class="mt-1 flex shrink-0 justify-center items-center size-9.5 bg-white border border-gray-200 rounded-lg dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">                                    
                                        <img src="/tigani/img/brands/harley.svg" class="shrink-0 size-5" width="32" height="32">
                                    </span>

                                    <div class="grow">
                                        <span class="font-medium text-gray-800 dark:text-neutral-200">
                                            Assicuriamo la tua Passione
                                        </span>
                                        <p class="text-xs text-gray-500 dark:text-neutral-500">                                            
                                            quote.assicuriamolatuapassione.it
                                        </p>
                                    </div>                                    

                                    <div>
                                        <label for="website-partner-1" class="relative cursor-pointer font-medium text-xs text-gray-800 dark:text-neutral-200">

                                            <input type="checkbox" id="website-partner-1" class="peer hidden" checked>

                                                <!-- Connect Icon (shown when not connected) -->
                                                <span class="relative z-10 size-7 flex shrink-0 justify-center items-center bg-gray-100 text-gray-800 rounded-full peer-checked:hidden peer-checked:bg-gray-800 peer-checked:text-white dark:bg-neutral-700 dark:text-neutral-200 dark:peer-checked:bg-neutral-200 dark:peer-checked:text-neutral-800">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
                                                    <span class="sr-only">Connect</span>
                                                </span>

                                                <!-- Connected Icon (shown when checked) -->
                                                <span class="relative z-10 size-7 hidden shrink-0 justify-center items-center bg-gray-100 text-gray-800 rounded-full peer-checked:flex peer-checked:bg-gray-800 peer-checked:text-white dark:bg-neutral-700 dark:text-neutral-200 dark:peer-checked:bg-neutral-200 dark:peer-checked:text-neutral-800">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 6 9 17l-5-5"/></svg>
                                                    <span class="sr-only">Connected</span>
                                                </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <!-- End Card -->

                            <!-- Card -->
                            <div class="py-3 flex flex-col">
                                <div class="flex justify-between items-center gap-3">
                                    <span class="mt-1 flex shrink-0 justify-center items-center size-9.5 bg-white border border-gray-200 rounded-lg dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">                                    
                                        <img src="/tigani/img/brands/triumph.svg" class="shrink-0 size-5" width="32" height="32">
                                    </span>

                                    <div class="grow">
                                        <span class="font-medium text-gray-800 dark:text-neutral-200">
                                            Triumph Easy
                                        </span>
                                        <p class="text-xs text-gray-500 dark:text-neutral-500">                                            
                                            quote.triumpheasy.it
                                        </p>
                                    </div>

                                    <div>
                                        <label for="hs-pro-sufitnt" class="relative cursor-pointer font-medium text-xs text-gray-800 dark:text-neutral-200">

                                            <input type="checkbox" id="hs-pro-sufitnt" class="peer hidden" checked>

                                                <!-- Connect Icon (shown when not connected) -->
                                                <span class="relative z-10 size-7 flex shrink-0 justify-center items-center bg-gray-100 text-gray-800 rounded-full peer-checked:hidden peer-checked:bg-gray-800 peer-checked:text-white dark:bg-neutral-700 dark:text-neutral-200 dark:peer-checked:bg-neutral-200 dark:peer-checked:text-neutral-800">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
                                                    <span class="sr-only">Connect</span>
                                                </span>

                                                <!-- Connected Icon (shown when checked) -->
                                                <span class="relative z-10 size-7 hidden shrink-0 justify-center items-center bg-gray-100 text-gray-800 rounded-full peer-checked:flex peer-checked:bg-gray-800 peer-checked:text-white dark:bg-neutral-700 dark:text-neutral-200 dark:peer-checked:bg-neutral-200 dark:peer-checked:text-neutral-800">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 6 9 17l-5-5"/></svg>
                                                    <span class="sr-only">Connected</span>
                                                </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <!-- End Card -->

                            <!-- Card -->
                            <div class="py-3 flex flex-col">
                                <div class="flex justify-between items-center gap-3">
                                    <span class="mt-1 flex shrink-0 justify-center items-center size-9.5 bg-white border border-gray-200 rounded-lg dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">                                    
                                        <img src="/tigani/img/brands/ducati.svg" class="shrink-0 size-5" width="32" height="32">
                                    </span>

                                    <div class="grow">
                                        <span class="font-medium text-gray-800 dark:text-neutral-200">
                                            Ducati Motoprotection
                                        </span>
                                        <p class="text-xs text-gray-500 dark:text-neutral-500">                                            
                                            quote.ducatimotoprotection.it
                                        </p>
                                    </div>

                                    <div>
                                        <label for="hs-pro-sufitgm" class="relative cursor-pointer font-medium text-xs text-gray-800 dark:text-neutral-200">

                                            <input type="checkbox" id="hs-pro-sufitgm" class="peer hidden" >

                                                <!-- Connect Icon (shown when not connected) -->
                                                <span class="relative z-10 size-7 flex shrink-0 justify-center items-center bg-gray-100 text-gray-800 rounded-full peer-checked:hidden peer-checked:bg-gray-800 peer-checked:text-white dark:bg-neutral-700 dark:text-neutral-200 dark:peer-checked:bg-neutral-200 dark:peer-checked:text-neutral-800">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
                                                    <span class="sr-only">Connect</span>
                                                </span>

                                                <!-- Connected Icon (shown when checked) -->
                                                <span class="relative z-10 size-7 hidden shrink-0 justify-center items-center bg-gray-100 text-gray-800 rounded-full peer-checked:flex peer-checked:bg-gray-800 peer-checked:text-white dark:bg-neutral-700 dark:text-neutral-200 dark:peer-checked:bg-neutral-200 dark:peer-checked:text-neutral-800">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 6 9 17l-5-5"/></svg>
                                                    <span class="sr-only">Connected</span>
                                                </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <!-- End Card -->                        
                        
                            <!-- Card -->
                            <div class="py-3 flex flex-col">
                                <div class="flex justify-between items-center gap-3">
                                    <span class="mt-1 flex shrink-0 justify-center items-center size-9.5 bg-white border border-gray-200 rounded-lg dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">                                    
                                        <img src="/tigani/img/brands/bmw.svg" class="shrink-0 size-5" width="32" height="32">
                                    </span>

                                    <div class="grow">
                                        <span class="font-medium text-gray-800 dark:text-neutral-200">
                                            Motorrad Assicura
                                        </span>
                                        <p class="text-xs text-gray-500 dark:text-neutral-500">                                            
                                            quote.motorradassicura.it
                                        </p>
                                    </div>

                                    <div>
                                        <label for="hs-pro-sufitdb" class="relative cursor-pointer font-medium text-xs text-gray-800 dark:text-neutral-200">

                                            <input type="checkbox" id="hs-pro-sufitdb" class="peer hidden" >

                                                <!-- Connect Icon (shown when not connected) -->
                                                <span class="relative z-10 size-7 flex shrink-0 justify-center items-center bg-gray-100 text-gray-800 rounded-full peer-checked:hidden peer-checked:bg-gray-800 peer-checked:text-white dark:bg-neutral-700 dark:text-neutral-200 dark:peer-checked:bg-neutral-200 dark:peer-checked:text-neutral-800">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
                                                    <span class="sr-only">Connect</span>
                                                </span>

                                                <!-- Connected Icon (shown when checked) -->
                                                <span class="relative z-10 size-7 hidden shrink-0 justify-center items-center bg-gray-100 text-gray-800 rounded-full peer-checked:flex peer-checked:bg-gray-800 peer-checked:text-white dark:bg-neutral-700 dark:text-neutral-200 dark:peer-checked:bg-neutral-200 dark:peer-checked:text-neutral-800">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 6 9 17l-5-5"/></svg>
                                                    <span class="sr-only">Connected</span>
                                                </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <!-- End Card -->

                            <!-- Card -->
                            <div class="py-3 flex flex-col">
                                <div class="flex justify-between items-center gap-3">
                                    <span class="mt-1 flex shrink-0 justify-center items-center size-9.5 bg-white border border-gray-200 rounded-lg dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">                                    
                                        <img src="/tigani/img/brands/motoprotection.svg" class="shrink-0 size-5" width="32" height="32">
                                    </span>

                                    <div class="grow">
                                        <span class="font-medium text-gray-800 dark:text-neutral-200">
                                            Motoprotection
                                        </span>
                                        <p class="text-xs text-gray-500 dark:text-neutral-500">                                            
                                            quote.motoprotection.it
                                        </p>
                                    </div>

                                    <div>
                                        <label for="hs-pro-sufitmc" class="relative cursor-pointer font-medium text-xs text-gray-800 dark:text-neutral-200">

                                            <input type="checkbox" id="hs-pro-sufitmc" class="peer hidden" >

                                                <!-- Connect Icon (shown when not connected) -->
                                                <span class="relative z-10 size-7 flex shrink-0 justify-center items-center bg-gray-100 text-gray-800 rounded-full peer-checked:hidden peer-checked:bg-gray-800 peer-checked:text-white dark:bg-neutral-700 dark:text-neutral-200 dark:peer-checked:bg-neutral-200 dark:peer-checked:text-neutral-800">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
                                                    <span class="sr-only">Connect</span>
                                                </span>

                                                <!-- Connected Icon (shown when checked) -->
                                                <span class="relative z-10 size-7 hidden shrink-0 justify-center items-center bg-gray-100 text-gray-800 rounded-full peer-checked:flex peer-checked:bg-gray-800 peer-checked:text-white dark:bg-neutral-700 dark:text-neutral-200 dark:peer-checked:bg-neutral-200 dark:peer-checked:text-neutral-800">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 6 9 17l-5-5"/></svg>
                                                    <span class="sr-only">Connected</span>
                                                </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <!-- End Card -->
                        </div>
                        <!-- End Col -->
                    </div>
                    <!-- End Grid -->

                </div>
                <!-- End Col -->
            </div>
            <!-- End Grid -->
        </div>
        <div class="py-6 sm:py-8 space-y-5 border-t border-gray-200 first:border-t-0 dark:border-neutral-700">
            <!-- Grid -->
            <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                <div class="sm:col-span-4 xl:col-span-3 2xl:col-span-2">
                    <label class="sm:mt-2.5 inline-block text-sm text-gray-500 dark:text-neutral-500">
                        Rivenditori
                    </label>
                </div>
                <!-- End Col -->
                <div class="sm:col-span-8 xl:col-span-8">

                    <!-- Search Input -->
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 flex items-center pointer-events-none z-20 pl-4">
                            <svg class="shrink-0 size-4 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="11" cy="11" r="8"/>
                                <path d="m21 21-4.35-4.35"/>
                            </svg>
                        </div>
                        <input 
                            type="text" 
                            id="dealerSearch"
                            class="py-1.5 sm:py-2 pl-11 pr-4 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-gray-800 focus:ring-gray-800 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" 
                            placeholder="Cerca rivenditori..."
                            onkeyup="filterDealers(this.value)"
                            >
                            <!-- Clear button -->
                            <div class="absolute inset-y-0 right-0 flex items-center pointer-events-none z-20 pr-4">
                                <button 
                                    type="button" 
                                    class="inline-flex shrink-0 justify-center items-center size-6 rounded-full text-gray-500 hover:text-gray-800 focus:outline-none focus:text-gray-800 dark:text-neutral-500 dark:hover:text-neutral-300 dark:focus:text-neutral-300 hidden"
                                    id="clearSearch"
                                    onclick="clearDealerSearch()"
                                    >
                                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M18 6 6 18"/>
                                        <path d="M6 6l12 12"/>
                                    </svg>
                                </button>
                            </div>
                    </div>

                    
                    <div class="divide-y divide-dashed divide-gray-200 dark:divide-neutral-700">
                    
                        <div class="max-h-100 overflow-y-auto
                             [&::-webkit-scrollbar]:w-2
                             [&::-webkit-scrollbar-track]:rounded-full
                             [&::-webkit-scrollbar-track]:bg-gray-100
                             [&::-webkit-scrollbar-thumb]:rounded-full
                             [&::-webkit-scrollbar-thumb]:bg-gray-300
                             dark:[&::-webkit-scrollbar-track]:bg-neutral-700
                             dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
  

                        <!-- Grid -->
                        <div class="flex flex-col divide-y divide-dashed divide-gray-200 dark:divide-neutral-700">
                            <div class="divide-y divide-dashed divide-gray-200 dark:divide-neutral-700">
                                <!-- Card -->
                                <div class="py-3 flex flex-col">
                                    <div class="flex justify-between items-center gap-3">
                                        <!-- HD Logo -->                                                                        
                                        <span class="mt-1 flex shrink-0 justify-center items-center size-9.5 bg-white border border-gray-200 rounded-lg dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">                                                                            
                                            <svg class="shrink-0 size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-store-icon lucide-store"><path d="M15 21v-5a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v5"/><path d="M17.774 10.31a1.12 1.12 0 0 0-1.549 0 2.5 2.5 0 0 1-3.451 0 1.12 1.12 0 0 0-1.548 0 2.5 2.5 0 0 1-3.452 0 1.12 1.12 0 0 0-1.549 0 2.5 2.5 0 0 1-3.77-3.248l2.889-4.184A2 2 0 0 1 7 2h10a2 2 0 0 1 1.653.873l2.895 4.192a2.5 2.5 0 0 1-3.774 3.244"/><path d="M4 10.95V19a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8.05"/></svg>
                                        </span>

                                        <!-- Clickable Title -->
                                        <div class="grow cursor-pointer" onclick="toggleAccordion()">                                        
                                            <span class="font-medium text-gray-800 dark:text-neutral-200">
                                                HD Bergamo
                                            </span>
                                            <p class="text-xs text-gray-500 dark:text-neutral-500">                                            
                                                HD-00023
                                            </p>                                    
                                        </div>

                                        <!-- Status Icons -->
                                        <div>
                                            <input type="checkbox" id="hs-pro-sufitsl" class="hidden">

                                                <!-- No Selection Icon -->
                                                <span
                                                    class="no-selection hidden relative z-10 size-7 shrink-0 justify-center items-center bg-gray-100 text-gray-800 rounded-full dark:bg-neutral-700 dark:text-neutral-200">            
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus-icon lucide-plus"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
                                                    <span class="sr-only">No Selection</span>
                                                </span>

                                                <!-- Single Check Icon -->
                                                <span
                                                    class="single-check hidden relative z-10 size-7 shrink-0 justify-center items-center bg-gray-800 text-white rounded-full dark:bg-neutral-200 dark:text-neutral-800">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-icon lucide-check"><path d="M20 6 9 17l-5-5"/></svg>
                                                    <span class="sr-only">Partial Selection</span>
                                                </span>

                                                <!-- Double Check Icon -->
                                                <span
                                                    class="double-check hidden relative z-10 size-7 shrink-0 justify-center items-center bg-gray-800 text-white rounded-full dark:bg-neutral-200 dark:text-neutral-800">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-check-icon lucide-check-check"><path d="M18 6 7 17l-5-5"/><path d="m22 10-7.5 7.5L13 16"/></svg>
                                                    <span class="sr-only">All Selected</span>
                                                </span>
                                        </div>
                                    </div>

                                    <!-- Accordion Content -->
                                    <div id="accordionContent" class="overflow-hidden transition-all duration-300 max-h-0">
                                        <div class="mt-3 p-3 bg-gray-50 dark:bg-neutral-700 rounded-lg">
                                            <div class="space-y-2">
                                                <!-- Person 1 - Marco Rossi (Selected) -->
                                                <div
                                                    class="flex items-center gap-3 p-2 hover:bg-gray-100 dark:hover:bg-neutral-600 rounded-lg cursor-pointer"
                                                    onclick="togglePerson(this, 'Marco Rossi')">
                                                    <div class="shrink-0">
                                                        <img class="size-6 rounded-full"
                                                             src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=facearea&facepad=3&w=320&h=320&q=80"
                                                             alt="Marco Rossi">
                                                    </div>
                                                    <div class="grow">
                                                        <div class="text-sm text-gray-800 dark:text-neutral-200">Marco Rossi</div>
                                                        <div class="text-xs text-gray-500 dark:text-neutral-400"><EMAIL></div>
                                                    </div>
                                                    <div
                                                        class="person-check flex justify-center items-center size-5 bg-gray-800 border border-white text-white rounded-full dark:bg-neutral-200 dark:text-neutral-800">
                                                        <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                                             stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M20 6 9 17l-5-5" />
                                                        </svg>
                                                    </div>
                                                </div>

                                                <!-- Person 2 - Giulia Bianchi (Not Selected) -->
                                                <div
                                                    class="flex items-center gap-3 p-2 hover:bg-gray-100 dark:hover:bg-neutral-600 rounded-lg cursor-pointer"
                                                    onclick="togglePerson(this, 'Giulia Bianchi')">
                                                    <div class="shrink-0">
                                                        <div
                                                            class="size-6 flex justify-center items-center bg-gray-200 text-gray-600 text-xs font-semibold rounded-full dark:bg-neutral-600 dark:text-neutral-300">
                                                            G
                                                        </div>
                                                    </div>
                                                    <div class="grow">
                                                        <div class="text-sm text-gray-800 dark:text-neutral-200">Giulia Bianchi</div>
                                                        <div class="text-xs text-gray-500 dark:text-neutral-400"><EMAIL></div>
                                                    </div>
                                                    <div
                                                        class="person-check hidden justify-center items-center size-5 bg-gray-400 border border-white text-white rounded-full">
                                                        <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                                             stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M20 6 9 17l-5-5" />
                                                        </svg>
                                                    </div>
                                                </div>

                                                <!-- Person 3 - Andrea Verdi (Selected) -->
                                                <div
                                                    class="flex items-center gap-3 p-2 hover:bg-gray-100 dark:hover:bg-neutral-600 rounded-lg cursor-pointer"
                                                    onclick="togglePerson(this, 'Andrea Verdi')">
                                                    <div class="shrink-0">
                                                        <div
                                                            class="size-6 flex justify-center items-center bg-gray-200 text-gray-600 text-xs font-semibold rounded-full dark:bg-neutral-600 dark:text-neutral-300">
                                                            A
                                                        </div>
                                                    </div>
                                                    <div class="grow">
                                                        <div class="text-sm text-gray-800 dark:text-neutral-200">Andrea Verdi</div>
                                                        <div class="text-xs text-gray-500 dark:text-neutral-400"><EMAIL></div>
                                                    </div>
                                                    <div
                                                        class="person-check flex justify-center items-center size-5 bg-gray-800 border border-white text-white rounded-full dark:bg-neutral-200 dark:text-neutral-800">
                                                        <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                                             stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M20 6 9 17l-5-5" />
                                                        </svg>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- End Card -->
                            </div>
                        </div>
                        <!-- End Grid -->
                    
                    </div>
                    
                    <!--altre righe di dealer-->
                    
                    </div>

                </div>
                <!-- End Col -->
            </div>
            <!-- End Grid -->
                        
        </div>
        <!-- End Logo -->     

        
    </form>
    <!-- End Form -->
</div>
<!-- End Product Card -->
<script>
        let isOpen = false;
let selectedCount = 2; // Inizialmente Marco e Andrea sono selezionati
const totalCount = 3;

function toggleAccordion() {
  const content = document.getElementById('accordionContent');

  if (isOpen) {
    content.classList.remove('max-h-96');
    content.classList.add('max-h-0');
    isOpen = false;
  } else {
    content.classList.remove('max-h-0');
    content.classList.add('max-h-96');
    isOpen = true;
  }
}

function togglePerson(element, name) {
  const check = element.querySelector('.person-check');

  if (!check) {
    console.error('Check element not found for', name);
    return;
  }

  const isCurrentlySelected =
    check.classList.contains('bg-gray-800') ||
    check.classList.contains('dark:bg-neutral-200');

  if (isCurrentlySelected) {
    // Deseleziona
    check.classList.remove('bg-gray-800', 'flex', 'dark:bg-neutral-200', 'dark:text-neutral-800');
    check.classList.add('bg-gray-400', 'hidden', 'text-white');
    selectedCount--;
  } else {
    // Seleziona
    check.classList.remove('bg-gray-400', 'hidden');
    check.classList.add('bg-gray-800', 'flex', 'text-white');
    check.classList.add('dark:bg-neutral-200', 'dark:text-neutral-800');
    selectedCount++;
  }

  updateStatusIcon();
}

function updateStatusIcon() {
  const checkbox = document.getElementById('hs-pro-sufitsl');
  const noSelection = document.querySelector('.no-selection');
  const singleCheck = document.querySelector('.single-check');
  const doubleCheck = document.querySelector('.double-check');

  // reset totale
  [noSelection, singleCheck, doubleCheck].forEach(el => {
    el.classList.add('hidden');
    el.classList.remove('flex');
  });

  if (selectedCount === 0) {
    checkbox.checked = false;
    noSelection.classList.remove('hidden');
    noSelection.classList.add('flex');
  } else if (selectedCount === totalCount) {
    checkbox.checked = true;
    doubleCheck.classList.remove('hidden');
    doubleCheck.classList.add('flex');
  } else {
    checkbox.checked = true;
    singleCheck.classList.remove('hidden');
    singleCheck.classList.add('flex');
  }
}

// Inizializza lo stato
updateStatusIcon();

    </script>