<!-- Product Card -->
<div class="p-5 md:p-8 bg-white border border-gray-200 shadow-2xs rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
    <!-- Title -->
    <div class="mb-4 xl:mb-8">
        <h1 class="text-lg font-semibold text-gray-800 dark:text-neutral-200">
            Caricamento Tariffa
        </h1>
        <p class="text-sm text-gray-500 dark:text-neutral-500">
            Carica il file Excel con la tariffa del prodotto
        </p>
    </div>
    <!-- End Title -->
    <!-- Form -->
    <form id="product-form" class="form-validate-jquery" method="post" novalidate>
        <!-- Hidden Step Field -->
        <input type="hidden" name="step" value="3">

        <!-- Logo -->
        <div class="py-6 sm:py-8 space-y-5 border-t border-gray-200 first:border-t-0 dark:border-neutral-700">
            <!-- Grid -->
            <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                <div class="sm:col-span-4 xl:col-span-3 2xl:col-span-2">
                    <label class="sm:mt-2.5 inline-block text-sm text-gray-500 dark:text-neutral-500">
                        File di tariffa
                    </label>
                </div>
                <!-- End Col -->
                <div class="sm:col-span-8 xl:col-span-8">

                    <!-- List Group -->
                    <ul>
                        <!-- List Item -->
                        <li class="py-3 border-b last:border-b-0 border-gray-200 dark:border-neutral-700">
                            <div class="flex gap-x-3">
                                <span class="mt-1 flex shrink-0 justify-center items-center size-9.5 bg-white border border-gray-200 rounded-lg dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">                                    
                                    <svg class="shrink-0 size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield-icon lucide-shield"><path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"/></svg>
                                </span>
                                <div class="grow">
                                    <span class="font-medium text-gray-800 dark:text-neutral-200">
                                        Responsabilità civile
                                    </span>
                                    <p class="text-xs text-gray-500 dark:text-neutral-500">
                                        Italiana
                                    </p>
                                </div>

                                <!-- Button Group -->
                                <div>
                                    <div class="flex border border-gray-200 divide-x divide-gray-200 rounded-lg -space-x-px dark:border-neutral-700 dark:divide-neutral-700">
                                        <!-- Check Button Tooltip -->
                                        <div class="hs-tooltip inline-block">
                                            <button type="button" class="hs-tooltip-toggle size-8 inline-flex justify-center items-center gap-x-2 rounded-s-md bg-white text-green-600 shadow-2xs hover:bg-gray-50 hover:text-green-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 focus:text-green-700 dark:bg-neutral-800 dark:text-green-500 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 dark:hover:text-green-400 dark:focus:text-green-400">
                                                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20,6 9,17 4,12"/></svg>
                                            </button>
                                            <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg dark:bg-neutral-700" role="tooltip">
                                                Caricato
                                            </span>
                                        </div>
                                        <!-- End Check Button Tooltip -->
                                        <!-- Delete Button Tooltip -->
                                        <div class="hs-tooltip inline-block">
                                            <button type="button" class="hs-tooltip-toggle size-8 inline-flex justify-center items-center gap-x-2 rounded-e-md bg-white text-gray-500 shadow-2xs hover:bg-gray-50 hover:text-gray-800 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 focus:text-gray-800 dark:bg-neutral-800 dark:text-neutral-500 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 dark:hover:text-neutral-200 dark:focus:text-neutral-200">
                                                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>
                                            </button>
                                            <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg dark:bg-neutral-700" role="tooltip">
                                                Elimina
                                            </span>
                                        </div>
                                        <!-- End Delete Button Tooltip -->
                                    </div>
                                </div>
                                <!-- End Button Group -->
                            </div>
                        </li>
                        <!-- End List Item -->

                        <!-- List Item -->
                        <li class="py-3 border-b last:border-b-0 border-gray-200 dark:border-neutral-700">
                            <div class="flex gap-x-3">
                                <span class="mt-1 flex shrink-0 justify-center items-center size-9.5 bg-white border border-gray-200 rounded-lg dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">                                    
                                    <svg class="shrink-0 size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-flame-icon lucide-flame"><path d="M12 3q1 4 4 6.5t3 5.5a1 1 0 0 1-14 0 5 5 0 0 1 1-3 1 1 0 0 0 5 0c0-2-1.5-3-1.5-5q0-2 2.5-4"/></svg>
                                </span>
                                <div class="grow">
                                    <span class="font-medium text-gray-800 dark:text-neutral-200">
                                        Incendio e Furto
                                    </span>
                                    <p class="text-xs text-gray-500 dark:text-neutral-500">
                                        Italiana
                                    </p>
                                </div>

                                <!-- Button Group -->
                                <div>
                                    <div class="flex border border-gray-200 divide-x divide-gray-200 rounded-lg -space-x-px dark:border-neutral-700 dark:divide-neutral-700">
                                        <!-- Check Button Tooltip -->
                                        <div class="hs-tooltip inline-block">
                                            <button type="button" class="hs-tooltip-toggle size-8 inline-flex justify-center items-center gap-x-2 rounded-s-md bg-white text-green-600 shadow-2xs hover:bg-gray-50 hover:text-green-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 focus:text-green-700 dark:bg-neutral-800 dark:text-green-500 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 dark:hover:text-green-400 dark:focus:text-green-400">
                                                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20,6 9,17 4,12"/></svg>
                                            </button>
                                            <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg dark:bg-neutral-700" role="tooltip">
                                                Caricato
                                            </span>
                                        </div>
                                        <!-- End Check Button Tooltip -->
                                        <!-- Delete Button Tooltip -->
                                        <div class="hs-tooltip inline-block">
                                            <button type="button" class="hs-tooltip-toggle size-8 inline-flex justify-center items-center gap-x-2 rounded-e-md bg-white text-gray-500 shadow-2xs hover:bg-gray-50 hover:text-gray-800 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 focus:text-gray-800 dark:bg-neutral-800 dark:text-neutral-500 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 dark:hover:text-neutral-200 dark:focus:text-neutral-200">
                                                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>
                                            </button>
                                            <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg dark:bg-neutral-700" role="tooltip">
                                                Elimina
                                            </span>
                                        </div>
                                        <!-- End Delete Button Tooltip -->
                                    </div>
                                </div>
                                <!-- End Button Group -->
                            </div>
                        </li>
                        <!-- End List Item -->

                        <!-- List Item -->
                        <li class="py-3 border-b last:border-b-0 border-gray-200 dark:border-neutral-700">
                            <div class="flex gap-x-3">
                                <span class="mt-1 flex shrink-0 justify-center items-center size-9.5 bg-white border border-gray-200 rounded-lg dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">                                    
                                    <svg class="shrink-0 size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-ambulance-icon lucide-ambulance"><path d="M10 10H6"/><path d="M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2"/><path d="M19 18h2a1 1 0 0 0 1-1v-3.28a1 1 0 0 0-.684-.948l-1.923-.641a1 1 0 0 1-.578-.502l-1.539-3.076A1 1 0 0 0 16.382 8H14"/><path d="M8 8v4"/><path d="M9 18h6"/><circle cx="17" cy="18" r="2"/><circle cx="7" cy="18" r="2"/></svg>
                                </span>
                                <div class="grow">
                                    <span class="font-medium text-gray-800 dark:text-neutral-200">
                                        Assistenza stradale
                                    </span>
                                    <p class="text-xs text-gray-500 dark:text-neutral-500">
                                        IMA
                                    </p>
                                </div>

                                <!-- Button Group -->
                                <div>
                                    <div class="flex border border-gray-200 divide-x divide-gray-200 rounded-lg -space-x-px dark:border-neutral-700 dark:divide-neutral-700">
                                        <!-- Check Button Tooltip -->
                                        <div class="hs-tooltip inline-block">
                                            <button type="button" class="hs-tooltip-toggle size-8 inline-flex justify-center items-center gap-x-2 rounded-s-md bg-white text-green-600 shadow-2xs hover:bg-gray-50 hover:text-green-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 focus:text-green-700 dark:bg-neutral-800 dark:text-green-500 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 dark:hover:text-green-400 dark:focus:text-green-400">
                                                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20,6 9,17 4,12"/></svg>
                                            </button>
                                            <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg dark:bg-neutral-700" role="tooltip">
                                                Caricato
                                            </span>
                                        </div>
                                        <!-- End Check Button Tooltip -->
                                        <!-- Delete Button Tooltip -->
                                        <div class="hs-tooltip inline-block">
                                            <button type="button" class="hs-tooltip-toggle size-8 inline-flex justify-center items-center gap-x-2 rounded-e-md bg-white text-gray-500 shadow-2xs hover:bg-gray-50 hover:text-gray-800 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 focus:text-gray-800 dark:bg-neutral-800 dark:text-neutral-500 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 dark:hover:text-neutral-200 dark:focus:text-neutral-200">
                                                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>
                                            </button>
                                            <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg dark:bg-neutral-700" role="tooltip">
                                                Elimina
                                            </span>
                                        </div>
                                        <!-- End Delete Button Tooltip -->
                                    </div>
                                </div>
                                <!-- End Button Group -->
                            </div>
                        </li>
                        <!-- End List Item -->

                        <!-- List Item -->
                        <li class="py-3 border-b last:border-b-0 border-gray-200 dark:border-neutral-700">
                            <div class="flex gap-x-3">
                                <span class="mt-1 flex shrink-0 justify-center items-center size-9.5 bg-white border border-gray-200 rounded-lg dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">                                    
                                    <svg class="shrink-0 size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-scale-icon lucide-scale"><path d="m16 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z"/><path d="m2 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z"/><path d="M7 21h10"/><path d="M12 3v18"/><path d="M3 7h2c2 0 5-1 7-2 2 1 5 2 7 2h2"/></svg>
                                </span>
                                <div class="grow">
                                    <span class="font-medium text-gray-800 dark:text-neutral-200">
                                        Tutela Legale
                                    </span>
                                    <p class="text-xs text-gray-500 dark:text-neutral-500">
                                        Italiana
                                    </p>
                                </div>

                                <!-- Button Group -->
                                <div>
                                    <div class="flex border border-gray-200 divide-x divide-gray-200 rounded-lg -space-x-px dark:border-neutral-700 dark:divide-neutral-700">
                                        <!-- Check Button Tooltip -->
                                        <div class="hs-tooltip inline-block">
                                            <button type="button" class="hs-tooltip-toggle size-8 inline-flex justify-center items-center gap-x-2 rounded-s-md bg-white text-green-600 shadow-2xs hover:bg-gray-50 hover:text-green-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 focus:text-green-700 dark:bg-neutral-800 dark:text-green-500 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 dark:hover:text-green-400 dark:focus:text-green-400">
                                                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20,6 9,17 4,12"/></svg>
                                            </button>
                                            <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg dark:bg-neutral-700" role="tooltip">
                                                Caricato
                                            </span>
                                        </div>
                                        <!-- End Check Button Tooltip -->
                                        <!-- Delete Button Tooltip -->
                                        <div class="hs-tooltip inline-block">
                                            <button type="button" class="hs-tooltip-toggle size-8 inline-flex justify-center items-center gap-x-2 rounded-e-md bg-white text-gray-500 shadow-2xs hover:bg-gray-50 hover:text-gray-800 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 focus:text-gray-800 dark:bg-neutral-800 dark:text-neutral-500 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 dark:hover:text-neutral-200 dark:focus:text-neutral-200">
                                                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>
                                            </button>
                                            <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg dark:bg-neutral-700" role="tooltip">
                                                Elimina
                                            </span>
                                        </div>
                                        <!-- End Delete Button Tooltip -->
                                    </div>
                                </div>
                                <!-- End Button Group -->
                            </div>
                        </li>
                        <!-- End List Item -->

                        <!-- List Item -->
                        <li class="py-3 border-b last:border-b-0 border-gray-200 dark:border-neutral-700">
                            <div class="flex gap-x-3">
                                <span class="mt-1 flex shrink-0 justify-center items-center size-9.5 bg-white border border-gray-200 rounded-lg dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">                                                                        
                                    <svg class="shrink-0 size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bandage-icon lucide-bandage"><path d="M10 10.01h.01"/><path d="M10 14.01h.01"/><path d="M14 10.01h.01"/><path d="M14 14.01h.01"/><path d="M18 6v11.5"/><path d="M6 6v12"/><rect x="2" y="6" width="20" height="12" rx="2"/></svg>
                                </span>
                                <div class="grow">
                                    <span class="font-medium text-gray-800 dark:text-neutral-200">
                                        Infortuni del conducente
                                    </span>
                                    <p class="text-xs text-gray-500 dark:text-neutral-500">
                                        Italiana
                                    </p>
                                </div>

                                <!-- Button Group -->
                                <div>
                                    <div class="flex border border-gray-200 divide-x divide-gray-200 rounded-lg -space-x-px dark:border-neutral-700 dark:divide-neutral-700">
                                        <!-- Upload Button Tooltip -->
                                        <div class="hs-tooltip inline-block">
                                            <button type="button" class="hs-tooltip-toggle size-8 inline-flex justify-center items-center gap-x-2 rounded-s-md bg-white text-gray-500 shadow-2xs hover:bg-gray-50 hover:text-gray-800 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 focus:text-gray-800 dark:bg-neutral-800 dark:text-neutral-500 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 dark:hover:text-neutral-200 dark:focus:text-neutral-200">
                                                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="7,10 12,5 17,10"/><line x1="12" x2="12" y1="5" y2="15"/></svg>
                                            </button>
                                            <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg dark:bg-neutral-700" role="tooltip">
                                                Upload
                                            </span>
                                        </div>
                                        <!-- End Upload Button Tooltip -->
                                        <!-- Delete Button Tooltip -->
                                        <div class="hs-tooltip inline-block">
                                            <button type="button" class="hs-tooltip-toggle size-8 inline-flex justify-center items-center gap-x-2 rounded-e-md bg-white text-gray-500 shadow-2xs hover:bg-gray-50 hover:text-gray-800 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 focus:text-gray-800 dark:bg-neutral-800 dark:text-neutral-500 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 dark:hover:text-neutral-200 dark:focus:text-neutral-200">
                                                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>
                                            </button>
                                            <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg dark:bg-neutral-700" role="tooltip">
                                                Elimina
                                            </span>
                                        </div>
                                        <!-- End Delete Button Tooltip -->
                                    </div>
                                </div>
                                <!-- End Button Group -->
                            </div>
                        </li>
                        <!-- End List Item -->
                    </ul>
                    <!-- End List Group -->                                      

                </div>
                <!-- End Col -->
            </div>
            <!-- End Grid -->



            <!-- Grid -->
            <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                <div class="sm:col-span-4 xl:col-span-3 2xl:col-span-2">
                    <label class="sm:mt-2.5 inline-block text-sm text-gray-500 dark:text-neutral-500">
                        Anteprima
                    </label>
                </div>
                <!-- End Col -->
                <div class="sm:col-span-8 xl:col-span-8">

                    <!-- Users Table Card -->
                    <div class="flex flex-col bg-white border border-gray-200 shadow-2xs rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
                        <div class="pt-5 ps-5 pe-5">
                            <!-- Nav Tab -->
                            <nav class="flex gap-1 relative after:absolute after:bottom-0 after:inset-x-0 after:border-b-2 after:border-gray-200 dark:after:border-neutral-700" aria-label="Tabs" role="tablist" aria-orientation="horizontal">
                                <button type="button" class="hs-tab-active:after:bg-gray-800 hs-tab-active:text-gray-800 px-2.5 py-1.5 mb-2 relative inline-flex justify-center items-center gap-x-2 hover:bg-gray-100 text-gray-500 hover:text-gray-800 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 after:absolute after:-bottom-2 after:inset-x-0 after:z-10 after:h-0.5 after:pointer-events-none dark:hs-tab-active:text-neutral-200 dark:hs-tab-active:after:bg-neutral-400 dark:text-neutral-500 dark:hover:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden active" id="hs-pro-tabs-dut-item-all" aria-selected="true" data-hs-tab="#hs-pro-tabs-dut-all" aria-controls="hs-pro-tabs-dut-all" role="tab">
                                    Responsabilità civile
                                </button>
                                <button type="button" class="hs-tab-active:after:bg-gray-800 hs-tab-active:text-gray-800 px-2.5 py-1.5 mb-2 relative inline-flex justify-center items-center gap-x-2 hover:bg-gray-100 text-gray-500 hover:text-gray-800 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 after:absolute after:-bottom-2 after:inset-x-0 after:z-10 after:h-0.5 after:pointer-events-none dark:hs-tab-active:text-neutral-200 dark:hs-tab-active:after:bg-neutral-400 dark:text-neutral-500 dark:hover:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden " id="hs-pro-tabs-dut-item-archived" aria-selected="false" data-hs-tab="#hs-pro-tabs-dut-archived" aria-controls="hs-pro-tabs-dut-archived" role="tab">
                                    Incendio e Furto
                                </button>
                                <button type="button" class="hs-tab-active:after:bg-gray-800 hs-tab-active:text-gray-800 px-2.5 py-1.5 mb-2 relative inline-flex justify-center items-center gap-x-2 hover:bg-gray-100 text-gray-500 hover:text-gray-800 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 after:absolute after:-bottom-2 after:inset-x-0 after:z-10 after:h-0.5 after:pointer-events-none dark:hs-tab-active:text-neutral-200 dark:hs-tab-active:after:bg-neutral-400 dark:text-neutral-500 dark:hover:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden " id="hs-pro-tabs-dut-item-publish" aria-selected="false" data-hs-tab="#hs-pro-tabs-dut-publish" aria-controls="hs-pro-tabs-dut-publish" role="tab">
                                    Assistenza stradale
                                </button>
                                <button type="button" class="hs-tab-active:after:bg-gray-800 hs-tab-active:text-gray-800 px-2.5 py-1.5 mb-2 relative inline-flex justify-center items-center gap-x-2 hover:bg-gray-100 text-gray-500 hover:text-gray-800 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 after:absolute after:-bottom-2 after:inset-x-0 after:z-10 after:h-0.5 after:pointer-events-none dark:hs-tab-active:text-neutral-200 dark:hs-tab-active:after:bg-neutral-400 dark:text-neutral-500 dark:hover:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden " id="hs-pro-tabs-dut-item-unpublish" aria-selected="false" data-hs-tab="#hs-pro-tabs-dut-unpublish" aria-controls="hs-pro-tabs-dut-unpublish" role="tab">
                                    Tutela Legale
                                </button>
                            </nav>
                            <!-- End Nav Tab -->



                        </div>
                        <div>
                            <!-- Tab Content -->
                            <div id="hs-pro-tabs-dut-all" role="tabpanel" aria-labelledby="hs-pro-tabs-dut-item-all">
                                <div class="py-3 px-4 border-b border-gray-200 dark:bg-neutral-800 dark:border-neutral-700">
                                    <div class="relative max-w-xs">
                                        <label class="sr-only">Search</label>
                                        <input type="text" name="hs-table-with-pagination-search" id="hs-table-with-pagination-search" class="py-1.5 sm:py-2 px-3 ps-9 block w-full border-gray-200 shadow-2xs rounded-lg sm:text-sm focus:z-10 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Cerca tariffe...">
                                            <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none ps-3">
                                                <svg class="size-4 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <circle cx="11" cy="11" r="8"></circle>
                                                    <path d="m21 21-4.3-4.3"></path>
                                                </svg>
                                            </div>
                                    </div>
                                </div>
                                <!-- Table Content -->
                                <div class="overflow-hidden border-b border-gray-200 dark:bg-neutral-800 dark:border-neutral-700">
                                    <table class="min-w-full divide-y divide-gray-200 dark:divide-neutral-700">
                                        <thead class="bg-gray-50 dark:bg-neutral-700">
                                            <tr>                                                
                                                <th scope="col" class="px-6 py-3 text-start text-xs font-medium text-gray-500 uppercase dark:text-neutral-500">Provincia</th>
                                                <th scope="col" class="px-6 py-3 text-start text-xs font-medium text-gray-500 uppercase dark:text-neutral-500">Compagnia</th>
                                                <th scope="col" class="px-6 py-3 text-start text-xs font-medium text-gray-500 uppercase dark:text-neutral-500">Tipo Garanzia</th>
                                                <th scope="col" class="px-6 py-3 text-end text-xs font-medium text-gray-500 uppercase dark:text-neutral-500">Premio Annuale</th>
                                            </tr>
                                        </thead>
                                        <tbody class="divide-y divide-gray-200 dark:divide-neutral-700">
                                            <tr>                                                
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-neutral-200">Milano</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">Italiana Assicurazioni</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">Responsabilità civile</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-end text-sm font-semibold text-gray-800 dark:text-neutral-200">€ 485,00</td>
                                            </tr>
                                            <tr>
                                                
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-neutral-200">Roma</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">IMA</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">Incendio e furto</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-end text-sm font-semibold text-gray-800 dark:text-neutral-200">€ 320,50</td>
                                            </tr>
                                            <tr>
                                                
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-neutral-200">Napoli</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">Italiana Assicurazioni</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">Assistenza stradale</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-end text-sm font-semibold text-gray-800 dark:text-neutral-200">€ 195,75</td>
                                            </tr>
                                            <tr>
                                                
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-neutral-200">Torino</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">IMA</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">Tutela legale</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-end text-sm font-semibold text-gray-800 dark:text-neutral-200">€ 425,90</td>
                                            </tr>
                                            <tr>
                                                
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-neutral-200">Bologna</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">Italiana Assicurazioni</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">Infortuni del conducente</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-end text-sm font-semibold text-gray-800 dark:text-neutral-200">€ 275,25</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div><!-- End Table Content -->

                                <!-- Footer -->
                                <div class="py-1 px-4">
                                    <nav class="flex items-center space-x-1" aria-label="Pagination">
                                        <button type="button" class="p-2.5 min-w-10 inline-flex justify-center items-center gap-x-2 text-sm rounded-full text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" aria-label="Previous">
                                            <span aria-hidden="true">«</span>
                                            <span class="sr-only">Previous</span>
                                        </button>
                                        <button type="button" class="min-w-10 flex justify-center items-center text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 py-2.5 text-sm rounded-full disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:focus:bg-neutral-700 dark:hover:bg-neutral-700" aria-current="page">1</button>
                                        <button type="button" class="min-w-10 flex justify-center items-center text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 py-2.5 text-sm rounded-full disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:focus:bg-neutral-700 dark:hover:bg-neutral-700">2</button>
                                        <button type="button" class="min-w-10 flex justify-center items-center text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 py-2.5 text-sm rounded-full disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:focus:bg-neutral-700 dark:hover:bg-neutral-700">3</button>
                                        <button type="button" class="p-2.5 min-w-10 inline-flex justify-center items-center gap-x-2 text-sm rounded-full text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" aria-label="Next">
                                            <span class="sr-only">Next</span>
                                            <span aria-hidden="true">»</span>
                                        </button>
                                    </nav>
                                </div>
                                <!-- End Footer -->
                            </div>
                            <!-- End Tab Content -->

                            <!-- Tab Content -->
                            <div id="hs-pro-tabs-dut-archived" class="hidden" role="tabpanel" aria-labelledby="hs-pro-tabs-dut-item-archived">
                                <!-- Empty State -->
                                <div class="p-5  flex flex-col justify-center items-center text-center">
                                    <svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800" />
                                        <rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-50 dark:stroke-neutral-700/10" />
                                        <rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                                        <rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                                        <rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                                        <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800" />
                                        <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/30" />
                                        <rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                                        <rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                                        <rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                                        <g filter="url(#filter4)">
                                            <rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor" class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges" />
                                            <rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges" />
                                            <rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700 " />
                                            <rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                                            <rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                                        </g>
                                        <defs>
                                            <filter id="filter4" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                                                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                                                <feOffset dy="6" />
                                                <feGaussianBlur stdDeviation="6" />
                                                <feComposite in2="hardAlpha" operator="out" />
                                                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0" />
                                                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1187_14810" />
                                                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810" result="shape" />
                                            </filter>
                                        </defs>
                                    </svg>

                                    <div class="max-w-sm mx-auto">
                                        <p class="mt-2 font-medium text-gray-800 dark:text-neutral-200">
                                            Non hai ancora caricato la tariffa
                                        </p>
                                        <p class="mb-5 text-sm text-gray-500 dark:text-neutral-500">
                                            Poi potrai vedere i premi per questa garanzia
                                        </p>
                                    </div>

                                    <a class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="#">
                                        Torna al caricamento
                                    </a>
                                </div>
                                <!-- End Empty State -->
                            </div>
                            <!-- End Tab Content -->

                            <!-- Tab Content -->
                            <div id="hs-pro-tabs-dut-publish" class="hidden" role="tabpanel" aria-labelledby="hs-pro-tabs-dut-item-publish">
                                <!-- Empty State -->
                                <div class="p-5  flex flex-col justify-center items-center text-center">
                                    <svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800" />
                                        <rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-50 dark:stroke-neutral-700/10" />
                                        <rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                                        <rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                                        <rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                                        <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800" />
                                        <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/30" />
                                        <rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                                        <rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                                        <rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                                        <g filter="url(#filter5)">
                                            <rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor" class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges" />
                                            <rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges" />
                                            <rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700 " />
                                            <rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                                            <rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                                        </g>
                                        <defs>
                                            <filter id="filter5" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                                                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                                                <feOffset dy="6" />
                                                <feGaussianBlur stdDeviation="6" />
                                                <feComposite in2="hardAlpha" operator="out" />
                                                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0" />
                                                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1187_14810" />
                                                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810" result="shape" />
                                            </filter>
                                        </defs>
                                    </svg>

                                    <div class="max-w-sm mx-auto">
                                        <p class="mt-2 font-medium text-gray-800 dark:text-neutral-200">
                                            Your data will appear here soon.
                                        </p>
                                        <p class="mb-5 text-sm text-gray-500 dark:text-neutral-500">
                                            In the meantime, you can create new custom insights to monitor your most important metrics.
                                        </p>
                                    </div>

                                    <a class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="#">
                                        Learn more
                                    </a>
                                </div>
                                <!-- End Empty State -->
                            </div>
                            <!-- End Tab Content -->

                            <!-- Tab Content -->
                            <div id="hs-pro-tabs-dut-unpublish" class="hidden" role="tabpanel" aria-labelledby="hs-pro-tabs-dut-item-unpublish">
                                <!-- Empty State -->
                                <div class="p-5  flex flex-col justify-center items-center text-center">
                                    <svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800" />
                                        <rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-50 dark:stroke-neutral-700/10" />
                                        <rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                                        <rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                                        <rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30" />
                                        <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800" />
                                        <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/30" />
                                        <rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                                        <rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                                        <rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70" />
                                        <g filter="url(#filter6)">
                                            <rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor" class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges" />
                                            <rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges" />
                                            <rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700 " />
                                            <rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                                            <rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700" />
                                        </g>
                                        <defs>
                                            <filter id="filter6" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                                                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                                                <feOffset dy="6" />
                                                <feGaussianBlur stdDeviation="6" />
                                                <feComposite in2="hardAlpha" operator="out" />
                                                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0" />
                                                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1187_14810" />
                                                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810" result="shape" />
                                            </filter>
                                        </defs>
                                    </svg>

                                    <div class="max-w-sm mx-auto">
                                        <p class="mt-2 font-medium text-gray-800 dark:text-neutral-200">
                                            Your data will appear here soon.
                                        </p>
                                        <p class="mb-5 text-sm text-gray-500 dark:text-neutral-500">
                                            In the meantime, you can create new custom insights to monitor your most important metrics.
                                        </p>
                                    </div>

                                    <a class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="#">
                                        Learn more
                                    </a>
                                </div>
                                <!-- End Empty State -->
                            </div>
                            <!-- End Tab Content -->
                        </div>
                    </div>
                    <!-- End Users Table Card -->


                </div>
                <!-- End Col -->
            </div>
            <!-- End Grid -->

        </div>
        <!-- End Logo -->     

    </form>
    <!-- End Form -->
</div>
<!-- End Product Card -->
