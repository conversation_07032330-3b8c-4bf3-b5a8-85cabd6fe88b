<!-- WarrantyType Form Content for Offcanvas -->
<script class="reload-script-on-load">
    addRoute('BE_WARRANTYTYPE', '{{ routes("BE_WARRANTYTYPE") }}');
    addRoute('BE_WARRANTYTYPE_SAVE', '{{ routes("BE_WARRANTYTYPE_SAVE") }}');
    addRoute('BE_WARRANTYTYPE_OPERATE', '{{ routes("BE_WARRANTYTYPE_OPERATE") }}');
</script>

<!-- Form Content with Sticky Footer -->
<div class="flex flex-col h-full">
    <!-- Scrollable Content Area -->
    <div class="flex-1 overflow-y-auto">
        <div class="mt-3">
            {% set postUrl = routes('BE_WARRANTYTYPE_SAVE') %}
            {% if curWarrantyType.id is not empty %}
            {% set postUrl = routes('BE_WARRANTYTYPE_SAVE') + '?warrantyTypeId=' + curWarrantyType.id %}
            {% endif %}

            <form id="warrantytype-edit-offcanvas" method="POST" action="{{ postUrl }}" enctype="multipart/form-data" class="form-validate-jquery mt-5">
                <!-- Hidden ID field -->
                <input type="hidden" name="id" value="{{ curWarrantyType.id }}">

                <!-- Name -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Nome: <span class="text-red-500">*</span>
                    </label>
                    <input name="name" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600 maxlength" placeholder="Nome del tipo di garanzia" value="{{ curWarrantyType.name }}" required maxlength="100" {% if not user.hasPermission('WARRANTY_TYPE_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Code -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Codice: <span class="text-red-500">*</span>
                    </label>
                    <input name="code" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600 maxlength" placeholder="Codice del tipo di garanzia" value="{{ curWarrantyType.code }}" required maxlength="50" {% if not user.hasPermission('WARRANTY_TYPE_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Icon -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Icona:
                    </label>
                    <select id="icon" name="icon" required data-hs-select='{
                        "searchClasses": "block w-full sm:text-sm border-gray-200 rounded-lg focus:border-blue-500 focus:ring-blue-500 before:absolute before:inset-0 before:z-1 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 py-1.5 sm:py-2 px-3",
                        "searchWrapperClasses": "bg-white p-2 -mx-1 sticky top-0 dark:bg-neutral-900",
                        "placeholder": "Seleziona icona...",
                        "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span class=\"me-2\" data-icon></span><span class=\"text-gray-800 dark:text-neutral-200 \" data-title></span></button>",
                        "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex gap-x-2 text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:outline-hidden focus:ring-2 focus:ring-blue-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600",
                        "dropdownClasses": "mt-2 max-h-72 pb-1 px-1 space-y-0.5 z-20 w-full bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                        "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                        "optionTemplate": "<div><div class=\"flex items-center\"><div class=\"me-2\" data-icon></div><div class=\"text-gray-800 dark:text-neutral-200 \" data-title></div><span class=\"ms-auto hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-blue-600 dark:text-blue-500 \" xmlns=\"http:.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div></div></div>",
                        "extraMarkup": "<div class=\"absolute top-1/2 end-3 -translate-y-1/2\"><svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500 \" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m7 15 5 5 5-5\"/><path d=\"m7 9 5-5 5 5\"/></svg></div>"
                    }' class="hidden" {% if not user.hasPermission('WARRANTY_TYPE_MANAGEMENT', 'edit') %}disabled{% endif %}>
                        <option value="">Seleziona stato</option>
                        <option value="liability" {% if curWarrantyType.icon == 'liability' %}selected{% endif %} data-hs-select-option='{
                            "icon": "<img class=\"inline-block size-4 rounded-full\" src=\"{{ contextPath }}/img/warranties/liability.svg\" alt=\"Responsabilità civile\" />"}'>
                            Responsabilità civile
                        </option>
                        <option value="theft-fire" {% if curWarrantyType.icon == 'theft-fire' %}selected{% endif %} data-hs-select-option='{
                            "icon": "<img class=\"inline-block size-4 rounded-full\" src=\"{{ contextPath }}/img/warranties/theft-fire.svg\" alt=\"Incendio e Furto\" />"}'>
                            Incendio e Furto
                        </option>
                        <option value="assistance" {% if curWarrantyType.icon == 'assistance' %}selected{% endif %} data-hs-select-option='{
                            "icon": "<img class=\"inline-block size-4 rounded-full\" src=\"{{ contextPath }}/img/warranties/assistance.svg\" alt=\"Assistenza stradale\" />"}'>
                            Assistenza stradale
                        </option>
                        <option value="legal" {% if curWarrantyType.icon == 'legal' %}selected{% endif %} data-hs-select-option='{
                                "icon": "<img class=\"inline-block size-4 rounded-full\" src=\"{{ contextPath }}/img/warranties/legal.svg\" alt=\"Tutela Legale\" />"}'>
                            Tutela Legale
                        </option>
                        <option value="injury" {% if curWarrantyType.icon == 'injury' %}selected{% endif %} data-hs-select-option='{
                                "icon": "<img class=\"inline-block size-4 rounded-full\" src=\"{{ contextPath }}/img/warranties/injury.svg\" alt=\"Infortuni del conducente\" />"}'>
                            Infortuni del conducente
                        </option>
                    </select>
                </div>

            </form>
        </div>
    </div>

    <!-- Sticky Footer -->
    <div class="flex-shrink-0 border-t border-gray-200 bg-white px-4 pt-4 dark:border-neutral-700 dark:bg-neutral-900">
        <div class="flex justify-end gap-x-2">
            {% if curWarrantyType is empty %}
                <!-- New WarrantyType - Show Save Button -->
                {% if user.hasPermission('WARRANTY_TYPE_MANAGEMENT', 'create') %}
                <button type="submit" form="warrantytype-edit-offcanvas" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                    Salva Tipo Garanzia
                </button>
                {% else %}
                <div class="text-sm text-gray-500 dark:text-neutral-400 py-2 px-3">
                    Non hai i permessi per creare tipi di garanzia
                </div>
                {% endif %}
            {% else %}
                <!-- Edit WarrantyType - Show Update and Delete Buttons -->
                {% if user.hasPermission('WARRANTY_TYPE_MANAGEMENT', 'delete') %}
                <button type="button" data-warrantytypeid="{{ curWarrantyType.id }}" id="delete-warrantytype-btn" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-red-600 hover:bg-red-50 focus:outline-none focus:bg-red-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-red-500 dark:hover:bg-red-800/30 dark:focus:bg-red-800/30">
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M3 6h18"/>
                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
                        <line x1="10" x2="10" y1="11" y2="17"/>
                        <line x1="14" x2="14" y1="11" y2="17"/>
                    </svg>
                    Elimina
                </button>
                {% endif %}
                {% if user.hasPermission('WARRANTY_TYPE_MANAGEMENT', 'edit') %}
                <button type="submit" form="warrantytype-edit-offcanvas" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                    Modifica Tipo Garanzia
                </button>
                {% else %}
                <div class="text-sm text-gray-500 dark:text-neutral-400 py-2 px-3">
                    Non hai i permessi per modificare tipi di garanzia
                </div>
                {% endif %}
            {% endif %}
        </div>
    </div>
</div>
