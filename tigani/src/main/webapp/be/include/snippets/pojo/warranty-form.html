<!-- Warranty Form Content for Offcanvas -->
<script class="reload-script-on-load">
    addRoute('BE_WARRANTY', '{{ routes("BE_WARRANTY") }}');
    addRoute('BE_WARRANTY_SAVE', '{{ routes("BE_WARRANTY_SAVE") }}');
    addRoute('BE_WARRANTY_OPERATE', '{{ routes("BE_WARRANTY_OPERATE") }}');
</script>

<!-- Form Content with Sticky Footer -->
<div class="flex flex-col h-full">
    <!-- Scrollable Content Area -->
    <div class="flex-1 overflow-y-auto">
        <div class="mt-3">
            {% set postUrl = routes('BE_WARRANTY_SAVE') %}
            {% if curWarranty.id is not empty %}
            {% set postUrl = routes('BE_WARRANTY_SAVE') + '?warrantyId=' + curWarranty.id %}
            {% endif %}

            <form id="warranty-edit-offcanvas" method="POST" action="{{ postUrl }}" enctype="multipart/form-data" class="form-validate-jquery mt-5">
                <!-- Hidden ID field -->
                <input type="hidden" name="id" value="{{ curWarranty.id }}">

                <!-- Code -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Codice: <span class="text-red-500">*</span>
                    </label>
                    <input name="code" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600 maxlength" placeholder="Codice della garanzia" value="{{ curWarranty.code }}" required maxlength="50" {% if not user.hasPermission('WARRANTY_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Title -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Titolo: <span class="text-red-500">*</span>
                    </label>
                    <input name="title" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600 maxlength" placeholder="Titolo della garanzia" value="{{ curWarranty.title }}" required maxlength="100" {% if not user.hasPermission('WARRANTY_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Description -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Descrizione:
                    </label>
                    <textarea name="description" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Descrizione della garanzia" rows="3" {% if not user.hasPermission('WARRANTY_MANAGEMENT', 'edit') %}readonly{% endif %}>{{ curWarranty.description }}</textarea>
                </div>

                <!-- Warranty Type Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Tipo Garanzia: <span class="text-red-500">*</span>
                    </label>
                    <select name="warrantyTypeId" data-hs-select='{
                        "hasSearch": true,
                        "searchPlaceholder": "Cerca tipo garanzia...",
                        "searchClasses": "block w-full sm:text-sm border-gray-200 rounded-lg focus:border-blue-500 focus:ring-blue-500 before:absolute before:inset-0 before:z-1 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 py-1.5 sm:py-2 px-3",
                        "searchWrapperClasses": "bg-white p-2 -mx-1 sticky top-0 dark:bg-neutral-900",
                        "placeholder": "Seleziona un tipo garanzia...",
                        "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span class=\"me-2\" data-icon></span><span class=\"text-gray-800 dark:text-neutral-200 \" data-title></span></button>",
                        "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex gap-x-2 text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:outline-hidden focus:ring-2 focus:ring-blue-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600",
                        "dropdownClasses": "mt-2 max-h-72 pb-1 px-1 space-y-0.5 z-20 w-full bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                        "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                        "optionTemplate": "<div><div class=\"flex items-center\"><div class=\"me-2\" data-icon></div><div class=\"text-gray-800 dark:text-neutral-200 \" data-title></div><span class=\"ms-auto hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-blue-600 dark:text-blue-500 \" xmlns=\"http:.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div></div></div>",
                        "extraMarkup": "<div class=\"absolute top-1/2 end-3 -translate-y-1/2\"><svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500 \" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m7 15 5 5 5-5\"/><path d=\"m7 9 5-5 5 5\"/></svg></div>"
                    }' class="hidden" required {% if not user.hasPermission('WARRANTY_MANAGEMENT', 'edit') %}disabled{% endif %}>
                        <option value="">Seleziona un tipo garanzia</option>
                        {% if warrantyTypes is not empty %}
                            {% for warrantyType in warrantyTypes %}
                                <option value="{{ warrantyType.id }}" {% if curWarranty.warrantyTypeId is not empty and curWarranty.warrantyTypeId.toString() == warrantyType.id.toString() %}selected{% endif %}>{{ warrantyType.name }}</option>
                            {% endfor %}
                        {% endif %}
                    </select>
                </div>

                <!-- Insurance Company Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Compagnia Assicurativa: <span class="text-red-500">*</span>
                    </label>
                    <select name="insuranceCompanyId" data-hs-select='{
                        "hasSearch": true,
                        "searchPlaceholder": "Cerca compagnia...",
                        "searchClasses": "block w-full sm:text-sm border-gray-200 rounded-lg focus:border-blue-500 focus:ring-blue-500 before:absolute before:inset-0 before:z-1 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 py-1.5 sm:py-2 px-3",
                        "searchWrapperClasses": "bg-white p-2 -mx-1 sticky top-0 dark:bg-neutral-900",
                        "placeholder": "Seleziona una compagnia...",
                        "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span class=\"me-2\" data-icon></span><span class=\"text-gray-800 dark:text-neutral-200 \" data-title></span></button>",
                        "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex gap-x-2 text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:outline-hidden focus:ring-2 focus:ring-blue-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600",
                        "dropdownClasses": "mt-2 max-h-72 pb-1 px-1 space-y-0.5 z-20 w-full bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                        "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                        "optionTemplate": "<div><div class=\"flex items-center\"><div class=\"me-2\" data-icon></div><div class=\"text-gray-800 dark:text-neutral-200 \" data-title></div><span class=\"ms-auto hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-blue-600 dark:text-blue-500 \" xmlns=\"http:.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div></div></div>",
                        "extraMarkup": "<div class=\"absolute top-1/2 end-3 -translate-y-1/2\"><svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500 \" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m7 15 5 5 5-5\"/><path d=\"m7 9 5-5 5 5\"/></svg></div>"
                    }' class="hidden" required {% if not user.hasPermission('WARRANTY_MANAGEMENT', 'edit') %}disabled{% endif %}>
                        <option value="">Seleziona una compagnia</option>
                        {% if insuranceCompanies is not empty %}
                            {% for company in insuranceCompanies %}
                                <option value="{{ company.id }}" {% if curWarranty.insuranceCompanyId is not empty and curWarranty.insuranceCompanyId.toString() == company.id.toString() %}selected{% endif %}>{{ company.code }}</option>
                            {% endfor %}
                        {% endif %}
                    </select>
                </div>

                <!-- Criteria Fields Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Criteri: <span class="text-red-500">*</span>
                    </label>
                    <select id="criteriaFields" name="criteriaFields" multiple="" data-hs-select='{
                        "hasSearch": true,
                        "searchPlaceholder": "Cerca criterio...",
                        "searchClasses": "block w-full sm:text-sm border-gray-200 rounded-lg focus:border-blue-500 focus:ring-blue-500 before:absolute before:inset-0 before:z-1 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 py-1.5 sm:py-2 px-3",
                        "searchWrapperClasses": "bg-white p-2 -mx-1 sticky top-0 dark:bg-neutral-900",
                        "placeholder": "Seleziona un criterio...",
                        "mode": "tags",
                        "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                        "optionClasses": "py-2 px-3 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                        "wrapperClasses": "relative ps-0.5 pe-9 min-h-[38px] flex items-center flex-wrap text-nowrap w-full border border-gray-200 rounded-lg text-start text-sm focus:border-stone-800 focus:ring-stone-800 dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300",
                        "tagsItemTemplate": "<div class=\"flex flex-nowrap items-center relative z-10 bg-white border border-gray-200 rounded-full py-1 px-2 m-0.5 dark:bg-neutral-900 dark:border-neutral-700\"><div class=\"size-4 me-1 flex items-center justify-center\" data-icon></div><div class=\"whitespace-nowrap text-gray-800 dark:text-neutral-200 leading-4 text-sm\" data-title></div><div class=\"inline-flex shrink-0 justify-center items-center size-4 ms-1.5 rounded-full text-gray-800 bg-gray-200 hover:bg-gray-300 focus:outline-hidden focus:ring-2 focus:ring-gray-400 text-xs dark:bg-neutral-700/50 dark:hover:bg-neutral-700 dark:text-neutral-400 cursor-pointer\" data-remove><svg class=\"shrink-0 size-2.5\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M18 6 6 18\"/><path d=\"m6 6 12 12\"/></svg></div></div>",
                        "tagsInputId": "hs-tags-input-criteri",
                        "tagsInputClasses": "py-1 px-2 min-w-16 rounded-lg order-1 border-transparent focus:ring-0 text-sm outline-hidden dark:bg-transparent dark:placeholder-neutral-500 dark:text-neutral-300",
                        "optionTemplate": "<div class=\"flex items-center\"><div class=\"me-2\" data-icon></div><div><div class=\"hs-selected:font-semibold text-sm text-gray-800 dark:text-neutral-200\" data-title></div></div><div class=\"ms-auto\"><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-4 text-blue-600\" xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" fill=\"currentColor\" viewBox=\"0 0 16 16\"><path d=\"M12.736 3.97a.733.733 0 0 1 1.047 0c.286.289.29.756.01 1.05L7.88 12.01a.733.733 0 0 1-1.065.02L3.217 8.384a.757.757 0 0 1 0-1.06.733.733 0 0 1 1.047 0l3.052 3.093 5.4-6.425a.247.247 0 0 1 .02-.022Z\"/></svg></span></div></div>",
                        "extraMarkup": "<div class=\"absolute top-1/2 end-3 -translate-y-1/2\"><svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m7 15 5 5 5-5\"/><path d=\"m7 9 5-5 5 5\"/></svg></div>",
                        "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span class=\"me-2\" data-icon></span><span class=\"text-gray-800 dark:text-neutral-200 \" data-title></span></button>",
                        "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex gap-x-2 text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:outline-hidden focus:ring-2 focus:ring-blue-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600"
                    }' class="hidden" required {% if not user.hasPermission('WARRANTY_MANAGEMENT', 'edit') %}disabled{% endif %}>
                        <option value="">Seleziona criteri di ricerca</option>
                        <option value="province" {% if curWarranty.criteriaFields is not empty and curWarranty.criteriaFields contains 'province' %}selected{% endif %} data-hs-select-option='{
                            "icon": "<svg class=\"shrink-0 size-4 text-gray-600\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><circle cx=\"12\" cy=\"10\" r=\"3\"/><path d=\"m12 21.7-7.3-7.3a7.05 7.05 0 0 1 0-10C6.4 2.7 9.1 1.5 12 1.5s5.6 1.2 7.3 2.9a7.05 7.05 0 0 1 0 10L12 21.7z\"/></svg>"}'>
                            Provincia di residenza
                        </option>
                        <option value="insuranceSituation" {% if curWarranty.criteriaFields is not empty and curWarranty.criteriaFields contains 'insuranceSituation' %}selected{% endif %} data-hs-select-option='{
                            "icon": "<svg class=\"shrink-0 size-4 text-gray-600\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10\"/><path d=\"m9 12 2 2 4-4\"/></svg>"}'>
                            Situazione assicurativa attuale
                        </option>
                        <option value="claimNumbers" {% if curWarranty.criteriaFields is not empty and curWarranty.criteriaFields contains 'claimNumbers' %}selected{% endif %} data-hs-select-option='{
                            "icon": "<svg class=\"shrink-0 size-4 text-gray-600\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z\"/></svg>"}'>
                            Numero di sinistri negli ultimi 5 anni
                        </option>
                        <option value="cu" {% if curWarranty.criteriaFields is not empty and curWarranty.criteriaFields contains 'cu' %}selected{% endif %} data-hs-select-option='{
                            "icon": "<svg class=\"shrink-0 size-4 text-gray-600\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\"/><polyline points=\"14,2 14,8 20,8\"/><line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\"/><line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\"/></svg>"}'>
                            Certificato di Unanime (CU)
                        </option>
                    </select>
                </div>

            </form>
        </div>
    </div>

    <!-- Sticky Footer -->
    <div class="flex-shrink-0 border-t border-gray-200 bg-white px-4 pt-4 dark:border-neutral-700 dark:bg-neutral-900">
        <div class="flex justify-end gap-x-2">
            {% if curWarranty is empty %}
                <!-- New Warranty - Show Save Button -->
                {% if user.hasPermission('WARRANTY_MANAGEMENT', 'create') %}
                <button type="submit" form="warranty-edit-offcanvas" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                    Salva Garanzia
                </button>
                {% else %}
                <div class="text-sm text-gray-500 dark:text-neutral-400 py-2 px-3">
                    Non hai i permessi per creare garanzie
                </div>
                {% endif %}
            {% else %}
                <!-- Edit Warranty - Show Update and Delete Buttons -->
                {% if user.hasPermission('WARRANTY_MANAGEMENT', 'delete') %}
                <button type="button" data-warrantyid="{{ curWarranty.id }}" id="delete-warranty-btn" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-red-600 hover:bg-red-50 focus:outline-none focus:bg-red-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-red-500 dark:hover:bg-red-800/30 dark:focus:bg-red-800/30">
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M3 6h18"/>
                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
                        <line x1="10" x2="10" y1="11" y2="17"/>
                        <line x1="14" x2="14" y1="11" y2="17"/>
                    </svg>
                    Elimina
                </button>
                {% endif %}
                {% if user.hasPermission('WARRANTY_MANAGEMENT', 'edit') %}
                <button type="submit" form="warranty-edit-offcanvas" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                    Modifica Garanzia
                </button>
                {% else %}
                <div class="text-sm text-gray-500 dark:text-neutral-400 py-2 px-3">
                    Non hai i permessi per modificare garanzie
                </div>
                {% endif %}
            {% endif %}
        </div>
    </div>
</div>
