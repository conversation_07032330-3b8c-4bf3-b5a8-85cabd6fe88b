{% extends "be/include/base.html" %}

{% set page = 'WARRANTY_CRITERIA_TEST' %}

{% block extrahead %}
<title>Test Criteri Garanzie</title>
{% include "be/include/snippets/plugins/select2.html" %}
<script src="{{ contextPath }}/be/js/pages/warranty-criteria-test.js?{{ buildNumber }}"></script>        
{% endblock %}

{% block content %}
<script class="reload-script-on-load">
addRoute('BE_WARRANTY_CRITERIA_SEARCH', '{{ routes("BE_WARRANTY_CRITERIA_SEARCH") }}');
</script>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Content area -->
    <div class="content">

        <!-- Search form -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Inserisci i criteri da testare</h5>
                <div class="card-text text-muted">
                    Inserisci i valori dei criteri per vedere quali garanzie sono valide per quella combinazione.
                    <br><strong>Nota:</strong> Puoi inserire uno o più criteri. Il sistema troverà tutte le garanzie che includono i valori specificati nei loro range/liste.
                </div>
            </div>

            <div class="card-body">
                <form id="criteriaTestForm">
                    <div class="row">
                        
                        <!-- Province Code -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Provincia:</label>
                            <select name="provinceCode" class="form-control" data-placeholder="Seleziona provincia">
                                <option value=""></option>
                                {% set cities = lookup('City', checkPublished=false, language='false', distinct='provinceCode') %}
                                {% if cities is not empty %}
                                    {% for city in cities %}
                                        <option value="{{ city.provinceCode }}">{{ city.provinceCode }} - {{ city.province }}</option>
                                    {% endfor %}
                                {% endif %}
                            </select>
                            <div class="form-text text-muted">Seleziona il codice provincia da testare.</div>
                        </div>

                        <!-- Claim Number -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Numero Sinistri:</label>
                            <input name="claimNumber" type="number" class="form-control" placeholder="Es: 2" min="0">
                            <div class="form-text text-muted">Inserisci il numero di sinistri da testare.</div>
                        </div>

                        <!-- Insurance Provenance Type -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Tipo Provenienza Assicurativa:</label>
                            <select name="insuranceProvenanceTypeId" class="form-control" data-placeholder="Seleziona tipo provenienza">
                                <option value=""></option>
                                {% set insuranceProvenanceTypes = lookup('InsuranceProvenanceType', checkPublished=false, language='false') %}
                                {% if insuranceProvenanceTypes is not empty %}
                                    {% for type in insuranceProvenanceTypes %}
                                        <option value="{{ type.id }}">{{ type.title }}</option>
                                    {% endfor %}
                                {% endif %}
                            </select>
                            <div class="form-text text-muted">Seleziona il tipo di provenienza assicurativa.</div>
                        </div>

                        <!-- Universal Class -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Classe Universale:</label>
                            <input name="universalClass" type="number" class="form-control" placeholder="Es: 10" min="1" max="18">
                            <div class="form-text text-muted">Inserisci la classe universale (da 1 a 18).</div>
                        </div>

                    </div>

                    <div class="text-end">
                        <button type="button" id="clearForm" class="btn btn-light">
                            <i class="ph-eraser me-2"></i>
                            Pulisci
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="ph-magnifying-glass me-2"></i>
                            Cerca Garanzie
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <!-- /search form -->

        <!-- Results -->
        <div id="resultsContainer" class="card" style="display: none;">
            <div class="card-header">
                <h5 class="mb-0">Risultati della ricerca</h5>
                <div id="resultsCount" class="card-text text-muted"></div>
            </div>

            <div class="card-body">
                <div id="resultsTable"></div>
            </div>
        </div>
        <!-- /results -->

    </div>
    <!-- /content area -->

</div>
<!-- /content area -->

{% endblock %}
