{% extends "be/include/base.html" %}

{% set page = 'FIELDTRANSLATION' %}
{% set title = curFieldTranslation is empty ? 'Nuova Traduzione Campo' : 'Modifica Traduzione Campo' %}

{% block extrahead %}

<title>{{ title }} </title>

<!-- Specific script -->
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}
{% include "be/include/snippets/plugins/select2.html" %}
<!-- specific script-->

<!-- Page script -->
<script src="{{ contextPath }}/be/js/pages/fieldtranslation.js?{{ buildNumber }}"></script>
<!-- /page script -->

{% endblock %}

{% block content %}
<script class="reload-script-on-load">
    addRoute('BE_FIELDTRANSLATION', '{{ routes("BE_FIELDTRANSLATION") }}');
</script>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Form -->
    <div class="card">
        <div class="card-header">
            {% if curFieldTranslation is empty %}
            <h5 class="mb-0">Inserisci Traduzione Campo</h5>
            {% else %}
            <h5 class="mb-0">Modifica Traduzione Campo</h5>
            {% endif %}
        </div>

        <div class="card-body">
            {% set postUrl = routes('BE_FIELDTRANSLATION_SAVE') %}
            {% if curFieldTranslation.id is not empty %}                
            {% set postUrl = routes('BE_FIELDTRANSLATION_SAVE') + '?fieldTranslationId=' + curFieldTranslation.id %}
            {% endif %}

            <form id="fieldtranslation-edit" class="form-validate" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">
                
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Nome Campo: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="fieldName" type="text" class="form-control form-control-maxlength" placeholder="Nome del campo (es. claimNumber)" value="{{ curFieldTranslation.fieldName }}" required maxlength="100">
                        <div class="form-text text-muted">Nome del campo come definito nel POJO (es. claimNumber, provinceCode).</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Traduzione: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="translation" type="text" class="form-control form-control-maxlength" placeholder="Traduzione italiana (es. Numero Sinistro)" value="{{ curFieldTranslation.translation }}" required maxlength="200">
                        <div class="form-text text-muted">Traduzione italiana user-friendly del campo.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Classe Sorgente:</label>
                    <div class="col-lg-9">
                        <input name="sourceClass" type="text" class="form-control form-control-maxlength" placeholder="Nome della classe POJO (es. WarrantyDetails)" value="{{ curFieldTranslation.sourceClass }}" maxlength="100">
                        <div class="form-text text-muted">Nome della classe POJO dove è definito il campo.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Tipo Campo:</label>
                    <div class="col-lg-9">
                        <input name="fieldType" type="text" class="form-control form-control-maxlength" placeholder="Tipo del campo (es. String, Integer, List<String>)" value="{{ curFieldTranslation.fieldType }}" maxlength="100">
                        <div class="form-text text-muted">Tipo di dato del campo.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Da BasePojo:</label>
                    <div class="col-lg-9">
                        <div class="form-check form-switch">
                            <input name="isFromBasePojo" type="checkbox" class="form-check-input" value="true" {{ curFieldTranslation.isFromBasePojo ? 'checked' : '' }}>
                            <label class="form-check-label">Il campo è definito in BasePojo</label>
                        </div>
                        <div class="form-text text-muted">Indica se il campo è ereditato dalla classe BasePojo.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Generato da AI:</label>
                    <div class="col-lg-9">
                        <div class="form-check form-switch">
                            <input name="isAiGenerated" type="checkbox" class="form-check-input" value="true" {{ curFieldTranslation.isAiGenerated ? 'checked' : '' }}>
                            <label class="form-check-label">Traduzione generata automaticamente da AI</label>
                        </div>
                        <div class="form-text text-muted">Indica se la traduzione è stata generata automaticamente dall'AI.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Note:</label>
                    <div class="col-lg-9">
                        <textarea name="notes" class="form-control" rows="3" placeholder="Note aggiuntive sulla traduzione">{{ curFieldTranslation.notes }}</textarea>
                        <div class="form-text text-muted">Note opzionali sulla traduzione.</div>
                    </div>
                </div>

                <div class="text-end">
                    <a href="{{ routes('BE_FIELDTRANSLATION_COLLECTION') }}" class="btn btn-light">
                        <i class="ph-arrow-left me-2"></i>
                        Torna alla lista
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="ph-check me-2"></i>
                        {% if curFieldTranslation is empty %}
                        Crea Traduzione
                        {% else %}
                        Aggiorna Traduzione
                        {% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
    <!-- /form -->
</div>
<!-- /content area -->

{% endblock %}
