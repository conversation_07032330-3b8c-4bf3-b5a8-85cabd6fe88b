{% extends "be/include/preline-base.html" %}

{% block extrahead %}

    {% set menu = 'CATALOG_COLLECTION' %}

    <title>Catalogo</title>

{% endblock %}

{% block content %}
<div class="lg:ps-65 p-2 sm:p-5 md:pt-5 space-y-5">
    <div class="max-w-6xl mx-auto">
        <!-- Tab -->
            <div class="flex flex-col lg:flex-row bg-white rounded-xl border border-gray-200 shadow-xs dark:bg-neutral-800 dark:bg-neutral-900 dark:border-neutral-700">
                <div class="shrink-0">
                    <div class="h-full lg:w-50 lg:border-e border-gray-200 dark:border-neutral-700">
                        <div class="p-4 lg:p-0">
                            <!-- Select Nav -->
                            <select id="hs-pro-vcnadpn-select" class="hidden" data-hs-select='{
                                    "placeholder": "Select option...",
                                    "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span class=\"me-2\" data-icon></span><span class=\"text-gray-800 dark:text-neutral-200 \" data-title></span></button>",
                                    "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-2 pe-8 flex items-center text-nowrap w-full cursor-pointer bg-gray-100 text-gray-800 rounded-lg text-start text-sm dark:bg-neutral-800 dark:text-neutral-200",
                                    "wrapperClasses": "lg:hidden",
                                    "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-xl overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                                    "optionClasses": "hs-selected:bg-gray-100 dark:hs-selected:bg-neutral-800 py-2 px-3 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                                    "optionTemplate": "<div class=\"flex items-center\"><div class=\"me-3\" data-icon></div><div class=\"text-gray-800 dark:text-neutral-200 \" data-title></div></div>",
                                    "extraMarkup": "<div class=\"absolute top-1/2 end-2 -translate-y-1/2\"><svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500 \" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m7 15 5 5 5-5\"/><path d=\"m7 9 5-5 5 5\"/></svg></div>"
                                    }'>
                                <option value="#catalog-collection-all" data-hs-select-option='{
                                        "icon": "<svg class=\"shrink-0 size-4\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><rect width=\"7\" height=\"9\" x=\"3\" y=\"3\" rx=\"1\"/><rect width=\"7\" height=\"5\" x=\"14\" y=\"3\" rx=\"1\"/><rect width=\"7\" height=\"9\" x=\"14\" y=\"12\" rx=\"1\"/><rect width=\"7\" height=\"5\" x=\"3\" y=\"16\" rx=\"1\"/></svg>"
                                        }' selected>Tutti</option>
                                <option value="#catalog-collection-vehicles" data-hs-select-option='{
                                        "icon": "<svg class=\"shrink-0 size-4\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><circle cx=\"18.5\" cy=\"17.5\" r=\"3.5\"/><circle cx=\"5.5\" cy=\"17.5\" r=\"3.5\"/><circle cx=\"15\" cy=\"5\" r=\"1\"/><path d=\"M12 17.5V14l-3-3 4-3 2 3h2\"/></svg>"
                                        }'>Veicoli</option>
                            </select>
                            <!-- End Select Nav -->
                        </div>

                        <!-- Nav Tab -->
                        <nav id="hs-pro-vcnadpn-tabs" class="p-4 hidden lg:flex flex-col gap-1" aria-label="Tabs" role="tablist" aria-orientation="horizontal">
                            <button type="button" class="hs-tab-active:bg-gray-100 hs-tab-active:hover:bg-gray-100 hs-tab-active:focus:bg-gray-100 hs-tab-active:text-gray-800 dark:hs-tab-active:bg-neutral-700 dark:hs-tab-active:focus:bg-neutral-700 dark:hs-tab-active:hover:bg-neutral-700 dark:hs-tab-active:text-neutral-200 w-full flex items-center gap-x-2 p-2 text-start text-sm rounded-lg text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden active" id="catalog-collection-all-item" aria-selected="true" data-hs-tab="#catalog-collection-all" aria-controls="catalog-collection-all" role="tab" >
                                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="7" height="9" x="3" y="3" rx="1"/><rect width="7" height="5" x="14" y="3" rx="1"/><rect width="7" height="9" x="14" y="12" rx="1"/><rect width="7" height="5" x="3" y="16" rx="1"/></svg>Tutti
                            </button>
                            <button type="button" class="hs-tab-active:bg-gray-100 hs-tab-active:hover:bg-gray-100 hs-tab-active:focus:bg-gray-100 hs-tab-active:text-gray-800 dark:hs-tab-active:bg-neutral-700 dark:hs-tab-active:focus:bg-neutral-700 dark:hs-tab-active:hover:bg-neutral-700 dark:hs-tab-active:text-neutral-200 w-full flex items-center gap-x-2 p-2 text-start text-sm rounded-lg text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden " id="catalog-collection-vehicles-item" aria-selected="false" data-hs-tab="#catalog-collection-vehicles" aria-controls="catalog-collection-vehicles" role="tab" >
                                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="18.5" cy="17.5" r="3.5"/><circle cx="5.5" cy="17.5" r="3.5"/><circle cx="15" cy="5" r="1"/><path d="M12 17.5V14l-3-3 4-3 2 3h2"/></svg>Veicoli
                            </button>
                        </nav>
                        <!-- End Nav Tab -->
                    </div>
                </div>

                <div class="grow">
                    <!-- Tab Content -->
                    <div id="catalog-collection-all" role="tabpanel" aria-labelledby="catalog-collection-all-item">
                        
                        <div class="p-5">
                            <!-- Grid -->
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                <!-- Card -->
                                <div class="p-4 group relative flex flex-col border border-gray-200 bg-white hover:border-gray-300 dark:bg-neutral-800 dark:border-neutral-700/50 dark:hover:border-neutral-700 rounded-lg">
                                    <div class="h-full flex gap-x-5">
                                        <div class="shrink-0 size-8">                                            
                                            <img class="shrink-0 size-8" src="{{ contextPath }}/img/brands/harley.svg" alt="Avatar" width="32" height="32">
                                        </div>

                                        <div class="grow">
                                            <div class="h-full flex flex-col">
                                                <div>
                                                    <h3 class="inline-flex items-center gap-x-1 font-medium text-gray-800 dark:text-neutral-200">
                                                        Harley Davidson Globale
                                                    </h3>                                                    
                                                    <p class="mt-1 text-sm text-gray-500 dark:text-neutral-500">
                                                        Polizza moto per Harley Davidson e Buell e tutte le garanzie selezionabili.
                                                    </p>
                                                </div>

                                                <div class="pt-1 mt-auto">
                                                    <span class="inline-flex items-center gap-x-2 text-sm font-medium group-disabled:opacity-50 group-disabled:pointer-events-none text-gray-800 group-hover:text-gray-900 group-hover:underline group-hover:decoration-2 dark:text-neutral-200 dark:group-hover:text-neutral-100">
                                                        Inizia quotazione
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <a class="after:absolute after:inset-0 after:z-10" href="#"></a>
                                </div>
                                <!-- End Card -->

                                <!-- Card -->
                                <div class="p-4 group relative flex flex-col border border-gray-200 bg-white dark:bg-neutral-800 dark:border-neutral-700/50bg-gray-50 opacity-50 pointer-events-none dark:bg-neutral-800 dark:border-neutral-700/50 rounded-lg">
                                    <div class="h-full flex gap-x-5">
                                        <div class="shrink-0 size-8">
                                            <div class="flex justify-center items-center size-8 rounded-full bg-gray-200 text-gray-500 dark:bg-neutral-700 dark:text-neutral-500"><svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 6 9 17l-5-5"/></svg></div>
                                        </div>

                                        <div class="grow">
                                            <div class="h-full flex flex-col">
                                                <div>
                                                    <h3 class="inline-flex items-center gap-x-1 font-medium text-gray-800 dark:text-neutral-200">
                                                        Notion
                                                    </h3>

                                                    <p class="mt-1 text-sm text-gray-500 dark:text-neutral-500">
                                                        Connect your Notion account to work seamlessly across Notion & Preline.
                                                    </p>
                                                </div>

                                                <div class="pt-1 mt-auto">
                                                    <span class="inline-flex items-center gap-x-2 text-sm font-medium group-disabled:opacity-50 group-disabled:pointer-events-none text-gray-600 dark:text-neutral-400text-gray-600 dark:text-neutral-400">
                                                        Installed
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- End Card -->

                                <!-- Card -->
                                <div class="p-4 group relative flex flex-col border border-gray-200 bg-white hover:border-gray-300 dark:bg-neutral-800 dark:border-neutral-700/50 dark:hover:border-neutral-700 rounded-lg">
                                    <div class="h-full flex gap-x-5">
                                        <div class="shrink-0 size-8">
                                            <svg class="shrink-0 size-8 text-gray-500 dark:text-neutral-500" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16.0001 0C7.16461 0 0 7.34466 0 16.405C0 23.6533 4.5845 29.8026 10.9419 31.9718C11.7415 32.1236 12.0351 31.6159 12.0351 31.1826C12.0351 30.7915 12.0202 29.4991 12.0134 28.1283C7.56202 29.1207 6.62276 26.1927 6.62276 26.1927C5.89494 24.2965 4.84626 23.7924 4.84626 23.7924C3.39464 22.7742 4.95568 22.795 4.95568 22.795C6.5624 22.9108 7.40843 24.4856 7.40843 24.4856C8.83545 26.9936 11.1514 26.2685 12.0645 25.8495C12.208 24.789 12.6227 24.0654 13.0803 23.6558C9.5265 23.2408 5.79054 21.8342 5.79054 15.5483C5.79054 13.7573 6.41559 12.2938 7.43917 11.1449C7.27303 10.7317 6.72541 9.0632 7.59415 6.80351C7.59415 6.80351 8.93772 6.36259 11.9953 8.48512C13.2715 8.12152 14.6403 7.93934 16.0001 7.93316C17.3598 7.93934 18.7296 8.12152 20.0083 8.48512C23.0623 6.36259 24.404 6.80351 24.404 6.80351C25.2748 9.0632 24.727 10.7317 24.5608 11.1449C25.5867 12.2938 26.2075 13.7572 26.2075 15.5483C26.2075 21.8491 22.4645 23.2366 18.9017 23.6426C19.4755 24.1518 19.9869 25.1502 19.9869 26.6806C19.9869 28.8756 19.9683 30.6422 19.9683 31.1826C19.9683 31.6192 20.2563 32.1307 21.0674 31.9696C27.4213 29.798 32 23.6509 32 16.405C32 7.34466 24.8364 0 16.0001 0Z" class="fill-black dark:fill-neutral-200" fill="currentColor"/><path d="M5.99251 23.3693C5.95737 23.4508 5.83213 23.4752 5.71832 23.4194C5.60224 23.3658 5.53699 23.2547 5.57464 23.1728C5.60915 23.089 5.73438 23.0655 5.8502 23.1219C5.96653 23.1753 6.03279 23.2875 5.99251 23.3693ZM6.77955 24.0893C6.70326 24.1619 6.55405 24.1282 6.45279 24.0135C6.34813 23.8991 6.32856 23.7463 6.40598 23.6726C6.48466 23.6001 6.62935 23.634 6.73425 23.7485C6.83891 23.8641 6.85924 24.0161 6.77943 24.0894M7.31952 25.0105C7.22139 25.0804 7.06102 25.0149 6.96201 24.869C6.864 24.7232 6.864 24.5482 6.96414 24.4781C7.06353 24.408 7.22139 24.471 7.32178 24.6158C7.41965 24.7641 7.41965 24.9391 7.31939 25.0107M8.23255 26.0775C8.14484 26.1766 7.95811 26.1501 7.82133 26.0147C7.68154 25.8825 7.64252 25.6947 7.73048 25.5955C7.8192 25.4962 8.00705 25.5241 8.14484 25.6583C8.28375 25.7903 8.32604 25.9795 8.23255 26.0775ZM9.41262 26.4378C9.3741 26.5662 9.19415 26.6246 9.01295 26.57C8.832 26.5138 8.71354 26.3633 8.75006 26.2335C8.7877 26.1041 8.9684 26.0433 9.15098 26.1017C9.33168 26.1577 9.45027 26.307 9.41262 26.4378ZM10.7558 26.5905C10.7603 26.7258 10.6066 26.838 10.4164 26.8405C10.225 26.8447 10.0703 26.7352 10.0683 26.6022C10.0683 26.4656 10.2185 26.3544 10.4097 26.3512C10.6 26.3473 10.7558 26.456 10.7558 26.5905ZM12.0752 26.5386C12.098 26.6706 11.9658 26.8063 11.7769 26.8423C11.5912 26.877 11.4193 26.7956 11.3955 26.6647C11.3725 26.5294 11.5072 26.3939 11.6926 26.3588C11.8818 26.3251 12.0511 26.4044 12.0752 26.5386Z" class="fill-black dark:fill-neutral-200" fill="currentColor"/></svg>
                                        </div>

                                        <div class="grow">
                                            <div class="h-full flex flex-col">
                                                <div>
                                                    <h3 class="inline-flex items-center gap-x-1 font-medium text-gray-800 dark:text-neutral-200">
                                                        Github
                                                    </h3>

                                                    <p class="mt-1 text-sm text-gray-500 dark:text-neutral-500">
                                                        Link git pull requests, branches, and commits to Preline.
                                                    </p>
                                                </div>

                                                <div class="pt-1 mt-auto">
                                                    <span class="inline-flex items-center gap-x-2 text-sm font-medium group-disabled:opacity-50 group-disabled:pointer-events-none text-blue-600 group-hover:text-blue-700 group-hover:underline group-hover:decoration-2 dark:text-blue-500 dark:group-hover:text-blue-400">
                                                        Install now
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <a class="after:absolute after:inset-0 after:z-10" href="#"></a>
                                </div>
                                <!-- End Card -->

                                <!-- Card -->
                                <div class="p-4 group relative flex flex-col border border-gray-200 bg-white hover:border-gray-300 dark:bg-neutral-800 dark:border-neutral-700/50 dark:hover:border-neutral-700 rounded-lg">
                                    <div class="h-full flex gap-x-5">
                                        <div class="shrink-0 size-8">
                                            <svg class="shrink-0 size-8" width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_11766_122209asdasd)"><path d="M3.11931 28.4817H8.21019V16.1181L0.937439 10.6636V26.3C0.937439 27.5054 1.91381 28.4819 3.11931 28.4819V28.4817Z" fill="#4285F4"/><path d="M25.6647 28.4817H30.7556C31.961 28.4817 32.9374 27.5054 32.9374 26.2999V10.6636L25.6647 16.1181V28.4817Z" fill="#34A853"/><path d="M25.6647 6.66356V16.1181L32.9374 10.6636V7.7545C32.9374 5.05812 29.8593 3.51812 27.701 5.13631L25.6647 6.66356Z" fill="#FBBC04"/><path fill-rule="evenodd" clip-rule="evenodd" d="M8.21021 16.1181V6.66356L16.9375 13.2091L25.6647 6.66356V16.1181L16.9375 22.6636L8.21021 16.1181Z" fill="#EA4335"/><path d="M0.937439 7.7545V10.6636L8.21019 16.1181V6.66356L6.17381 5.13631C4.01556 3.51813 0.937439 5.05813 0.937439 7.75438V7.7545Z" fill="#C5221F"/></g><defs><clipPath id="clip0_11766_122209asdasd"><rect width="32" height="32" fill="white" transform="translate(0.937439)"/></clipPath></defs></svg>
                                        </div>

                                        <div class="grow">
                                            <div class="h-full flex flex-col">
                                                <div>
                                                    <h3 class="inline-flex items-center gap-x-1 font-medium text-gray-800 dark:text-neutral-200">
                                                        Gmail
                                                    </h3>

                                                    <p class="mt-1 text-sm text-gray-500 dark:text-neutral-500">
                                                        Create and share Preline right from your inbox.
                                                    </p>
                                                </div>

                                                <div class="pt-1 mt-auto">
                                                    <span class="inline-flex items-center gap-x-2 text-sm font-medium group-disabled:opacity-50 group-disabled:pointer-events-none text-blue-600 group-hover:text-blue-700 group-hover:underline group-hover:decoration-2 dark:text-blue-500 dark:group-hover:text-blue-400">
                                                        Install now
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <a class="after:absolute after:inset-0 after:z-10" href="#"></a>
                                </div>
                                <!-- End Card -->

                                <!-- Card -->
                                <div class="p-4 group relative flex flex-col border border-gray-200 bg-white hover:border-gray-300 dark:bg-neutral-800 dark:border-neutral-700/50 dark:hover:border-neutral-700 rounded-lg">
                                    <div class="h-full flex gap-x-5">
                                        <div class="shrink-0 size-8">
                                            <svg class="shrink-0 size-8" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_11766_122079)"><path d="M16 32C7.16 32 0 24.84 0 16C0 7.16 7.16 0 16 0C24.84 0 32 7.16 32 16C32 24.84 24.84 32 16 32Z" fill="#FFE01B"/><path d="M11.72 19.28C11.74 19.3 11.74 19.34 11.72 19.38C11.64 19.52 11.48 19.6 11.32 19.58C11.02 19.54 10.8 19.3 10.82 19C10.82 18.8 10.86 18.62 10.92 18.42C11.02 18.18 10.92 17.92 10.72 17.78C10.6 17.7 10.44 17.68 10.3 17.7C10.16 17.72 10.04 17.82 9.96001 17.94C9.90001 18.04 9.86001 18.14 9.84001 18.24C9.84001 18.26 9.82001 18.28 9.82001 18.28C9.78001 18.4 9.70001 18.44 9.64001 18.44C9.62001 18.44 9.58001 18.42 9.56001 18.36C9.50001 18.02 9.62001 17.68 9.84001 17.42C10.04 17.2 10.32 17.1 10.62 17.14C10.92 17.18 11.2 17.38 11.32 17.66C11.46 18 11.42 18.38 11.24 18.7C11.22 18.72 11.22 18.76 11.2 18.78C11.14 18.9 11.12 19.06 11.2 19.18C11.26 19.26 11.34 19.3 11.44 19.3C11.48 19.3 11.52 19.3 11.56 19.28C11.64 19.24 11.7 19.24 11.72 19.28ZM24.94 19.6C24.92 20.22 24.78 20.82 24.56 21.4C23.44 24.1 20.76 25.6 17.56 25.5C14.58 25.42 12.04 23.84 10.94 21.26C10.24 21.24 9.56001 20.96 9.06001 20.5C8.52001 20.04 8.18001 19.4 8.10001 18.7C8.04001 18.22 8.10001 17.74 8.28001 17.28L7.66001 16.76C4.78001 14.36 13.72 4.4 16.56 6.9C16.58 6.92 17.54 7.86 17.54 7.86C17.54 7.86 18.06 7.64 18.08 7.64C20.58 6.6 22.62 7.1 22.62 8.76C22.62 9.62 22.08 10.62 21.2 11.54C21.56 11.9 21.8 12.34 21.92 12.82C22.02 13.16 22.06 13.5 22.08 13.86C22.1 14.22 22.12 15.04 22.12 15.04C22.14 15.04 22.4 15.12 22.48 15.14C23 15.26 23.46 15.48 23.86 15.82C24.08 16.02 24.2 16.3 24.26 16.58C24.32 16.96 24.22 17.34 24 17.64C24.06 17.78 24.1 17.9 24.16 18.04C24.24 18.28 24.28 18.48 24.3 18.5C24.52 18.54 24.94 18.86 24.94 19.6ZM12.34 18.12C12.14 16.86 11.3 16.42 10.72 16.38C10.58 16.38 10.44 16.38 10.28 16.42C9.26001 16.62 8.66001 17.5 8.78001 18.64C8.96001 19.7 9.82001 20.5 10.88 20.56C10.98 20.56 11.08 20.56 11.18 20.54C12.24 20.38 12.5 19.24 12.34 18.12ZM14.1 10.12C14.98 9.4 15.9 8.76 16.88 8.2C16.88 8.2 16.1 7.3 15.86 7.22C14.42 6.82 11.3 8.98 9.30001 11.84C8.50001 13 7.34001 15.04 7.90001 16.08C8.10001 16.32 8.32001 16.52 8.56001 16.72C8.92001 16.2 9.48001 15.84 10.12 15.72C10.9 13.54 12.28 11.6 14.1 10.12ZM17.22 20.1C17.3 20.44 17.56 20.72 17.9 20.8C18.08 20.86 18.24 20.92 18.44 20.94C20.72 21.34 22.86 20.02 23.34 19.7C23.38 19.68 23.4 19.7 23.38 19.74C23.36 19.76 23.34 19.78 23.34 19.8C22.76 20.56 21.18 21.44 19.12 21.44C18.22 21.44 17.32 21.12 17 20.64C16.48 19.88 16.98 18.78 17.82 18.9C17.82 18.9 18.12 18.94 18.2 18.94C19.52 19.06 20.86 18.86 22.08 18.32C23.24 17.78 23.68 17.18 23.62 16.7C23.6 16.56 23.52 16.42 23.42 16.3C23.1 16.04 22.72 15.86 22.32 15.78C22.14 15.72 22.02 15.7 21.88 15.66C21.64 15.58 21.52 15.52 21.5 15.06C21.48 14.86 21.46 14.18 21.44 13.88C21.42 13.38 21.36 12.7 20.94 12.42C20.84 12.34 20.7 12.3 20.58 12.3C20.5 12.3 20.44 12.3 20.36 12.32C20.14 12.36 19.96 12.48 19.8 12.64C19.4 13 18.88 13.18 18.34 13.14C18.04 13.12 17.74 13.08 17.38 13.06C17.32 13.06 17.24 13.06 17.18 13.04C16.22 13.06 15.44 13.78 15.32 14.74C15.12 16.16 16.14 16.88 16.44 17.32C16.48 17.38 16.52 17.44 16.52 17.52C16.52 17.6 16.48 17.68 16.42 17.72C15.6 18.64 15.3 19.92 15.62 21.12C15.66 21.26 15.7 21.4 15.76 21.54C16.5 23.28 18.82 24.1 21.08 23.36C21.38 23.26 21.66 23.14 21.94 23C22.44 22.76 22.88 22.42 23.26 22.02C23.84 21.44 24.22 20.68 24.36 19.86C24.42 19.4 24.32 19.24 24.2 19.16C24.1 19.1 24 19.08 23.88 19.1C23.82 18.74 23.72 18.4 23.58 18.08C22.94 18.56 22.2 18.94 21.42 19.16C20.48 19.42 19.52 19.52 18.54 19.48C17.92 19.42 17.5 19.24 17.34 19.76C18.28 20.08 19.28 20.18 20.28 20.06C20.3 20.06 20.34 20.08 20.34 20.1C20.34 20.12 20.32 20.14 20.3 20.16C20.22 20.14 19.06 20.68 17.22 20.1ZM13.84 11.88C14.6 11.34 15.48 10.96 16.38 10.76C17.42 10.52 18.48 10.52 19.52 10.76C19.56 10.76 19.58 10.7 19.54 10.68C19 10.4 18.42 10.24 17.8 10.22C17.78 10.22 17.76 10.2 17.76 10.18V10.16C17.86 10.04 17.96 9.92 18.08 9.84C18.1 9.82 18.1 9.8 18.08 9.8L18.06 9.78C17.32 9.86 16.62 10.1 15.98 10.52C15.96 10.52 15.94 10.52 15.94 10.52V10.5C15.98 10.32 16.06 10.14 16.16 9.96C16.16 9.94 16.16 9.92 16.14 9.92H16.12C15.22 10.42 14.42 11.08 13.76 11.86C13.74 11.88 13.74 11.9 13.76 11.9C13.8 11.9 13.82 11.9 13.84 11.88ZM19.84 16.7C19.96 16.78 20.14 16.76 20.24 16.64C20.3 16.52 20.22 16.38 20.06 16.3C19.94 16.22 19.76 16.24 19.66 16.36C19.6 16.46 19.68 16.62 19.84 16.7ZM20.34 14.88C20.38 15.08 20.46 15.28 20.58 15.44C20.7 15.42 20.84 15.42 20.96 15.44C21.04 15.22 21.04 14.98 20.98 14.76C20.88 14.34 20.76 14.1 20.52 14.14C20.26 14.18 20.24 14.48 20.34 14.88ZM20.88 15.84C20.72 15.8 20.54 15.88 20.48 16.06C20.44 16.22 20.52 16.4 20.7 16.46C20.88 16.52 21.04 16.42 21.1 16.24C21.1 16.22 21.12 16.18 21.12 16.16C21.12 16 21.02 15.86 20.88 15.84Z" fill="black"/><path d="M16.66 15.8C16.62 15.8 16.6 15.78 16.6 15.76C16.58 15.68 16.7 15.58 16.8 15.48C17.14 15.22 17.6 15.18 17.98 15.34C18.16 15.42 18.32 15.54 18.42 15.7C18.46 15.76 18.46 15.82 18.44 15.84C18.4 15.88 18.3 15.84 18.12 15.76C17.92 15.66 17.68 15.6 17.46 15.62C17.2 15.66 16.92 15.72 16.66 15.8ZM18.38 16.16C18.22 16 18 15.92 17.8 15.96C17.64 15.98 17.5 16.04 17.38 16.14C17.32 16.18 17.28 16.24 17.28 16.32C17.28 16.34 17.28 16.36 17.3 16.36C17.32 16.36 17.32 16.38 17.34 16.38C17.4 16.38 17.46 16.36 17.5 16.34C17.74 16.26 17.98 16.22 18.22 16.26C18.34 16.28 18.38 16.28 18.42 16.24C18.4 16.2 18.4 16.18 18.38 16.16Z" fill="black"/></g><defs><clipPath id="clip0_11766_122079"><rect width="32" height="32" fill="white"/></clipPath></defs></svg>
                                        </div>

                                        <div class="grow">
                                            <div class="h-full flex flex-col">
                                                <div>
                                                    <h3 class="inline-flex items-center gap-x-1 font-medium text-gray-800 dark:text-neutral-200">
                                                        Mailchimp
                                                    </h3>

                                                    <p class="mt-1 text-sm text-gray-500 dark:text-neutral-500">
                                                        Send email newsletters and manage subscribers in Mailchimp.
                                                    </p>
                                                </div>

                                                <div class="pt-1 mt-auto">
                                                    <span class="inline-flex items-center gap-x-2 text-sm font-medium group-disabled:opacity-50 group-disabled:pointer-events-none text-blue-600 group-hover:text-blue-700 group-hover:underline group-hover:decoration-2 dark:text-blue-500 dark:group-hover:text-blue-400">
                                                        Install now
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <a class="after:absolute after:inset-0 after:z-10" href="#"></a>
                                </div>
                                <!-- End Card -->

                                <!-- Card -->
                                <div class="p-4 group relative flex flex-col border border-gray-200 bg-white dark:bg-neutral-800 dark:border-neutral-700/50 rounded-lg">
                                    <div class="h-full flex gap-x-5">
                                        <div class="shrink-0 size-8">
                                            <svg class="shrink-0 size-8" width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M32.8875 15.3054C32.9242 16.2093 32.8209 17.1099 32.5811 17.9792C32.3447 18.8486 31.9716 19.6695 31.4787 20.4141C30.989 21.1593 30.3861 21.8167 29.6935 22.3607L29.6769 22.3745L23.019 27.563L19.7451 30.1433L17.7501 31.7089C17.6335 31.8024 17.5036 31.8716 17.3671 31.9201C17.2305 31.9686 17.084 31.9929 16.9374 31.9929C16.7942 31.9929 16.6477 31.9686 16.5111 31.9201C16.3745 31.8716 16.2447 31.8024 16.1281 31.7089L14.1331 30.1433L10.8591 27.563L4.24125 22.4057L4.20129 22.378L4.18796 22.3641C3.49187 21.8203 2.88904 21.1623 2.39611 20.4176C1.90319 19.6729 1.53016 18.8486 1.29036 17.9792C1.05056 17.1099 0.947313 16.2059 0.98395 15.3019C1.02392 14.3979 1.20044 13.5078 1.51018 12.6626L1.55348 12.5414L5.90654 0.747936C5.92875 0.69021 5.95539 0.634792 5.98648 0.581684C6.01534 0.528576 6.04976 0.478931 6.08972 0.43275C6.12747 0.38426 6.16855 0.339234 6.21295 0.297671C6.25736 0.258417 6.30399 0.221472 6.35284 0.186836C6.45609 0.121028 6.56267 0.0725381 6.67924 0.0448295C6.79248 0.0136573 6.91238 -0.000196993 7.02895 0.00673016C7.14885 0.0136573 7.26542 0.0379024 7.37533 0.0829289C7.48524 0.124492 7.59181 0.186836 7.68507 0.263035C7.72948 0.302289 7.77278 0.343852 7.81496 0.387724C7.85493 0.433905 7.89046 0.483549 7.92154 0.536658C7.95485 0.587457 7.98371 0.641719 8.00814 0.699446C8.03256 0.754863 8.05254 0.812589 8.06809 0.872625L11.0023 10.2139H22.8792L25.8134 0.872625C25.8289 0.812589 25.85 0.754863 25.8767 0.699446C25.9011 0.644029 25.93 0.589766 25.9633 0.536658C25.9944 0.485858 26.0299 0.437368 26.0699 0.391187C26.1098 0.345006 26.1531 0.302289 26.1997 0.263035C26.293 0.186836 26.3962 0.127955 26.5062 0.0829289C26.6194 0.0413659 26.736 0.0171209 26.8525 0.0101937C26.9724 0.00326659 27.089 0.0136573 27.2056 0.0448295C27.3188 0.0760017 27.4287 0.124492 27.5286 0.1903C27.5797 0.222627 27.6275 0.259571 27.6719 0.301134C27.7163 0.340388 27.7573 0.38426 27.7951 0.43275C27.8328 0.48124 27.8673 0.532039 27.8983 0.585148C27.9272 0.638256 27.9527 0.693673 27.9749 0.751399L32.3213 12.5483L32.3646 12.6696C32.6744 13.5112 32.8509 14.4014 32.8875 15.3054Z" fill="#E24329"/><path d="M32.8909 15.309C32.9275 16.2095 32.8243 17.1135 32.5845 17.9829C32.3447 18.8523 31.9717 19.6766 31.4787 20.4213C30.9858 21.1659 30.383 21.824 29.6902 22.3678L29.6736 22.3816L23.0157 27.5701C23.0157 27.5701 20.1881 25.3499 16.9374 22.7903L26.4795 15.2813C26.9092 14.9453 27.3588 14.6371 27.8218 14.3531C28.2847 14.0656 28.7643 13.8093 29.2539 13.5807C29.7468 13.3521 30.2498 13.1477 30.7593 12.978C31.2722 12.8048 31.7918 12.6628 32.3214 12.5485L32.3647 12.6698C32.6744 13.5149 32.8509 14.405 32.8909 15.309Z" fill="#FC6D26"/><path d="M16.9374 22.7903C20.1881 25.343 23.0191 27.5701 23.0191 27.5701L19.7451 30.1504L17.7501 31.716C17.6335 31.8095 17.5036 31.8788 17.3671 31.9273C17.2305 31.9758 17.084 32 16.9374 32C16.7942 32 16.6477 31.9758 16.5111 31.9273C16.3746 31.8788 16.2447 31.8095 16.1281 31.716L14.1331 30.1504L10.8591 27.5701C10.8591 27.5701 13.6868 25.343 16.9374 22.7903Z" fill="#FCA326"/><path d="M16.9374 22.7834C13.6834 25.343 10.8591 27.5631 10.8591 27.5631L4.24125 22.4059L4.20129 22.3782L4.18796 22.3643C3.49187 21.8205 2.88904 21.1625 2.39611 20.4178C1.90319 19.6731 1.53016 18.8488 1.29036 17.9794C1.05056 17.1101 0.947313 16.2061 0.98395 15.3021C1.02392 14.3981 1.20044 13.5079 1.51018 12.6628L1.55348 12.5416C2.08304 12.6559 2.60261 12.7979 3.11552 12.9711C3.6251 13.1443 4.12801 13.3452 4.62094 13.5772C5.11053 13.8058 5.59014 14.0656 6.05309 14.3496C6.51604 14.6336 6.96233 14.9453 7.39531 15.2813L16.9374 22.7834Z" fill="#FC6D26"/></svg>
                                        </div>

                                        <div class="grow">
                                            <div class="h-full flex flex-col">
                                                <div>
                                                    <h3 class="inline-flex items-center gap-x-1 font-medium text-gray-800 dark:text-neutral-200">
                                                        Gitlab
                                                    </h3>

                                                    <p class="mt-1 text-sm text-gray-500 dark:text-neutral-500">
                                                        Link git merge requests, branches, and commits to Preline.
                                                    </p>
                                                </div>

                                                <div class="pt-1 mt-auto">
                                                    <span class="inline-flex items-center gap-x-2 text-sm font-medium group-disabled:opacity-50 group-disabled:pointer-events-none text-gray-600 dark:text-neutral-400">
                                                        Coming soon
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- End Card -->
                            </div>
                            <!-- End Grid -->
                        </div>
                        
                        <!-- Empty State -->
                        <div class="p-5  flex flex-col justify-center items-center text-center">
                            <svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800"/>
                                <rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-50 dark:stroke-neutral-700/10"/>
                                <rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/>
                                <rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/>
                                <rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/>
                                <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800"/>
                                <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/30"/>
                                <rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/>
                                <rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/>
                                <rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/>
                                <g filter="url(#filter5)">
                                    <rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor" class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges"/>
                                    <rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges"/>
                                    <rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700 "/>
                                    <rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700"/>
                                    <rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700"/>
                                </g>
                                <defs>
                                    <filter id="filter5" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                                        <feOffset dy="6"/>
                                        <feGaussianBlur stdDeviation="6"/>
                                        <feComposite in2="hardAlpha" operator="out"/>
                                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0"/>
                                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1187_14810"/>
                                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810" result="shape"/>
                                    </filter>
                                </defs>
                            </svg>

                            <div class="max-w-sm mx-auto">
                                <p class="mt-2 font-medium text-gray-800 dark:text-neutral-200">
                                    Nessun prodotto disponibile
                                </p>
                                <p class="mb-5 text-sm text-gray-500 dark:text-neutral-500">
                                    Per questa categoria non sono ancora stati inseriti prodotti.
                                </p>
                            </div>

                        </div>
                        <!-- End Empty State -->
                    </div>
                    <!-- End Tab Content -->

                    <!-- Tab Content -->
                    <div id="catalog-collection-vehicles" class="hidden" role="tabpanel" aria-labelledby="catalog-collection-vehicles-item">

                        <div class="p-5 flex flex-col gap-4">

                            <!-- Card -->
                            <div class="p-4 bg-white border border-gray-200 rounded-xl shadow-2xs dark:bg-neutral-900 dark:border-neutral-700 w-full">
                                <!-- Header con titolo e prezzo -->
                                <div class="flex items-center justify-between gap-4">

                                    <!-- Titolo con icona a sinistra -->
                                    <div class="flex items-center gap-x-3">
                                        <!-- Logo -->
                                        <img class="inline-block size-15.5 rounded-full" src="{{ contextPath }}/img/brands/harley.svg" alt="Avatar">

                                            <!-- End Logo -->
                                            <div>
                                                <h3 class="font-semibold text-lg text-gray-800 dark:text-neutral-200">
                                                    Harley Davidson RC
                                                </h3>
                                                <p class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Moto
                                                </p>
                                            </div>
                                    </div>

                                    <!-- Prezzo a destra -->
                                    <div class="text-end shrink-0">
                                        <h2 class="text-2xl font-semibold text-gray-800 dark:text-neutral-200">
                                            €296
                                        </h2>
                                        <p class="text-sm text-gray-500 dark:text-neutral-500">
                                            a partire da
                                        </p>
                                    </div>

                                </div>

                                <!-- Sezione collassabile con dettagli nascosti -->
                                <div id="hs-pro-insurance-details" class="hs-collapse hidden w-full overflow-hidden transition-[height] duration-300" aria-labelledby="hs-pro-insurance-toggle">
                                    <div class="mt-4 pt-4 border-t border-gray-200 dark:border-neutral-700">
                                        <!-- Grid dettagli nascosti -->
                                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-y-4 gap-x-6">

                                            <!-- Col 1: Documenti necessari -->
                                            <div class="flex flex-col gap-y-2">
                                                <div class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Documenti necessari:
                                                </div>
                                                <div class="flex flex-wrap justify-start items-center gap-1">
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                            <polyline points="14,2 14,8 20,8"/>
                                                            <line x1="16" y1="13" x2="8" y2="13"/>
                                                            <line x1="16" y1="17" x2="8" y2="17"/>
                                                            <line x1="10" y1="9" x2="8" y2="9"/>
                                                        </svg>
                                                        Carta d'identità
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                            <polyline points="14,2 14,8 20,8"/>
                                                            <line x1="16" y1="13" x2="8" y2="13"/>
                                                            <line x1="16" y1="17" x2="8" y2="17"/>
                                                            <line x1="10" y1="9" x2="8" y2="9"/>
                                                        </svg>
                                                        Libretto
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                            <polyline points="14,2 14,8 20,8"/>
                                                            <line x1="16" y1="13" x2="8" y2="13"/>
                                                            <line x1="16" y1="17" x2="8" y2="17"/>
                                                            <line x1="10" y1="9" x2="8" y2="9"/>
                                                        </svg>
                                                        Passaggio di proprietà
                                                    </div>
                                                </div>
                                            </div>


                                            <!-- Col 2: Brand compatibili -->
                                            <div class="flex flex-col gap-y-2">
                                                <div class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Brand compatibili:
                                                </div>
                                                <div class="flex flex-wrap justify-start items-center gap-1">
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <img src="{{ contextPath }}/img/brands/harley.svg" class="shrink-0 size-4" width="32" height="32">
                                                            Harley Davidson
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <img src="{{ contextPath }}/img/brands/ducati.svg" class="shrink-0 size-4" width="32" height="32">
                                                            Ducati
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <img src="{{ contextPath }}/img/brands/triumph.svg" class="shrink-0 size-4" width="32" height="32">
                                                            Triumph
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Col: Garanzie previste -->
                                            <div class="flex flex-col gap-y-2">
                                                <div class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Garanzie previste:
                                                </div>
                                                <div class="flex flex-wrap justify-start items-center gap-1">
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"/>
                                                        </svg>
                                                        RC
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                                                            <circle cx="9" cy="7" r="4"/>
                                                            <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
                                                            <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                                                        </svg>
                                                        Infortuni
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z"/>
                                                        </svg>
                                                        Furto incendio
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"/>
                                                        </svg>
                                                        Tutela legale
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/>
                                                            <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/>
                                                        </svg>
                                                        Assistenza stradale
                                                    </div>
                                                </div>
                                            </div>

                                        </div>

                                        <!-- Descrizione completa -->
                                        <div class="mt-4">
                                            <div class="text-sm text-gray-500 dark:text-neutral-500 mb-2">
                                                Descrizione:
                                            </div>
                                            <p class="text-sm text-gray-800 dark:text-neutral-200 leading-relaxed">
                                                La polizza moto completa offre la massima protezione per il tuo veicolo. Include copertura RC obbligatoria,
                                                protezione contro furto e incendio, e copertura kasko per tutti i danni. Ideale per moto di alta gamma e
                                                per chi desidera la massima tranquillità. Preventivo veloce in soli 2 minuti con possibilità di personalizzazione
                                                delle franchigie e massimali.
                                            </p>
                                        </div>

                                        <!-- Informazioni aggiuntive -->
                                        <div class="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-4">
                                            <div class="flex flex-col gap-y-0.5">
                                                <div class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Durata polizza:
                                                </div>
                                                <p class="text-sm text-gray-800 dark:text-neutral-200">
                                                    12 mesi (rinnovabile)
                                                </p>
                                            </div>
                                            <div class="flex flex-col gap-y-0.5">
                                                <div class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Tempo preventivo:
                                                </div>
                                                <p class="text-sm font-medium text-green-600 dark:text-green-400">
                                                    Solo 2 minuti
                                                </p>
                                            </div>
                                        </div>

                                    </div>
                                </div>

                                <!-- Footer con CTA e View Details -->
                                <div class="pt-3 mt-4 border-t border-gray-200 dark:border-neutral-700">
                                    <div class="flex flex-wrap justify-between items-center gap-3">

                                        <!-- View Details Button -->
                                        <button type="button" class="hs-collapse-toggle inline-flex justify-center items-center gap-x-1 text-sm text-blue-600 decoration-2 hover:underline focus:outline-hidden focus:underline dark:text-blue-400 dark:hover:text-blue-500" id="hs-pro-insurance-toggle" aria-expanded="false" aria-controls="hs-pro-insurance-details" data-hs-collapse="#hs-pro-insurance-details">
                                            <span class="hs-collapse-open:hidden">Vedi dettagli</span>
                                            <i data-lucide="chevron-down" class="hs-collapse-open:hidden size-4"></i>
                                            <span class="hs-collapse-open:block hidden">Nascondi dettagli</span>
                                            <i data-lucide="chevron-up" class="hs-collapse-open:block hidden size-4"></i>
                                        </button>

                                        <!-- CTA Button -->
                                        <button type="button" class="py-1.5 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:ring-2 focus:ring-blue-500">
                                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-play-icon lucide-circle-play"><path d="M9 9.003a1 1 0 0 1 1.517-.859l4.997 2.997a1 1 0 0 1 0 1.718l-4.997 2.997A1 1 0 0 1 9 14.996z"/><circle cx="12" cy="12" r="10"/></svg>
                                            Nuova quotazione
                                        </button>


                                    </div>
                                </div>

                            </div>
                            <!-- End Card -->

                            <!-- Card -->
                            <div class="p-4 bg-white border border-gray-200 rounded-xl shadow-2xs dark:bg-neutral-900 dark:border-neutral-700 w-full">
                                <!-- Header con titolo e prezzo -->
                                <div class="flex items-center justify-between gap-4">

                                    <!-- Titolo con icona a sinistra -->
                                    <div class="flex items-center gap-x-3">
                                        <!-- Logo -->
                                        <img class="inline-block size-15.5 rounded-full" src="{{ contextPath }}/img/brands/harley.svg" alt="Avatar">

                                            <!-- End Logo -->
                                            <div>
                                                <h3 class="font-semibold text-lg text-gray-800 dark:text-neutral-200">
                                                    Harley Davidson Assistenza
                                                </h3>
                                                <p class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Moto
                                                </p>
                                            </div>
                                    </div>

                                    <!-- Prezzo a destra -->
                                    <div class="text-end shrink-0">
                                        <h2 class="text-2xl font-semibold text-gray-800 dark:text-neutral-200">
                                            €86
                                        </h2>
                                        <p class="text-sm text-gray-500 dark:text-neutral-500">
                                            a partire da
                                        </p>
                                    </div>

                                </div>

                                <!-- Sezione collassabile con dettagli nascosti -->
                                <div id="hs-pro-insurance-details" class="hs-collapse hidden w-full overflow-hidden transition-[height] duration-300" aria-labelledby="hs-pro-insurance-toggle">
                                    <div class="mt-4 pt-4 border-t border-gray-200 dark:border-neutral-700">
                                        <!-- Grid dettagli nascosti -->
                                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-y-4 gap-x-6">

                                            <!-- Col 1: Documenti necessari -->
                                            <div class="flex flex-col gap-y-2">
                                                <div class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Documenti necessari:
                                                </div>
                                                <div class="flex flex-wrap justify-start items-center gap-1">
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                            <polyline points="14,2 14,8 20,8"/>
                                                            <line x1="16" y1="13" x2="8" y2="13"/>
                                                            <line x1="16" y1="17" x2="8" y2="17"/>
                                                            <line x1="10" y1="9" x2="8" y2="9"/>
                                                        </svg>
                                                        Carta d'identità
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                            <polyline points="14,2 14,8 20,8"/>
                                                            <line x1="16" y1="13" x2="8" y2="13"/>
                                                            <line x1="16" y1="17" x2="8" y2="17"/>
                                                            <line x1="10" y1="9" x2="8" y2="9"/>
                                                        </svg>
                                                        Libretto
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                            <polyline points="14,2 14,8 20,8"/>
                                                            <line x1="16" y1="13" x2="8" y2="13"/>
                                                            <line x1="16" y1="17" x2="8" y2="17"/>
                                                            <line x1="10" y1="9" x2="8" y2="9"/>
                                                        </svg>
                                                        Passaggio di proprietà
                                                    </div>
                                                </div>
                                            </div>


                                            <!-- Col 2: Brand compatibili -->
                                            <div class="flex flex-col gap-y-2">
                                                <div class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Brand compatibili:
                                                </div>
                                                <div class="flex flex-wrap justify-start items-center gap-1">
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <img src="{{ contextPath }}/img/brands/harley.svg" class="shrink-0 size-4" width="32" height="32">
                                                            Harley Davidson
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <img src="{{ contextPath }}/img/brands/ducati.svg" class="shrink-0 size-4" width="32" height="32">
                                                            Ducati
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <img src="{{ contextPath }}/img/brands/triumph.svg" class="shrink-0 size-4" width="32" height="32">
                                                            Triumph
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Col: Garanzie previste -->
                                            <div class="flex flex-col gap-y-2">
                                                <div class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Garanzie previste:
                                                </div>
                                                <div class="flex flex-wrap justify-start items-center gap-1">
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/>
                                                            <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/>
                                                        </svg>
                                                        Assistenza stradale
                                                    </div>
                                                </div>
                                            </div>

                                        </div>

                                        <!-- Descrizione completa -->
                                        <div class="mt-4">
                                            <div class="text-sm text-gray-500 dark:text-neutral-500 mb-2">
                                                Descrizione:
                                            </div>
                                            <p class="text-sm text-gray-800 dark:text-neutral-200 leading-relaxed">
                                                La polizza moto completa offre la massima protezione per il tuo veicolo. Include copertura RC obbligatoria,
                                                protezione contro furto e incendio, e copertura kasko per tutti i danni. Ideale per moto di alta gamma e
                                                per chi desidera la massima tranquillità. Preventivo veloce in soli 2 minuti con possibilità di personalizzazione
                                                delle franchigie e massimali.
                                            </p>
                                        </div>

                                        <!-- Informazioni aggiuntive -->
                                        <div class="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-4">
                                            <div class="flex flex-col gap-y-0.5">
                                                <div class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Durata polizza:
                                                </div>
                                                <p class="text-sm text-gray-800 dark:text-neutral-200">
                                                    12 mesi (rinnovabile)
                                                </p>
                                            </div>
                                            <div class="flex flex-col gap-y-0.5">
                                                <div class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Tempo preventivo:
                                                </div>
                                                <p class="text-sm font-medium text-green-600 dark:text-green-400">
                                                    Solo 2 minuti
                                                </p>
                                            </div>
                                        </div>

                                    </div>
                                </div>

                                <!-- Footer con CTA e View Details -->
                                <div class="pt-3 mt-4 border-t border-gray-200 dark:border-neutral-700">
                                    <div class="flex flex-wrap justify-between items-center gap-3">

                                        <!-- View Details Button -->
                                        <button type="button" class="hs-collapse-toggle inline-flex justify-center items-center gap-x-1 text-sm text-blue-600 decoration-2 hover:underline focus:outline-hidden focus:underline dark:text-blue-400 dark:hover:text-blue-500" id="hs-pro-insurance-toggle" aria-expanded="false" aria-controls="hs-pro-insurance-details" data-hs-collapse="#hs-pro-insurance-details">
                                            <span class="hs-collapse-open:hidden">Vedi dettagli</span>
                                            <i data-lucide="chevron-down" class="hs-collapse-open:hidden size-4"></i>
                                            <span class="hs-collapse-open:block hidden">Nascondi dettagli</span>
                                            <i data-lucide="chevron-up" class="hs-collapse-open:block hidden size-4"></i>
                                        </button>

                                        <!-- CTA Button -->
                                        <button type="button" class="py-1.5 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:ring-2 focus:ring-blue-500">
                                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-play-icon lucide-circle-play"><path d="M9 9.003a1 1 0 0 1 1.517-.859l4.997 2.997a1 1 0 0 1 0 1.718l-4.997 2.997A1 1 0 0 1 9 14.996z"/><circle cx="12" cy="12" r="10"/></svg>
                                            Nuova quotazione
                                        </button>


                                    </div>
                                </div>

                            </div>
                            <!-- End Card -->

                        </div>

                    </div>
                    <!-- End Tab Content -->

                </div>
            </div>
            <!-- End Tab -->
        </div>
    </div>
{% endblock %}
{% block pagescript %}


{% endblock %}
