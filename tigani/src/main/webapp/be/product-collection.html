{% extends "be/include/preline-base.html" %}

{% block extrahead %}

{% set menu = 'MAINTENANCE' %}
{% set submenu = 'PRODUCT_COLLECTION' %}

<title>Prodotti</title>

<!-- Page Libs -->
{% include "be/include/snippets/plugins/datatable.html" %}
{% include "be/include/snippets/plugins/jquery-confirm.html" %}

{% endblock %}

{% block content %}

<div class="lg:ps-65 p-2 sm:p-5 md:pt-5 space-y-5">
    <!-- Card -->
    <div class="bg-white border border-gray-200 shadow-xs rounded-xl dark:bg-neutral-900 dark:border-neutral-700">
        <!-- Header -->
        <div class="py-3 px-5 flex flex-wrap justify-between items-center gap-2 border-b border-gray-200 dark:border-neutral-700">
            <!-- Title -->
            <div class="flex flex-wrap items-center gap-2">
                <h2 class="font-medium text-gray-800 dark:text-neutral-200">
                    Prodotti
                </h2>
            </div>
            <!-- End Title -->
            <!-- Actions -->
            <div class="flex flex-wrap items-center gap-2">
                {% if user.hasPermission('PRODUCT_MANAGEMENT', 'create') %}
                <button type="button" id="new-product-btn" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-gray-800 border border-gray-800 text-white text-sm font-medium rounded-lg shadow-2xs align-middle hover:bg-gray-950 focus:outline-hidden focus:bg-gray-900 dark:bg-white dark:text-neutral-800 dark:hover:bg-neutral-200 dark:focus:bg-neutral-200 disabled:opacity-50 disabled:pointer-events-none">
                    <svg class="block shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
                    Nuovo prodotto
                </button>
                {% endif %}
            </div>
            <!-- End Actions -->
        </div>
        <!-- End Header -->

        <!-- DataTable Content -->
        <div class="flex flex-col">
            <div id="dealer-datatable-container">
                <!-- DataTable Header -->
                <div class="flex flex-wrap items-center gap-2 p-5">
                    <!-- Search -->
                    {% include "be/include/snippets/tables/search.html" %}
                    <!-- Search -->
                    <div class="flex-1 flex items-center justify-start space-x-2 xl:justify-end">
                        <div class="flex flex-wrap md:flex-nowrap items-center gap-2">
                            <!-- Filters -->
                            {% if user.hasPermission('PRODUCT_MANAGEMENT', 'view') %}
                            {% include "be/include/snippets/tables/filters.html" %}
                            {% endif %}
                            <!-- End Filters -->

                            <!-- Export -->
                            {% if user.hasPermission('PRODUCT_MANAGEMENT', 'view') %}
                            {% include "be/include/snippets/tables/export.html" %}
                            {% endif %}
                            <!-- End Export -->
                        </div>
                    </div>
                </div>
                <!-- End DataTable Header -->

                <!-- Table -->
                <div class="overflow-x-auto">
                    <div class="min-w-full inline-block align-middle">
                        <div class="overflow-hidden">
                            <table class="min-w-full">
                                <thead class="border-y border-gray-200 dark:border-neutral-700 bg-gray-50 dark:bg-neutral-800">
                                <tr>
                                    <th scope="col" class="!py-1 px-5 --exclude-from-ordering">
                                        <div class="flex items-center h-5">
                                            <input id="hs-table-search-checkbox-all" type="checkbox" class="border-gray-300 rounded-sm text-blue-600 checked:border-blue-500 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800 cursor-pointer">
                                            <label for="hs-table-search-checkbox-all" class="sr-only">Checkbox</label>
                                        </div>
                                    </th>
                                    <th scope="col" class="!py-1 !px-0 group text-start font-normal focus:outline-hidden">
                                        <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm font-medium text-gray-800 rounded-md hover:border-gray-200 dark:text-neutral-200 dark:hover:border-neutral-700 cursor-pointer">
                                            Protocollo
                                            <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th scope="col" class="!py-1 !px-0 group text-start font-normal focus:outline-hidden">
                                        <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm font-medium text-gray-800 rounded-md hover:border-gray-200 dark:text-neutral-200 dark:hover:border-neutral-700 cursor-pointer">
                                            Sorgente
                                            <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th scope="col" class="!py-1 !px-0 group text-start font-normal focus:outline-hidden">
                                        <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm font-medium text-gray-800 rounded-md hover:border-gray-200 dark:text-neutral-200 dark:hover:border-neutral-700 cursor-pointer">
                                            Inserita da
                                            <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th scope="col" class="!py-1 !px-0 group text-start font-normal focus:outline-hidden">
                                        <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm font-medium text-gray-800 rounded-md hover:border-gray-200 dark:text-neutral-200 dark:hover:border-neutral-700 cursor-pointer">
                                            Contraente
                                            <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th scope="col" class="!py-1 !px-0 group text-start font-normal focus:outline-hidden">
                                        <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm font-medium text-gray-800 rounded-md hover:border-gray-200 dark:text-neutral-200 dark:hover:border-neutral-700 cursor-pointer">
                                            Prodotto
                                            <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th scope="col" class="!py-1 !px-0 group text-start font-normal focus:outline-hidden">
                                        <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm font-medium text-gray-800 rounded-md hover:border-gray-200 dark:text-neutral-200 dark:hover:border-neutral-700 cursor-pointer">
                                            Premio
                                            <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th scope="col" class="!py-1 !px-0 group text-start font-normal focus:outline-hidden">
                                        <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm font-medium text-gray-800 rounded-md hover:border-gray-200 dark:text-neutral-200 dark:hover:border-neutral-700 cursor-pointer">
                                            Quotazione
                                            <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th scope="col" class="!py-1 !px-0 group text-start font-normal focus:outline-hidden">
                                        <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm font-medium text-gray-800 rounded-md hover:border-gray-200 dark:text-neutral-200 dark:hover:border-neutral-700 cursor-pointer">
                                            Pagamento
                                            <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th scope="col" class="!py-1 !px-0 group text-start font-normal focus:outline-hidden">
                                        <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm font-medium text-gray-800 rounded-md hover:border-gray-200 dark:text-neutral-200 dark:hover:border-neutral-700 cursor-pointer">
                                            Emissione
                                            <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th scope="col" class="!py-1 !px-0 group text-start font-normal focus:outline-hidden">
                                        <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm font-medium text-gray-800 rounded-md hover:border-gray-200 dark:text-neutral-200 dark:hover:border-neutral-700 cursor-pointer">
                                            Consegna
                                            <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th scope="col" class="!py-1 !px-0 group text-start font-normal focus:outline-hidden">
                                        <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm font-medium text-gray-800 rounded-md hover:border-gray-200 dark:text-neutral-200 dark:hover:border-neutral-700 cursor-pointer">
                                            Data Creazione
                                            <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th scope="col" class="!py-1 !px-0 group text-start font-normal focus:outline-hidden">
                                        <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm font-medium text-gray-800 rounded-md hover:border-gray-200 dark:text-neutral-200 dark:hover:border-neutral-700 cursor-pointer">
                                            Data Modifica
                                            <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th scope="col" class="!py-1 px-5 text-sm font-medium text-gray-800 --exclude-from-ordering dark:text-neutral-200">Azioni</th>
                                </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200 dark:divide-neutral-700">
                                <tr>
                                    <td class="!py-1 px-5 w-0 select-checkbox p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <div class="flex items-center h-5">
                                            <input id="hs-table-checkbox-1" type="checkbox" class="border-gray-300 rounded-sm text-blue-600 checked:border-blue-500 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" data-hs-datatable-row-selecting-individual="">
                                            <label for="hs-table-checkbox-1" class="sr-only">Checkbox</label>
                                        </div>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <a href="{{ routes('BE_ESTIMATE_VIEW') }}" class="text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline">
                                            P2025000100
                                        </a>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <div class="inline-flex items-center gap-x-1.5 py-0.5 px-2 border border-gray-200 dark:border-neutral-700 rounded-md">
                                            <span class="w-1 h-3 bg-teal-500 rounded-full"></span>
                                            <span class="font-medium text-[13px] text-gray-800 dark:text-neutral-200">
                                                    Area dealer
                                                </span>
                                        </div>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <a href="" class="text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline">
                                            Mauro Milani
                                        </a>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <a href="" class="text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline">
                                            Andrea Ballan
                                        </a>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <div class="inline-flex items-center gap-x-1.5 py-0.5 px-2 border border-gray-200 dark:border-neutral-700 rounded-md">
                                            <img src="{{ contextPath }}/img/brands/harley.svg" class="shrink-0 size-4" width="32" height="32">
                                            <span class="font-medium text-[13px] text-gray-800 dark:text-neutral-200">HD Assistenza</span>
                                        </div>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <span class="font-medium">894,54€</span>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <!-- Tooltip per Stato Quotazione -->
                                        <div class="hs-tooltip [--trigger:hover] [--placement:top] inline-block">
                                            <div class="hs-tooltip-toggle py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-sky-200 text-sky-800 rounded-full cursor-pointer" tabindex="0">
                                                <!-- Badge -->
                                                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-loader-icon lucide-loader"><path d="M12 2v4"/><path d="m16.2 7.8 2.9-2.9"/><path d="M18 12h4"/><path d="m16.2 16.2 2.9 2.9"/><path d="M12 18v4"/><path d="m4.9 19.1 2.9-2.9"/><path d="M2 12h4"/><path d="m4.9 4.9 2.9 2.9"/></svg>
                                                In corso
                                                <!-- Tooltip Content -->
                                                <div class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible hidden opacity-0 transition-opacity absolute invisible z-51 max-w-xs w-full bg-white border border-gray-100 text-start rounded-xl shadow-md after:absolute after:-top-4 after:start-0 after:w-full after:h-4 before:absolute before:-bottom-4 before:start-0 before:w-full before:h-4 dark:bg-neutral-800 dark:border-neutral-700" role="tooltip">
                                                    <!-- Header -->
                                                    <div class="py-3 px-4 border-b border-gray-200 dark:border-neutral-700">
                                                        <h4 class="font-semibold text-gray-800 dark:text-white">Stato Quotazione</h4>
                                                    </div>
                                                    <!-- Details -->
                                                    <div class="py-3 px-4">
                                                        <ul class="space-y-2">
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Stato:</span>
                                                                    <div class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-sky-100 text-sky-800 rounded-full">
                                                                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-loader-icon lucide-loader"><path d="M12 2v4"/><path d="m16.2 7.8 2.9-2.9"/><path d="M18 12h4"/><path d="m16.2 16.2 2.9 2.9"/><path d="M12 18v4"/><path d="m4.9 19.1 2.9-2.9"/><path d="M2 12h4"/><path d="m4.9 4.9 2.9 2.9"/></svg>
                                                                        In corso
                                                                    </div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Versione prodotto:</span>
                                                                    <span class="font-medium text-sm text-gray-800 dark:text-neutral-200">v1.1</span>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Step corrente:</span>
                                                                    <span class="font-medium text-gray-800 dark:text-neutral-200">2 di 4</span>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Modello:</span>
                                                                    <span class="font-medium text-gray-800 dark:text-neutral-200">Harley Davidson Goldwing 900</span>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Targa:</span>
                                                                    <span class="font-medium text-gray-800 dark:text-neutral-200">FH115DS</span>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Garanzie:</span>
                                                                    <span class="font-medium text-gray-800 dark:text-neutral-200 whitespace-normal text-end">RC, Tutela legale</span>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Scadenza quotazione:</span>
                                                                    <span class="font-medium text-gray-800 dark:text-neutral-200">21/09/2025</span>
                                                                </div>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                    <!-- Action -->
                                                    <div class="py-2 px-4 bg-gray-100 dark:bg-neutral-800">
                                                        <a href="#" class="w-full py-1.5 px-3 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700">
                                                            Gestisci
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                            <span class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-gray-200 text-gray-800 rounded-full dark:bg-neutral-800 dark:text-neutral-300">
                                                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-minus-icon lucide-circle-minus"><circle cx="12" cy="12" r="10"/><path d="M8 12h8"/></svg>
                                                No
                                            </span>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                            <span class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-gray-200 text-gray-800 rounded-full dark:bg-neutral-800 dark:text-neutral-300">
                                                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-minus-icon lucide-circle-minus"><circle cx="12" cy="12" r="10"/><path d="M8 12h8"/></svg>
                                                No
                                            </span>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                            <span class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-gray-200 text-gray-800 rounded-full dark:bg-neutral-800 dark:text-neutral-300">
                                                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-minus-icon lucide-minus"><path d="M5 12h14"/></svg>
                                                No
                                            </span>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        14/09/2025 18.53
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        14/09/2025 20.30
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <button type="button" class="py-1.5 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 focus:outline-hidden focus:bg-gray-100 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                                            <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="5" width="6" height="6" rx="1"></rect><path d="m3 17 2 2 4-4"></path><path d="M13 6h8"></path><path d="M13 12h8"></path><path d="M13 18h8"></path></svg>
                                            Riprendi
                                        </button>

                                    </td>
                                </tr>


                                <tr>
                                    <td class="!py-1 px-5 w-0 select-checkbox p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <div class="flex items-center h-5">
                                            <input id="hs-table-checkbox-1" type="checkbox" class="border-gray-300 rounded-sm text-blue-600 checked:border-blue-500 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" data-hs-datatable-row-selecting-individual="">
                                            <label for="hs-table-checkbox-1" class="sr-only">Checkbox</label>
                                        </div>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <a href="{{ routes('BE_ESTIMATE_VIEW') }}" class="text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline">
                                            P2025000100
                                        </a>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <div class="inline-flex items-center gap-x-1.5 py-0.5 px-2 border border-gray-200 dark:border-neutral-700 rounded-md">
                                            <span class="w-1 h-3 bg-teal-500 rounded-full"></span>
                                            <span class="font-medium text-[13px] text-gray-800 dark:text-neutral-200">
                                                    Area dealer
                                                </span>
                                        </div>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <a href="" class="text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline">
                                            Mauro Milani
                                        </a>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <a href="" class="text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline">
                                            Andrea Ballan
                                        </a>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <div class="inline-flex items-center gap-x-1.5 py-0.5 px-2 border border-gray-200 dark:border-neutral-700 rounded-md">
                                            <img src="{{ contextPath }}/img/brands/harley.svg" class="shrink-0 size-4" width="32" height="32">
                                            <span class="font-medium text-[13px] text-gray-800 dark:text-neutral-200">HD Assistenza</span>
                                        </div>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <span class="font-medium">894,54€</span>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <div class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-green-100 text-green-800 rounded-full cursor-pointer" tabindex="0">
                                            <!-- Badge -->
                                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="m9 12 2 2 4-4"/><circle cx="12" cy="12" r="10"/>
                                            </svg>
                                            Completata
                                        </div>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <!-- Tooltip -->
                                        <div class="hs-tooltip [--trigger:hover] [--placement:top] inline-block">
                                            <div class="hs-tooltip-toggle py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-orange-100 text-orange-800 rounded-full cursor-pointer" tabindex="0">
                                                <!-- Badge -->
                                                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <circle cx="12" cy="12" r="10"/><path d="M12 6v6l4 2"/>
                                                </svg>
                                                In attesa
                                                <!-- Tooltip Content -->
                                                <div class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible hidden opacity-0 transition-opacity absolute invisible z-51 max-w-xs w-full bg-white border border-gray-100 text-start rounded-xl shadow-md after:absolute after:-top-4 after:start-0 after:w-full after:h-4 before:absolute before:-bottom-4 before:start-0 before:w-full before:h-4 dark:bg-neutral-800 dark:border-neutral-700" role="tooltip">
                                                    <!-- Header -->
                                                    <div class="py-3 px-4 border-b border-gray-200 dark:border-neutral-700">
                                                        <h4 class="font-semibold text-gray-800 dark:text-white">Dettagli Pagamento</h4>
                                                    </div>
                                                    <!-- Details -->
                                                    <ul class="py-3 px-4 space-y-2">
                                                        <li>
                                                            <div class="flex justify-between items-center text-sm">
                                                                <span class="text-gray-600 dark:text-neutral-400">Tipo:</span>
                                                                <span class="font-medium text-gray-800 dark:text-neutral-200">Bonifico</span>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <div class="flex justify-between items-center text-sm">
                                                                <span class="text-gray-600 dark:text-neutral-400">Importo:</span>
                                                                <span class="font-medium text-gray-800 dark:text-neutral-200">€ 894,54</span>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <div class="flex justify-between items-center text-sm">
                                                                <span class="text-gray-600 dark:text-neutral-400">Stato:</span>
                                                                <div class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-orange-100 text-orange-800 rounded-full cursor-pointer">
                                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                        <circle cx="12" cy="12" r="10"/><path d="M12 6v6l4 2"/>
                                                                    </svg>
                                                                    In attesa
                                                                </div>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <div class="flex justify-between items-center text-sm">
                                                                <span class="text-gray-600 dark:text-neutral-400">Scadenza:</span>
                                                                <span class="font-medium text-gray-800 dark:text-neutral-200">20/09/2025</span>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                    <!-- Action -->
                                                    <div class="py-2 px-4 bg-gray-100 dark:bg-neutral-800">
                                                        <a href="#" class="w-full py-1.5 px-3 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700">
                                                            Gestisci
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <!-- Tooltip -->
                                        <div class="hs-tooltip [--trigger:hover] [--placement:top] inline-block">
                                            <div class="hs-tooltip-toggle py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full cursor-pointer" tabindex="0">
                                                <!-- Badge -->
                                                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <circle cx="12" cy="12" r="10"/><path d="M12 6v6l4 2"/>
                                                </svg>
                                                1/3 Compagnie
                                                <!-- Tooltip Content -->
                                                <div class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible hidden opacity-0 transition-opacity absolute invisible z-51 max-w-xs w-full bg-white border border-gray-100 text-start rounded-xl shadow-md after:absolute after:-top-4 after:start-0 after:w-full after:h-4 before:absolute before:-bottom-4 before:start-0 before:w-full before:h-4 dark:bg-neutral-800 dark:border-neutral-700" role="tooltip">
                                                    <!-- Header -->
                                                    <div class="py-3 px-4 border-b border-gray-200 dark:border-neutral-700">
                                                        <h4 class="font-semibold text-gray-800 dark:text-white">Stato Emissioni</h4>
                                                    </div>
                                                    <!-- Details -->
                                                    <!-- Details -->
                                                    <div class="py-3 px-4">
                                                        <ul class="space-y-2">
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Assicurazioni Generali:</span>
                                                                    <div class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                                                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                            <path d="m9 12 2 2 4-4"/><circle cx="12" cy="12" r="10"/>
                                                                        </svg>
                                                                        Emessa
                                                                    </div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Axa:</span>
                                                                    <div class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-orange-100 text-orange-800 rounded-full">
                                                                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                            <circle cx="12" cy="12" r="10"/><path d="M12 6v6l4 2"/>
                                                                        </svg>
                                                                        Attesa conferma
                                                                    </div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Zurich:</span>
                                                                    <div class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                                                                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                            <circle cx="12" cy="12" r="10"/><path d="m15 9-6 6"/><path d="m9 9 6 6"/>
                                                                        </svg>
                                                                        Errore API
                                                                    </div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Ultimo aggiornamento:</span>
                                                                    <span class="font-medium text-gray-800 dark:text-neutral-200">14/09/2025 20:30</span>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Tentativi totali:</span>
                                                                    <span class="font-medium text-gray-800 dark:text-neutral-200">3</span>
                                                                </div>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                    <!-- Action -->
                                                    <div class="py-2 px-4 bg-gray-100 dark:bg-neutral-800">
                                                        <a href="#" class="w-full py-1.5 px-3 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700">
                                                            Gestisci
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                            <span class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-gray-200 text-gray-800 rounded-full dark:bg-neutral-800 dark:text-neutral-300">
                                                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-minus-icon lucide-minus"><path d="M5 12h14"/></svg>
                                                No
                                            </span>
                                    </td>

                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        14/09/2025 18.53
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        14/09/2025 20.30
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">

                                    </td>
                                </tr>

                                <tr>
                                    <td class="!py-1 px-5 w-0 select-checkbox p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <div class="flex items-center h-5">
                                            <input id="hs-table-checkbox-1" type="checkbox" class="border-gray-300 rounded-sm text-blue-600 checked:border-blue-500 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" data-hs-datatable-row-selecting-individual="">
                                            <label for="hs-table-checkbox-1" class="sr-only">Checkbox</label>
                                        </div>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <a href="{{ routes('BE_ESTIMATE_VIEW') }}" class="text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline">
                                            P2025000100
                                        </a>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <div class="inline-flex items-center gap-x-1.5 py-0.5 px-2 border border-gray-200 dark:border-neutral-700 rounded-md">
                                            <span class="w-1 h-3 bg-teal-500 rounded-full"></span>
                                            <span class="font-medium text-[13px] text-gray-800 dark:text-neutral-200">
                                                    Area dealer
                                                </span>
                                        </div>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <a href="" class="text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline">
                                            Mauro Milani
                                        </a>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <a href="" class="text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline">
                                            Andrea Ballan
                                        </a>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <div class="inline-flex items-center gap-x-1.5 py-0.5 px-2 border border-gray-200 dark:border-neutral-700 rounded-md">
                                            <img src="{{ contextPath }}/img/brands/harley.svg" class="shrink-0 size-4" width="32" height="32">
                                            <span class="font-medium text-[13px] text-gray-800 dark:text-neutral-200">HD Assistenza</span>
                                        </div>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <span class="font-medium">894,54€</span>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <div class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-green-100 text-green-800 rounded-full cursor-pointer" tabindex="0">
                                            <!-- Badge -->
                                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="m9 12 2 2 4-4"/><circle cx="12" cy="12" r="10"/>
                                            </svg>
                                            Completata
                                        </div>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <!-- Tooltip -->
                                        <div class="hs-tooltip [--trigger:hover] [--placement:top] inline-block">
                                            <div class="hs-tooltip-toggle py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-green-100 text-green-800 rounded-full cursor-pointer" tabindex="0">
                                                <!-- Badge -->
                                                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="m9 12 2 2 4-4"/><circle cx="12" cy="12" r="10"/>
                                                </svg>
                                                Pagato
                                                <!-- Tooltip Content -->
                                                <div class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible hidden opacity-0 transition-opacity absolute invisible z-51 max-w-xs w-full bg-white border border-gray-100 text-start rounded-xl shadow-md after:absolute after:-top-4 after:start-0 after:w-full after:h-4 before:absolute before:-bottom-4 before:start-0 before:w-full before:h-4 dark:bg-neutral-800 dark:border-neutral-700" role="tooltip">
                                                    <!-- Header -->
                                                    <div class="py-3 px-4 border-b border-gray-200 dark:border-neutral-700">
                                                        <h4 class="font-semibold text-gray-800 dark:text-white">Dettagli Pagamento</h4>
                                                    </div>
                                                    <!-- Details -->
                                                    <ul class="py-3 px-4 space-y-2">
                                                        <li>
                                                            <div class="flex justify-between items-center text-sm">
                                                                <span class="text-gray-600 dark:text-neutral-400">Tipo:</span>
                                                                <span class="font-medium text-gray-800 dark:text-neutral-200">Carta di Credito</span>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <div class="flex justify-between items-center text-sm">
                                                                <span class="text-gray-600 dark:text-neutral-400">Importo:</span>
                                                                <span class="font-medium text-gray-800 dark:text-neutral-200">€ 894,54</span>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <div class="flex justify-between items-center text-sm">
                                                                <span class="text-gray-600 dark:text-neutral-400">Stato:</span>
                                                                <div class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-green-100 text-green-800 rounded-full cursor-pointer" tabindex="0">
                                                                    <!-- Badge -->
                                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                        <path d="m9 12 2 2 4-4"/><circle cx="12" cy="12" r="10"/>
                                                                    </svg>
                                                                    Pagato
                                                                </div>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <div class="flex justify-between items-center text-sm">
                                                                <span class="text-gray-600 dark:text-neutral-400">Data:</span>
                                                                <span class="font-medium text-gray-800 dark:text-neutral-200">14/09/2025 18:53</span>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <div class="flex justify-between items-center text-sm">
                                                                <span class="text-gray-600 dark:text-neutral-400">ID Transazione:</span>
                                                                <span class="font-medium text-gray-800 dark:text-neutral-200">TXN-789456</span>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                    <!-- Action -->
                                                    <div class="py-2 px-4 bg-gray-100 dark:bg-neutral-800">
                                                        <a href="#" class="w-full py-1.5 px-3 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700">
                                                            Gestisci
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <!-- Tooltip -->
                                        <div class="hs-tooltip [--trigger:hover] [--placement:top] inline-block">
                                            <div class="hs-tooltip-toggle py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-green-100 text-green-800 rounded-full cursor-pointer" tabindex="0">
                                                <!-- Badge -->
                                                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="m9 12 2 2 4-4"/><circle cx="12" cy="12" r="10"/>
                                                </svg>
                                                Emessa
                                                <!-- Tooltip Content -->
                                                <div class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible hidden opacity-0 transition-opacity absolute invisible z-51 max-w-xs w-full bg-white border border-gray-100 text-start rounded-xl shadow-md after:absolute after:-top-4 after:start-0 after:w-full after:h-4 before:absolute before:-bottom-4 before:start-0 before:w-full before:h-4 dark:bg-neutral-800 dark:border-neutral-700" role="tooltip">
                                                    <!-- Header -->
                                                    <div class="py-3 px-4 border-b border-gray-200 dark:border-neutral-700">
                                                        <h4 class="font-semibold text-gray-800 dark:text-white">Stato Emissioni</h4>
                                                    </div>
                                                    <!-- Details -->
                                                    <div class="py-3 px-4">
                                                        <ul class="space-y-2">
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Assicurazioni Generali:</span>
                                                                    <div class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                                                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                            <path d="m9 12 2 2 4-4"/><circle cx="12" cy="12" r="10"/>
                                                                        </svg>
                                                                        Emessa
                                                                    </div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Ultimo aggiornamento:</span>
                                                                    <span class="font-medium text-gray-800 dark:text-neutral-200">14/09/2025 20:30</span>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Tentativi totali:</span>
                                                                    <span class="font-medium text-gray-800 dark:text-neutral-200">1</span>
                                                                </div>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                    <!-- Action -->
                                                    <div class="py-2 px-4 bg-gray-100 dark:bg-neutral-800">
                                                        <a href="#" class="w-full py-1.5 px-3 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700">
                                                            Gestisci
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <!-- Tooltip -->
                                        <div class="hs-tooltip [--trigger:hover] [--placement:top] inline-block">
                                            <div class="hs-tooltip-toggle py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-green-100 text-green-800 rounded-full cursor-pointer" tabindex="0">
                                                <!-- Badge -->
                                                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="m9 12 2 2 4-4"/><circle cx="12" cy="12" r="10"/>
                                                </svg>
                                                Aperti
                                                <!-- Tooltip Content -->
                                                <div class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible hidden opacity-0 transition-opacity absolute invisible z-51 max-w-xs w-full bg-white border border-gray-100 text-start rounded-xl shadow-md after:absolute after:-top-4 after:start-0 after:w-full after:h-4 before:absolute before:-bottom-4 before:start-0 before:w-full before:h-4 dark:bg-neutral-800 dark:border-neutral-700" role="tooltip">
                                                    <!-- Header -->
                                                    <div class="py-3 px-4 border-b border-gray-200 dark:border-neutral-700">
                                                        <h4 class="font-semibold text-gray-800 dark:text-white">Consegna documenti</h4>
                                                    </div>
                                                    <!-- Details -->
                                                    <!-- Details -->
                                                    <div class="py-3 px-4">
                                                        <ul class="space-y-2">
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Email:</span>
                                                                    <div class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                                                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                            <path d="m9 12 2 2 4-4"/><circle cx="12" cy="12" r="10"/>
                                                                        </svg>
                                                                        Consegnata e aperta
                                                                    </div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">WhatsApp:</span>
                                                                    <div class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                                                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                                                                            <path d="m10 7-3 3 3 3"/><path d="m14 7 3 3-3 3"/>
                                                                        </svg>
                                                                        Consegnata
                                                                    </div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">SMS:</span>
                                                                    <div class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                                                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-minus-icon lucide-circle-minus"><circle cx="12" cy="12" r="10"/><path d="M8 12h8"/></svg>
                                                                        Non inviata
                                                                    </div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Ultimo invio:</span>
                                                                    <span class="font-medium text-gray-800 dark:text-neutral-200">14/09/2025 16:20</span>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Documenti inviati:</span>
                                                                    <span class="font-medium text-gray-800 dark:text-neutral-200">Polizza, Certificato</span>
                                                                </div>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                    <!-- Action -->
                                                    <div class="py-2 px-4 bg-gray-100 dark:bg-neutral-800">
                                                        <a href="#" class="w-full py-1.5 px-3 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700">
                                                            Gestisci
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>

                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        14/09/2025 18.53
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        14/09/2025 20.30
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">

                                        <a href="#" class="py-1 px-2 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 focus:outline-hidden focus:bg-gray-100 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                                            <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                                <polyline points="14,2 14,8 20,8"></polyline>
                                                <line x1="16" y1="13" x2="8" y2="13"></line>
                                                <line x1="16" y1="17" x2="8" y2="17"></line>
                                                <line x1="10" y1="9" x2="8" y2="9"></line>
                                            </svg>
                                            <span class="font-medium text-gray-800 dark:text-neutral-200">
                                                    P2025010025
                                                </span>
                                        </a>
                                    </td>
                                </tr>


                                <tr>
                                    <td class="!py-1 px-5 w-0 select-checkbox p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <div class="flex items-center h-5">
                                            <input id="hs-table-checkbox-1" type="checkbox" class="border-gray-300 rounded-sm text-blue-600 checked:border-blue-500 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" data-hs-datatable-row-selecting-individual="">
                                            <label for="hs-table-checkbox-1" class="sr-only">Checkbox</label>
                                        </div>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <a href="{{ routes('BE_ESTIMATE_VIEW') }}" class="text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline">
                                            P2025000100
                                        </a>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <div class="inline-flex items-center gap-x-1.5 py-0.5 px-2 border border-gray-200 dark:border-neutral-700 rounded-md">
                                            <span class="w-1 h-3 bg-teal-500 rounded-full"></span>
                                            <span class="font-medium text-[13px] text-gray-800 dark:text-neutral-200">
                                                    Area dealer
                                                </span>
                                        </div>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <a href="" class="text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline">
                                            Mauro Milani
                                        </a>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <a href="" class="text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline">
                                            Andrea Ballan
                                        </a>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <div class="inline-flex items-center gap-x-1.5 py-0.5 px-2 border border-gray-200 dark:border-neutral-700 rounded-md">
                                            <img src="{{ contextPath }}/img/brands/harley.svg" class="shrink-0 size-4" width="32" height="32">
                                            <span class="font-medium text-[13px] text-gray-800 dark:text-neutral-200">HD Assistenza</span>
                                        </div>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <span class="font-medium">894,54€</span>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <div class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-green-100 text-green-800 rounded-full cursor-pointer" tabindex="0">
                                            <!-- Badge -->
                                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="m9 12 2 2 4-4"/><circle cx="12" cy="12" r="10"/>
                                            </svg>
                                            Completata
                                        </div>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <!-- Tooltip -->
                                        <div class="hs-tooltip [--trigger:hover] [--placement:top] inline-block">
                                            <div class="hs-tooltip-toggle py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-green-100 text-green-800 rounded-full cursor-pointer" tabindex="0">
                                                <!-- Badge -->
                                                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="m9 12 2 2 4-4"/><circle cx="12" cy="12" r="10"/>
                                                </svg>
                                                Pagato
                                                <!-- Tooltip Content -->
                                                <div class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible hidden opacity-0 transition-opacity absolute invisible z-51 max-w-xs w-full bg-white border border-gray-100 text-start rounded-xl shadow-md after:absolute after:-top-4 after:start-0 after:w-full after:h-4 before:absolute before:-bottom-4 before:start-0 before:w-full before:h-4 dark:bg-neutral-800 dark:border-neutral-700" role="tooltip">
                                                    <!-- Header -->
                                                    <div class="py-3 px-4 border-b border-gray-200 dark:border-neutral-700">
                                                        <h4 class="font-semibold text-gray-800 dark:text-white">Dettagli Pagamento</h4>
                                                    </div>
                                                    <!-- Details -->
                                                    <ul class="py-3 px-4 space-y-2">
                                                        <li>
                                                            <div class="flex justify-between items-center text-sm">
                                                                <span class="text-gray-600 dark:text-neutral-400">Tipo:</span>
                                                                <span class="font-medium text-gray-800 dark:text-neutral-200">Carta di Credito</span>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <div class="flex justify-between items-center text-sm">
                                                                <span class="text-gray-600 dark:text-neutral-400">Importo:</span>
                                                                <span class="font-medium text-gray-800 dark:text-neutral-200">€ 894,54</span>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <div class="flex justify-between items-center text-sm">
                                                                <span class="text-gray-600 dark:text-neutral-400">Stato:</span>
                                                                <div class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-green-100 text-green-800 rounded-full cursor-pointer" tabindex="0">
                                                                    <!-- Badge -->
                                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                        <path d="m9 12 2 2 4-4"/><circle cx="12" cy="12" r="10"/>
                                                                    </svg>
                                                                    Pagato
                                                                </div>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <div class="flex justify-between items-center text-sm">
                                                                <span class="text-gray-600 dark:text-neutral-400">Data:</span>
                                                                <span class="font-medium text-gray-800 dark:text-neutral-200">14/09/2025 18:53</span>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <div class="flex justify-between items-center text-sm">
                                                                <span class="text-gray-600 dark:text-neutral-400">ID Transazione:</span>
                                                                <span class="font-medium text-gray-800 dark:text-neutral-200">TXN-789456</span>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                    <!-- Action -->
                                                    <div class="py-2 px-4 bg-gray-100 dark:bg-neutral-800">
                                                        <a href="#" class="w-full py-1.5 px-3 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700">
                                                            Gestisci
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <!-- Tooltip -->
                                        <div class="hs-tooltip [--trigger:hover] [--placement:top] inline-block">
                                            <div class="hs-tooltip-toggle py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-green-100 text-green-800 rounded-full cursor-pointer" tabindex="0">
                                                <!-- Badge -->
                                                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="m9 12 2 2 4-4"/><circle cx="12" cy="12" r="10"/>
                                                </svg>
                                                Emessa
                                                <!-- Tooltip Content -->
                                                <div class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible hidden opacity-0 transition-opacity absolute invisible z-51 max-w-xs w-full bg-white border border-gray-100 text-start rounded-xl shadow-md after:absolute after:-top-4 after:start-0 after:w-full after:h-4 before:absolute before:-bottom-4 before:start-0 before:w-full before:h-4 dark:bg-neutral-800 dark:border-neutral-700" role="tooltip">
                                                    <!-- Header -->
                                                    <div class="py-3 px-4 border-b border-gray-200 dark:border-neutral-700">
                                                        <h4 class="font-semibold text-gray-800 dark:text-white">Stato Emissioni</h4>
                                                    </div>
                                                    <!-- Details -->
                                                    <div class="py-3 px-4">
                                                        <ul class="space-y-2">
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Assicurazioni Generali:</span>
                                                                    <div class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                                                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                            <path d="m9 12 2 2 4-4"/><circle cx="12" cy="12" r="10"/>
                                                                        </svg>
                                                                        Emessa
                                                                    </div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Ultimo aggiornamento:</span>
                                                                    <span class="font-medium text-gray-800 dark:text-neutral-200">14/09/2025 20:30</span>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Tentativi totali:</span>
                                                                    <span class="font-medium text-gray-800 dark:text-neutral-200">1</span>
                                                                </div>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                    <!-- Action -->
                                                    <div class="py-2 px-4 bg-gray-100 dark:bg-neutral-800">
                                                        <a href="#" class="w-full py-1.5 px-3 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700">
                                                            Gestisci
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        <!-- Tooltip -->
                                        <div class="hs-tooltip [--trigger:hover] [--placement:top] inline-block">
                                            <div class="hs-tooltip-toggle py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-blue-100 text-blue-800 rounded-full cursor-pointer" tabindex="0">
                                                <!-- Badge -->
                                                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="m9 12 2 2 4-4"/><circle cx="12" cy="12" r="10"/>
                                                </svg>
                                                Consegnati
                                                <!-- Tooltip Content -->
                                                <div class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible hidden opacity-0 transition-opacity absolute invisible z-51 max-w-xs w-full bg-white border border-gray-100 text-start rounded-xl shadow-md after:absolute after:-top-4 after:start-0 after:w-full after:h-4 before:absolute before:-bottom-4 before:start-0 before:w-full before:h-4 dark:bg-neutral-800 dark:border-neutral-700" role="tooltip">
                                                    <!-- Header -->
                                                    <div class="py-3 px-4 border-b border-gray-200 dark:border-neutral-700">
                                                        <h4 class="font-semibold text-gray-800 dark:text-white">Consegna documenti</h4>
                                                    </div>
                                                    <!-- Details -->
                                                    <!-- Details -->
                                                    <div class="py-3 px-4">
                                                        <ul class="space-y-2">
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Email:</span>
                                                                    <div class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                                                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                            <path d="m9 12 2 2 4-4"/><circle cx="12" cy="12" r="10"/>
                                                                        </svg>
                                                                        Consegnata
                                                                    </div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">WhatsApp:</span>
                                                                    <div class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                                                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-minus-icon lucide-circle-minus"><circle cx="12" cy="12" r="10"/><path d="M8 12h8"/></svg>
                                                                        Non inviata
                                                                    </div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">SMS:</span>
                                                                    <div class="py-1 ps-1.5 pe-2.5 inline-flex items-center gap-x-1.5 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                                                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-minus-icon lucide-circle-minus"><circle cx="12" cy="12" r="10"/><path d="M8 12h8"/></svg>
                                                                        Non inviata
                                                                    </div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Ultimo invio:</span>
                                                                    <span class="font-medium text-gray-800 dark:text-neutral-200">14/09/2025 16:20</span>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div class="flex justify-between items-center text-sm">
                                                                    <span class="text-gray-600 dark:text-neutral-400">Documenti inviati:</span>
                                                                    <span class="font-medium text-gray-800 dark:text-neutral-200">Polizza, Certificato</span>
                                                                </div>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                    <!-- Action -->
                                                    <div class="py-2 px-4 bg-gray-100 dark:bg-neutral-800">
                                                        <a href="#" class="w-full py-1.5 px-3 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700">
                                                            Gestisci
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>

                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        14/09/2025 18.53
                                    </td>
                                    <td class="p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200">
                                        14/09/2025 20.30
                                    </td>
                                </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <!--  End Table -->

                <!-- Table Footer -->
                <div class="flex flex-wrap justify-between items-center gap-2 p-4">
                    <div class="flex items-center gap-x-1 whitespace-nowrap text-sm text-gray-500 dark:text-neutral-400" data-hs-datatable-info="">
                        <span>Visualizzo</span>

                        <!-- Select -->
                        <select class="hidden" data-hs-select='{
                                "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span data-title></span></button>",
                                "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opadealer-50 relative py-2 px-3 pe-9 flex text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 before:absolute before:inset-0 before:z-1 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-200 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800",
                                "dropdownClasses": "mt-2 z-50 w-20 max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-md overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                                "optionClasses": "py-2 px-3 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-md focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                                "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-blue-600 dark:text-blue-500\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>",
                                "extraMarkup": "<div class=\"absolute top-1/2 end-3 -translate-y-1/2\"><svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m7 15 5 5 5-5\"/><path d=\"m7 9 5-5 5 5\"/></svg></div>"
                                }' data-hs-datatable-page-entities="">
                            <option value="10" selected="">10</option>
                            <option value="15">15</option>
                            <option value="20">20</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                        </select>
                        <!-- End Select -->

                        <span>da</span>
                        <span data-hs-datatable-info-from=""></span>
                        <span>a</span>
                        <span data-hs-datatable-info-to=""></span>
                        <span>di</span>
                        <span data-hs-datatable-info-length=""></span>
                        <span>rivenditori</span>
                    </div>
                    <div class="inline-flex items-center gap-1" data-hs-datatable-paging="">

                        <button type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opadealer-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" data-hs-datatable-paging-prev="">
                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m15 18-6-6 6-6"/></svg>
                            Indietro
                        </button>
                        <div class="flex items-center space-x-1 [&>button]:py-2 [&>button]:px-3 [&>button]:text-sm [&>button]:font-medium [&>button]:rounded-lg [&>button]:border [&>button]:border-gray-200 [&>button]:bg-white [&>button]:text-gray-800 [&>button]:shadow-2xs [&>button]:hover:bg-gray-50 [&>button]:focus:outline-hidden [&>button]:focus:bg-gray-50 [&>button]:dark:bg-neutral-800 [&>button]:dark:border-neutral-700 [&>button]:dark:text-white [&>button]:dark:hover:bg-neutral-700 [&>button]:dark:focus:bg-neutral-700 [&>.active]:bg-blue-500 [&>.active]:text-white [&>.active]:border-blue-500" data-hs-datatable-paging-pages=""></div>
                        <button type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opadealer-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" data-hs-datatable-paging-next="">
                            Avanti
                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m9 18 6-6-6-6"/></svg>
                        </button>
                    </div>
                </div>
                <!-- End Table Footer -->
            </div>
        </div>
        <!-- End DataTable Content -->
    </div>
    <!-- End Card -->
</div>
<!-- End Container -->

<!-- Fixed Bottom Bulk Actions - Preline Inbox Demo Style -->
<div id="bulk-actions-container" class="bulk-action-container hidden fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
    <div class="flex items-center gap-x-3 py-3 px-5 bg-gray-800 border border-gray-700 rounded-xl shadow-2xl dark:bg-gray-900 dark:border-gray-600 backdrop-blur-sm">
        <!-- Selection Counter -->
        <div class="flex items-center gap-x-2">
            <span id="selected-count" class="text-sm font-medium text-white">0</span>
            <span class="text-sm text-gray-300">Selezionati</span>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center gap-x-2">
            <!-- Archive Selected -->
            {% if user.hasPermission('PRODUCT_MANAGEMENT', 'edit') %}
            <button type="button" class="inline-flex items-center gap-x-1.5 py-2 px-3 text-xs font-medium rounded-lg border border-gray-600 bg-gray-700 text-white hover:bg-gray-600 focus:outline-none focus:bg-gray-600 disabled:opadealer-50 disabled:pointer-events-none dark:border-gray-500 dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:bg-gray-700 transition-colors duration-200" onclick="DealerCollection.archiveSelectedRows(); return false;">
                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="5" x="2" y="3" rx="1"/><path d="M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8"/><path d="M10 12h4"/></svg>
                Archivia
            </button>
            {% endif %}

            <!-- Delete Selected -->
            {% if user.hasPermission('PRODUCT_MANAGEMENT', 'delete') %}
            <button type="button" class="inline-flex items-center gap-x-1.5 py-2 px-3 text-xs font-medium rounded-lg border border-transparent bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:bg-red-700 disabled:opadealer-50 disabled:pointer-events-none transition-colors duration-200" onclick="DealerCollection.deleteSelectedRows(); return false;">
                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c-1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>
                Elimina
            </button>
            {% endif %}

            <!-- Close Button -->
            <button type="button" class="inline-flex items-center justify-center size-9 text-sm font-semibold rounded-lg border border-transparent text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none focus:text-white focus:bg-gray-700 disabled:opadealer-50 disabled:pointer-events-none transition-colors duration-200" onclick="DealerCollection.clearSelection(); return false;" aria-label="Close">
                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m18 6-12 12"/><path d="m6 6 12 12"/></svg>
            </button>
        </div>
    </div>
</div>

<!-- Table Filters Offcanvas -->
<div id="table-filters" class="hs-overlay hs-overlay-open:translate-x-0 hidden translate-x-full fixed top-0 end-0 transition-all duration-300 transform size-full sm:w-100 z-80 flex flex-col bg-white dark:bg-neutral-800" role="dialog" tabindex="-1" aria-labelledby="hs-pro-shflo-label">
    <!-- Header -->
    <div class="py-3 px-6 flex justify-between items-center border-b border-gray-200 dark:border-neutral-700">
        <h3 id="hs-pro-shflo-label" class="font-medium text-gray-800 dark:text-neutral-200">
            Filtra
        </h3>
        <button type="button" class="py-1.5 px-2 inline-flex justify-center items-center gap-x-1 rounded-full border border-gray-200 text-xs text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 disabled:opadealer-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:hover:bg-neutral-700 dark:text-neutral-200 dark:focus:bg-neutral-700" aria-label="Close" data-hs-overlay="#table-filters">
            <span class="hidden lg:block">Esc</span>
            <span class="block lg:hidden">Chiudi</span>
            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
        </button>
    </div>
    <!-- End Header -->

    <!-- Body -->
    <div class="bg-gray-100 h-full overflow-y-auto overflow-hidden [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-800">
        <div class="p-2 space-y-2">
            <!-- Archived Filter Card -->
            <div class="p-4 bg-white rounded-lg shadow-2xs dark:bg-neutral-900">
                <div class="mb-3">
                    <span class="font-medium text-sm text-gray-800 dark:text-neutral-200">Opzioni di visualizzazione</span>
                </div>

                <!-- Archived -->
                <div class="flex items-center">
                    <label for="dealer_archived" class="p-2 group w-full inline-flex items-center cursor-pointer text-sm rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-800">
                        <input type="checkbox" class="shrink-0 size-4.5 border-gray-300 rounded-sm text-blue-600 checked:border-blue-600 focus:ring-blue-500 disabled:opadealer-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-500 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" id="dealer_archived" onchange="DealerCollection.reloadTable(this.checked)">
                        <span class="ms-2 text-gray-800 dark:text-neutral-400">Mostra archiviati</span>
                    </label>
                </div>
                <!-- End Archived Checkbox -->

            </div>
            <!-- End Archived Filter Card -->

        </div>
    </div>
    <!-- End Body -->

    <!-- Footer -->
    <div class="p-6 border-t border-gray-200 dark:border-neutral-700">
        <div class="flex items-center gap-x-2">
            <button type="button" class="py-2 px-3 w-full inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 disabled:opadealer-50 disabled:pointer-events-none focus:outline-hidden focus:bg-blue-700" data-hs-overlay="#table-filters">
                Chiudi
            </button>
        </div>
    </div>
    <!-- End Footer -->
</div>
<!-- End Table Filters Offcanvas -->

{% endblock %}
{% block pagescript %}

<!-- Reload -->
<script class="reload-script-on-load">
    addRoute('BE_PRODUCT_EDIT', '{{ routes("BE_PRODUCT_EDIT") }}');
    addRoute('BE_PRODUCT_CHECK_DRAFT', '{{ routes("BE_PRODUCT_CHECK_DRAFT") }}');
    addRoute('BE_PRODUCT_DELETE_DRAFT', '{{ routes("BE_PRODUCT_DELETE_DRAFT") }}');
</script>

<!-- Page Scripts -->
<script>
    $(document).ready(function() {
        // Handle new product button click
        $('#new-product-btn').on('click', function() {
            const button = $(this);

            // Disable button and show loading state
            button.prop('disabled', true);
            const originalText = button.html();
            button.html('<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Controllo...');

            // Check for existing drafts
            $.ajax({
                url: appRoutes.get('BE_PRODUCT_CHECK_DRAFT'),
                type: 'GET',
                success: function(response) {
                    if (response.hasDraft) {
                        // Show draft modal using jQuery Confirm
                        createDraftModal({
                            draftName: response.draftName || 'Prodotto senza nome',
                            onResume: function() {
                                // Navigate to edit page with draft ID
                                window.location.href = appRoutes.get('BE_PRODUCT_EDIT') + '?productId=' + response.draftId;
                            },
                            onStartFresh: function() {
                                // Delete existing draft and create new one
                                $.ajax({
                                    url: appRoutes.get('BE_PRODUCT_DELETE_DRAFT'),
                                    type: 'POST',
                                    success: function() {
                                        // Navigate to edit page for new product
                                        window.location.href = appRoutes.get('BE_PRODUCT_EDIT');
                                    },
                                    error: function() {
                                        showToast('Errore durante l\'eliminazione della bozza', 'error');
                                        resetButton();
                                    }
                                });
                            },
                            onCancel: function() {
                                resetButton();
                            }
                        });
                    } else {
                        // No draft found, proceed directly to edit page
                        window.location.href = appRoutes.get('BE_PRODUCT_EDIT');
                    }
                },
                error: function() {
                    showToast('Errore durante il controllo delle bozze', 'error');
                    resetButton();
                }
            });

            function resetButton() {
                button.prop('disabled', false);
                button.html(originalText);
            }
        });

        // Draft Management Modal System
        // Creates and manages draft detection modal for product creation using jQuery Confirm
        // Uses custom 'preline' theme to match Preline UI design system
        function createDraftModal(options) {
            const defaults = {
                title: 'Bozza esistente trovata',
                draftName: 'Prodotto senza nome',
                onResume: null,
                onStartFresh: null,
                onCancel: null
            };

            const config = Object.assign({}, defaults, options);

            // Create content with icon and text
            const contentHtml = `
                <div class="draft-modal-content">
                    <div class="draft-modal-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="m21 16-4 4-4-4"></path>
                            <path d="M17 20V4"></path>
                            <path d="m3 8 4-4 4 4"></path>
                            <path d="M7 4v16"></path>
                        </svg>
                    </div>
                    <div class="draft-modal-text">
                        <h4>Hai già una bozza in corso</h4>
                        <p>È stata trovata una bozza esistente: <strong>"${config.draftName}"</strong></p>
                        <p>Cosa vuoi fare?</p>
                    </div>
                </div>
            `;

            // Create jQuery Confirm modal with Preline-like styling
            return $.confirm({
                title: config.title,
                content: contentHtml,
                theme: 'preline',
                type: 'orange',
                typeAnimated: true,
                animation: 'scale',
                closeAnimation: 'scale',
                animationSpeed: 300,
                animationBounce: 1,
                escapeKey: 'annulla',
                backgroundDismiss: false,
                columnClass: 'col-lg-4 col-lg-offset-4 col-md-6 col-md-offset-3 col-sm-8 col-sm-offset-2',
                buttons: {
                    riprendi: {
                        text: 'Riprendi bozza',
                        btnClass: 'btn-blue',
                        action: function () {
                            if (typeof config.onResume === 'function') {
                                config.onResume();
                            }
                            return true; // Close modal
                        }
                    },
                    iniziaDaCapo: {
                        text: 'Inizia da capo',
                        btnClass: 'btn-red',
                        action: function () {
                            if (typeof config.onStartFresh === 'function') {
                                config.onStartFresh();
                            }
                            return true; // Close modal
                        }
                    },
                    annulla: {
                        text: 'Annulla',
                        btnClass: 'btn-light',
                        action: function () {
                            if (typeof config.onCancel === 'function') {
                                config.onCancel();
                            }
                            return true; // Close modal
                        }
                    }
                }
            });
        }
    });
</script>

{% endblock %}
