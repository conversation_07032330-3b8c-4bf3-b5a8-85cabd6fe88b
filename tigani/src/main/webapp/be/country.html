{% extends "be/include/preline-base.html" %}

{% set menu = 'MAINTENANCE' %}
{% set submenu = 'COUNTRY_COLLECTION' %}
{% set title = curCountry is empty ? 'Nuovo Paese' : 'Modifica Paese' %}

{% block extrahead %}

<title>{{ title }}</title>

<!-- Page Libs -->
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}

{% endblock %}

{% block content %}

<div class="lg:ps-65 p-2 sm:p-5 md:pt-5 space-y-5">
    <!-- Card -->
    <div class="bg-white border border-gray-200 shadow-xs rounded-xl dark:bg-neutral-900 dark:border-neutral-700">
        <!-- Header -->
        <div class="py-3 px-5 flex flex-wrap justify-between items-center gap-2 border-b border-gray-200 dark:border-neutral-700">
            <!-- Title -->
            <div class="flex flex-wrap items-center gap-2">
                <h2 class="font-medium text-gray-800 dark:text-neutral-200">
                    {{ title }}
                </h2>
            </div>
            <!-- End Title -->
            <!-- Actions -->
            <div class="flex flex-wrap items-center gap-2">
                <a href="{{ routes('BE_COUNTRY_COLLECTION') }}" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m15 18-6-6 6-6"/></svg>
                    Torna alla lista
                </a>
            </div>
            <!-- End Actions -->
        </div>
        <!-- End Header -->

        <!-- Form Content -->
        <div class="p-5">
            {% set postUrl = routes('BE_COUNTRY_SAVE') %}
            {% if curCountry.id is not empty %}                
            {% set postUrl = routes('BE_COUNTRY_SAVE') + '?countryId=' + curCountry.id %}
            {% endif %}

            <form id="country-edit" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Left Column -->
                    <div class="space-y-4">
                        <!-- Description -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                                Descrizione: <span class="text-red-500">*</span>
                            </label>
                            <input name="description" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Descrizione del paese" value="{{ curCountry.description }}" required {% if not user.hasPermission('COUNTRY_MANAGEMENT', 'edit') %}readonly{% endif %}>
                        </div>

                        <!-- Code -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                                Codice: <span class="text-red-500">*</span>
                            </label>
                            <input name="code" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Codice del paese (es. IT, FR, DE)" value="{{ curCountry.code }}" required {% if not user.hasPermission('COUNTRY_MANAGEMENT', 'edit') %}readonly{% endif %}>
                        </div>

                        <!-- Codice Belfiore -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                                Codice Belfiore:
                            </label>
                            <input name="codiceBelfiore" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Codice Belfiore" value="{{ curCountry.codiceBelfiore }}" {% if not user.hasPermission('COUNTRY_MANAGEMENT', 'edit') %}readonly{% endif %}>
                        </div>
                    </div>

                    <!-- Right Column -->
                    <div class="space-y-4">
                        {% if curCountry.id is not empty %}
                        <!-- System Information -->
                        <div class="bg-gray-50 dark:bg-neutral-800 rounded-lg p-4">
                            <h3 class="text-sm font-medium text-gray-800 dark:text-neutral-200 mb-3">Informazioni di sistema</h3>
                            
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-neutral-400">ID:</span>
                                    <span class="text-sm font-mono text-gray-800 dark:text-neutral-200">{{ curCountry.id }}</span>
                                </div>
                                
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-neutral-400">Data creazione:</span>
                                    <span class="text-sm text-gray-800 dark:text-neutral-200">{{ curCountry.creation | date('dd/MM/yyyy HH:mm') }}</span>
                                </div>
                                
                                {% if curCountry.lastUpdate is not empty %}
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-neutral-400">Ultima modifica:</span>
                                    <span class="text-sm text-gray-800 dark:text-neutral-200">{{ curCountry.lastUpdate | date('dd/MM/yyyy HH:mm') }}</span>
                                </div>
                                {% endif %}
                                
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600 dark:text-neutral-400">Stato:</span>
                                    <span class="text-sm">
                                        {% if curCountry.archived %}
                                            <span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-800/30 dark:text-red-500">
                                                <span class="size-1.5 inline-block rounded-full bg-red-800 dark:bg-red-500"></span>
                                                Archiviato
                                            </span>
                                        {% else %}
                                            <span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-teal-100 text-teal-800 dark:bg-teal-800/30 dark:text-teal-500">
                                                <span class="size-1.5 inline-block rounded-full bg-teal-800 dark:bg-teal-500"></span>
                                                Attivo
                                            </span>
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end gap-x-2 mt-6 pt-6 border-t border-gray-200 dark:border-neutral-700">
                    {% if curCountry is empty %}
                        <!-- New Country - Show Save Button -->
                        {% if user.hasPermission('COUNTRY_MANAGEMENT', 'create') %}
                        <button type="submit" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                            Salva Paese
                        </button>
                        {% else %}
                        <div class="text-sm text-gray-500 dark:text-neutral-400 py-2 px-3">
                            Non hai i permessi per creare paesi
                        </div>
                        {% endif %}
                    {% else %}
                        <!-- Edit Country - Show Update and Delete Buttons -->
                        {% if user.hasPermission('COUNTRY_MANAGEMENT', 'delete') %}
                        <button type="button" data-countryid="{{ curCountry.id }}" id="delete-country-btn" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-red-600 hover:bg-red-50 focus:outline-none focus:bg-red-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-red-500 dark:hover:bg-red-800/30 dark:focus:bg-red-800/30">
                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M3 6h18"/>
                                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
                                <line x1="10" x2="10" y1="11" y2="17"/>
                                <line x1="14" x2="14" y1="11" y2="17"/>
                            </svg>
                            Elimina
                        </button>
                        {% endif %}
                        {% if user.hasPermission('COUNTRY_MANAGEMENT', 'edit') %}
                        <button type="submit" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                            Modifica Paese
                        </button>
                        {% else %}
                        <div class="text-sm text-gray-500 dark:text-neutral-400 py-2 px-3">
                            Non hai i permessi per modificare paesi
                        </div>
                        {% endif %}
                    {% endif %}
                </div>
            </form>
        </div>
        <!-- End Form Content -->
    </div>
    <!-- End Card -->
</div>
<!-- End Container -->

{% endblock %}

{% block pagescript %}

    <!-- Reload -->
    <script class="reload-script-on-load">
        addRoute('BE_COUNTRY', '{{ routes("BE_COUNTRY") }}');
        addRoute('BE_COUNTRY_SAVE', '{{ routes("BE_COUNTRY_SAVE") }}');
        addRoute('BE_COUNTRY_OPERATE', '{{ routes("BE_COUNTRY_OPERATE") }}');
    </script>

    <!-- Page Scripts -->
    <script src="{{ contextPath }}/js/pages/country-form.js?{{ buildNumber }}"></script>
{% endblock %}
