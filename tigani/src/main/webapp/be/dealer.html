{% extends "be/include/base.html" %}

{% set page = 'DEALER' %}
{% set title = curDealer is empty ? 'Nuovo rivenditore' : 'Modifica rivenditore' %}

{% block extrahead %}

<title>{{ title }} </title>

<!-- Specific script -->
{% include "be/include/snippets/plugins/filepond.html" %}
{% include "be/include/snippets/plugins/select2.html" %}
{% include "be/include/snippets/plugins/daterangepicker.html" %}
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}
<!-- specific script-->

<!-- Page script -->
<script src="{{ contextPath }}/be/js/pages/dealer-form.js?{{ buildNumber }}"></script>
<!-- /page script -->

{% endblock %}

{% block content %}
<script class="reload-script-on-load">
    addRoute('BE_IMAGE', '{{ routes("BE_IMAGE") }}');
    addRoute('BE_DEALER', '{{ routes("BE_DEALER") }}');
    addVariables('imageId', '{{ curDealer.imageId }}');
</script>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Checkbox selection -->
    <div class="card">
        <div class="card-header">
            {% if curDealer is empty %}
            <h5 class="mb-0">Inserisci rivenditore</h5>
            {% else %}
            <h5 class="mb-0">Modifica rivenditore</h5>
            {% endif %}
        </div>

        <div class="card-body">
            {% set postUrl = routes('BE_DEALER_SAVE') %}
            {% if curDealer.id is not empty %}                
            {% set postUrl = routes('BE_DEALER_SAVE') + '?dealerId=' + curDealer.id %}
            {% endif %}

            <form id="dealer-edit" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Immagine profilo:</label>
                    <div class="col-lg-9">                              
                        <input id="logo" name="logo" type="file" class="filepond filepond-avatar">                        
                        <div class="form-text text-muted">Formato immagine .jpg, .png o .svg.</div>                                                        
                    </div>

                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Nome: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="name" type="text" class="form-control" placeholder="Nome" value="{{ curDealer.name }}" required>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Ragione Sociale:</label>
                    <div class="col-lg-9">
                        <input name="companyName" type="text" class="form-control" placeholder="Ragione Sociale" value="{{ curDealer.companyName }}">
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Codice Rivenditore:</label>
                    <div class="col-lg-9">
                        <input name="dealerCode" type="text" class="form-control" placeholder="Codice Rivenditore" value="{{ curDealer.dealerCode }}">
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Email:</label>
                    <div class="col-lg-9">
                        <input name="email" type="email" class="form-control" placeholder="Email" value="{{ curDealer.email }}">
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Telefono:</label>
                    <div class="col-lg-9">
                        <input name="phoneNumber" type="text" class="form-control" value="{{ curDealer.phoneNumber }}">
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Indirizzo:</label>
                    <div class="col-lg-9">
                        <input name="address" type="text" class="form-control" placeholder="Indirizzo" value="{{ curDealer.address }}">
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Partita IVA:</label>
                    <div class="col-lg-9">
                        <input name="vatNumber" type="text" class="form-control" placeholder="Partita IVA" value="{{ curDealer.vatNumber }}">
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Note:</label>
                    <div class="col-lg-9">
                        <textarea name="notes" type="text" class="form-control">{{ curDealer.notes }}</textarea>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Stato:</label>
                    <div class="col-lg-9">
                        <select name="status" class="form-control" data-minimum-results-for-search="Infinity">
                            <option value="ACTIVE" {{ (curDealer is not empty and curDealer.status is not empty and curDealer.status equals 'ACTIVE') ? 'selected' : '' }}>Attivo</option>
                            <option value="INACTIVE" {{ (curDealer is not empty and curDealer.status is not empty and curDealer.status equals 'INACTIVE') ? 'selected' : '' }}>Inattivo</option>
                        </select>
                    </div>
                </div>

                <div class="text-end">                    
                    <button type="submit" class="btn btn-primary">
                        <i class="ph-check me-2"></i>
                        Salva
                    </button>                    
                </div>
            </form>
        </div>
    </div>
    <!-- /checkbox selection -->
</div>
<!-- /content area -->

{% endblock %}
