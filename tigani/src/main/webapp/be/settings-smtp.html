{% extends "be/include/base.html" %}

{% set area = 'SETTINGS' %}
{% set page = 'SETTINGS_SMTP' %}
{% set title = smtp is empty ? 'Nuova configurazione SMTP' : 'Modifica configurazione SMTP' %}

{% block extrahead %}

    <title>{{ title }} </title>
    
    <!-- Specific script -->
    {% include "be/include/snippets/plugins/select2.html" %}
    {% include "be/include/snippets/plugins/daterangepicker.html" %}
    {% include "be/include/snippets/plugins/validate.html" %}
    {% include "be/include/snippets/plugins/maxlength.html" %}
    <!-- specific script-->
    
    <!-- Page script -->
    <script src="{{ contextPath }}/be/js/pages/settings-smtp.js?{{ buildNumber }}"></script>
    <!-- /page script -->
    
{% endblock %}

{% block content %}

<!-- Content area -->
<div class="content container pt-0">

    <!-- Checkbox selection -->
    <div class="card">
        <div class="card-header">
            {% if smtp is empty %}
            <h5 class="mb-0">Inserisci Smtp</h5>
            {% else %}
            <h5 class="mb-0">Modifica Smtp</h5>
            {% endif %}
        </div>

        <div class="card-body">
            {% set postUrl = routes('BE_SETTINGS_SMTP_SAVE') %}
            {% if smtp.id is not empty %}                
            {% set postUrl = routes('BE_SETTINGS_SMTP_SAVE') + '?smtpId=' + smtp.id %}
            {% endif %}

            <form id="smtp-edit" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Hostname: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="hostname" type="text" class="form-control" placeholder="Hostname" value="{{ smtp.hostname }}" required {{ disabled }}>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Port: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="port" type="number" class="form-control" placeholder="Port" value="{{ smtp.port }}" required {{ disabled }}>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Authentication:</label>
                    <div class="col-lg-9">
                        <input name="authentication" type="checkbox" class="form-control form-check-input" {{ smtp.authentication ? 'checked' : '' }} {{ disabled }}>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Username: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="username" type="text" class="form-control" value="{{ smtp.username }}" required {{ disabled }}>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Password: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="password" type="password" class="form-control" value="{{ smtp.password }}" required {{ disabled }}>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Encryption:</label>
                    <div class="col-lg-9">
                        <input name="encryption" type="checkbox" class="form-control form-check-input" {{ smtp.encryption ? 'checked' : '' }} {{ disabled }}>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">TLS:</label>
                    <div class="col-lg-9">
                        <input name="startTls" type="checkbox" class="form-control form-check-input" {{ smtp.startTls ? 'checked' : '' }} {{ disabled }}>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Sender: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <input name="sender" type="text" class="form-control" placeholder="Sender" value="{{ smtp.sender }}" required {{ disabled }}>
                    </div>
                </div>

                <div class="text-end">
                    {% if smtp is not empty %}
                    <button type="submit" class="btn btn-primary">Modifica Smtp <i class="ph-paper-plane-tilt ms-2"></i></button>
                    {% else %}
                    <button type="submit" class="btn btn-primary">Inserisci Smtp <i class="ph-paper-plane-tilt ms-2"></i></button>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>
    <!-- /checkbox selection -->
</div>
<!-- /content area -->

{% endblock %}