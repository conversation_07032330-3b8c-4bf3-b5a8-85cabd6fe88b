{% extends "be/include/base.html" %}

{% set area = 'MAILTEMPLATE' %}
{% set title = mailtemplate is empty ? 'Nuovo template ' : 'Modifica template' %}
{% set postUrl = routes('BE_MAILTEMPLATE') %}
{% if mailtemplate.id is not empty %}
{% set postUrl = routes('BE_MAILTEMPLATE') + '?mailtemplateId=' + mailtemplate.id %}
{% endif %}

{% block extrahead %}
<title>{{ title }}</title>

<!-- Page script -->
{% include "be/include/snippets/plugins/ckeditor.html" %}
{% include "be/include/snippets/plugins/select2.html" %}
{% include "be/include/snippets/plugins/daterangepicker.html" %}
{% include "be/include/snippets/plugins/filepond.html" %}
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}
<script src="{{ contextPath }}/be/js/pages/mailtemplate.js?{{ buildNumber }}"></script>
<!-- /page script -->
{% endblock %}

{% block content %}
<script class="reload-script-on-load">
addRoute('BE_IMAGE', '{{ routes("BE_IMAGE") }}');
addRoute('BE_MAILTEMPLATE', '{{ routes("BE_MAILTEMPLATE") }}');
addRoute('BE_IMAGE_SAVE', '{{ routes("BE_IMAGE_SAVE") }}');
addVariables('mailtemplateId', '{{ mailtemplate.id }}');
{% if parentId is not empty %}
addVariables('parentId', '{{ parentId }}');
addVariables('parentIdLanguage', '{{ parentIdLanguage }}');
{% endif %}
{% if requiredLanguage is not empty %}
addVariables('language', '{{ requiredLanguage }}');
{% endif %}
</script>
<div class="row justify-content-center">
    <div class="col-xxl-10">
        {% set postUrl = routes('BE_MAILTEMPLATE_SAVE') %}
        {% if mailtemplate.id is not empty %}
        {% set postUrl = routes('BE_MAILTEMPLATE_SAVE') + '?mailtemplateId=' + mailtemplate.id %}
        {% endif %}
        <form id="mailtemplate" class="form-validate-jquery" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">

            <div class="card">
                <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
                    <h5 class="py-sm-3 mb-sm-auto">{{ title }}</h5>
                    <div class="ms-sm-auto my-sm-auto">

                    </div>
                </div>

                <div class="card-body">
                    <fieldset>

                        <legend class="fs-base fw-bold border-bottom pb-2 mb-3">Dati principali</legend>

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Lingua: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <select name="language" class="form-control select-language" data-minimum-results-for-search="Infinity" {{ mailtemplate.id is empty and requiredLanguage is empty ? '' : 'disabled' }}>
                                    {% for language in availableLanguages %}
                                    <option value="{{ language }}" {{ (mailtemplate.id is not empty and mailtemplate.language == language) or (requiredLanguage is not empty and requiredLanguage == language) ? 'selected' : '' }}>{{ language }}</option>
                                    {% endfor %}
                                </select>
                                <div class="form-text text-muted">La galleria sarà pubblicato nella rispettiva versione del sito sulla base della lingua scelta. Successivamente potrai creare delle copie tradotte della stessa galleria per le altre lingue.</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Chiave: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <input id="key" name="key" type="text" class="form-control maxlength" placeholder="Chiave" value="{{ mailtemplate.key }}" {{ disabled }} maxlength="300" required>
                                <div class="form-text text-muted">Valore univoco per identificare il template.</div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Oggetto: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <input id="object" name="object" type="text" class="form-control maxlength" placeholder="Oggetto" value="{{ mailtemplate.object }}" {{ disabled }} maxlength="300" required>
                                <div class="form-text text-muted">L'oggetto che verrà inserito nella mail.</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Corpo del messaggio: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <textarea id="description" name="description" rows="20" cols="20" class="form-control" placeholder="Contenuto dell'email" {{ disabled }} required>{{ mailtemplate.description }}</textarea>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Mittente: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <input id="from" name="from" type="email" class="form-control maxlength" placeholder="Mittente" value="{{ mailtemplate.from }}" {{ disabled }} maxlength="300" required>
                                <div class="form-text text-muted">L'email che verrà visualizzata come mittente.</div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Destinatari:</label>
                            <div class="col-lg-10">
                                <div class="w-100">
                                    <select id="to" name="to" class="form-control select" multiple="multiple" data-tags="true" data-maximum-selection-length="10" data-placeholder="Destinatari" {{ disabled }}>
                                        {% if mailtemplate.to is not empty %}
                                            {% for mail in mailtemplate.to %}
                                                <option value="{{ mail }}" selected>{{ mail }}</option>
                                            {% endfor %}
                                        {% endif %}
                                    </select>
                                    <div class="form-text text-muted">Inserisci uno alla volta i destinatari che riceveranno la mail.</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Destinatari (CC):</label>
                            <div class="col-lg-10">
                                <div class="w-100">
                                    <select id="cc" name="cc" class="form-control select" multiple="multiple" data-tags="true" data-maximum-selection-length="10" data-placeholder="Destinatari (CC)" {{ disabled }}>
                                        {% if mailtemplate.cc is not empty %}
                                            {% for mail in mailtemplate.cc %}
                                                <option value="{{ mail }}" selected>{{ mail }}</option>
                                            {% endfor %}
                                        {% endif %}
                                    </select>
                                    <div class="form-text text-muted">Inserisci uno alla volta i destinatari (inseriti come cc) che riceveranno la mail.</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Destinatari (CCN):</label>
                            <div class="col-lg-10">
                                <div class="w-100">
                                    <select id="allCcn" name="ccn" class="form-control select" multiple="multiple" data-tags="true" data-maximum-selection-length="10" data-placeholder="Destinatari (CCN)" {{ disabled }}>
                                        {% if mailtemplate.ccn is not empty %}
                                            {% for mail in mailtemplate.ccn %}
                                                <option value="{{ mail }}" selected>{{ mail }}</option>
                                            {% endfor %}
                                        {% endif %}
                                    </select>
                                    <div class="form-text text-muted">Inserisci uno alla volta i destinatari (inseriti come ccn) che riceveranno la mail.</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">SMTP: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <div class="w-100">
                                    <select id="smtpId" name="smtpId" class="form-control select" data-placeholder="Server SMTP" required {{ disabled }}>
                                        <option value=""></option>
                                        {% set smtps = lookup('Smtp', checkPublished=false, language='false') %}
                                        {% if smtps is not empty %}
                                            {% for smtp in smtps %}
                                                <option value="{{ smtp.id }}" {{ mailtemplate.smtpId equals smtp.id ? 'selected' : '' }}>{{ smtp.hostname }} : {{ smtp.port }}</option>
                                            {% endfor %}
                                        {% endif %}
                                    </select>
                                    <div class="form-text text-muted">Seleziona l'SMTP che verrà utilizzato per mandare le mail.</div>
                                </div>
                            </div>
                        </div>

                    </fieldset>
                </div>
                <div class="card-footer d-flex justify-content-between align-items-center py-sm-2">
                    <div class="btn-group w-auto mt-sm-0">
                        <button class="btn btn-danger w-100 w-sm-auto"><i class="ph-trash me-sm-2"></i><span class="d-none d-sm-block">Archivia</span></button>
                        <button class="btn btn-danger dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false"></button>
                        <div class="dropdown-menu dropdown-menu-end" style="">
                            <a href="#" class="dropdown-item"><i class="ph-x me-2"></i> Elimina</a>                            
                        </div>
                    </div>
                    <div class="hstack gap-2 mt-0">
                        <a href="{{ routes('BE_MAILTEMPLATE_COLLECTION') }}" class="btn btn-light w-auto btn-cancel">
                            <i class="ph-arrow-u-up-left me-sm-2"></i>
                            <span class="d-none d-sm-block">Annulla</span>
                        </a>
                        <button type="submit" class="btn btn-primary w-100 w-auto">
                            <i class="ph-check me-2"></i>
                            Salva
                        </button>
                    </div>
                </div>
            </div>

        </form>
    </div>
</div>

{% endblock %}

{% block sidebar %}
    {% include "be/include/snippets/sidebar/sidebar-mailtemplate.html" %}
{% endblock %}